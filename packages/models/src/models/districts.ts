import { districts } from '@muraadso/db/schema/districts';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import { z } from 'zod/v4';
import type { City } from './cities';

const refinedSchema = {
  name: z.string().min(1, 'Name is required'),
  cityId: z.string().uuid('Valid city is required'),
};

export const selectDistrictSchema = createSelectSchema(
  districts,
  refinedSchema,
);
export const createDistrictSchema = createInsertSchema(
  districts,
  refinedSchema,
);
export const updateDistrictSchema = createUpdateSchema(
  districts,
  refinedSchema,
);

export type District = z.infer<typeof selectDistrictSchema> & {
  city?: City;
};
export type CreateDistrict = z.infer<typeof createDistrictSchema>;
export type UpdateDistrict = z.infer<typeof updateDistrictSchema>;
