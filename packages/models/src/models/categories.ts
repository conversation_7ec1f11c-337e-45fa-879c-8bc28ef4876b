import { categories } from '@muraadso/db/schema/categories';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import type { z } from 'zod/v4';
import { slugSchema } from '../utils';

const refinedSchema = {
  name: (s: z.ZodString) => s.nonempty('Name is required').max(64),
  slug: slugSchema,
  description: (s: z.ZodString) => s.max(500),
  parentId: (s: z.ZodUUID) => s.optional(),
};

export const selectCategorySchema = createSelectSchema(
  categories,
  refinedSchema,
);
export const createCategorySchema = createInsertSchema(
  categories,
  refinedSchema,
);
export const updateCategorySchema = createUpdateSchema(
  categories,
  refinedSchema,
);

export type Category = z.infer<typeof selectCategorySchema>;
export type CreateCategory = z.infer<typeof createCategorySchema>;
export type UpdateCategory = z.infer<typeof updateCategorySchema>;
