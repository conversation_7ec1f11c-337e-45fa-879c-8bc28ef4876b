import { countries } from '@muraadso/db/schema/countries';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import { z } from 'zod/v4';
import type { City } from './cities';

const refinedSchema = {
  name: z.string().min(1, 'Name is required'),
  code: z.string().min(1, 'Code is required'),
  emoji: z.string().min(1, 'Emoji is required'),
};

export const selectCountrySchema = createSelectSchema(countries, refinedSchema);
export const createCountrySchema = createInsertSchema(countries, refinedSchema);
export const updateCountrySchema = createUpdateSchema(countries, refinedSchema);

export type Country = z.infer<typeof selectCountrySchema> & {
  cities?: City[];
};
export type CreateCountry = z.infer<typeof createCountrySchema>;
export type UpdateCountry = z.infer<typeof updateCountrySchema>;
