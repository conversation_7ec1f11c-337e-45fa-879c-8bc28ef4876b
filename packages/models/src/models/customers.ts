import { customers } from '@muraadso/db/schema/customers';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import type { z } from 'zod/v4';
import type { User } from './users';

const refinedSchema = {
  name: (s: z.ZodString) => s.nonempty('Name is required').max(100),
  phone: (s: z.ZodString) =>
    s.min(10, 'Invalid phone number').max(15, 'Invalid phone number'),
};

export const selectCustomerSchema = createSelectSchema(
  customers,
  refinedSchema,
);
export const createCustomerSchema = createInsertSchema(
  customers,
  refinedSchema,
);
export const updateCustomerSchema = createUpdateSchema(
  customers,
  refinedSchema,
);

export type Customer = z.infer<typeof selectCustomerSchema> & {
  user?: User | null;
};

export type CreateCustomer = z.infer<typeof createCustomerSchema>;
export type UpdateCustomer = z.infer<typeof updateCustomerSchema>;
