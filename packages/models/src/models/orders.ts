import {
  orderItems,
  orderQchStatusEnum,
  orderServiceTypeEnum,
  orderStatusEnum,
  orders,
} from '@muraadso/db/schema/orders';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import { array, z } from 'zod/v4';
import { selectCategorySchema } from './categories';
import { createCustomerSchema, selectCustomerSchema } from './customers';
import { type Location, selectLocationSchema } from './locations';
import { type Product, selectProductSchema } from './products';
import { type Variant, selectVariantSchema } from './variants';

const orderItemRefinements = {
  unitPrice: (s: z.ZodNumber) => s.positive(),
  quantity: (s: z.ZodNumber) => s.int().positive(),
  productSnapshot: selectProductSchema
    .extend({ category: selectCategorySchema })
    .partial(),
  variantSnapshot: selectVariantSchema.partial(),
};

export const selectOrderItemSchema = createSelectSchema(
  orderItems,
  orderItemRefinements,
);
export const createOrderItemSchema = createInsertSchema(orderItems, {
  ...orderItemRefinements,
  productId: z.uuid('Product ID must be a valid UUID'),
  variantId: z.uuid('Variant ID must be a valid UUID'),
  productSnapshot: selectProductSchema.partial().optional(),
  variantSnapshot: selectVariantSchema.partial().optional(),
}).partial({ orderId: true });
export const updateOrderItemSchema = createUpdateSchema(
  orderItems,
  orderItemRefinements,
);

export type OrderItem = z.infer<typeof selectOrderItemSchema> & {
  product?: Product;
  variant?: Variant;
};
export type CreateOrderItem = z.infer<typeof createOrderItemSchema>;
export type UpdateOrderItem = z.infer<typeof updateOrderItemSchema>;

const orderRefinements = {
  // we make customerId optional here because we also expect customer data
  // to be passed if the customer does not already exist
  customerId: (s: z.ZodUUID) => s.optional(),
  trackingId: (s: z.ZodString) => s.min(1),
};

export const selectOrderSchema = createSelectSchema(
  orders,
  orderRefinements,
).extend({
  customer: selectCustomerSchema,
  location: selectLocationSchema.optional(),
  items: array(selectOrderItemSchema),
});

export const createOrderSchema = createInsertSchema(orders, {
  ...orderRefinements,
  totalAmount: (s: z.ZodNumber) => s.optional(),
})
  .extend({
    customer: createCustomerSchema.optional(),
    items: array(createOrderItemSchema).min(1),
  })
  .refine(
    (order) => {
      const hasCustomer = !!order.customer;
      const hasCustomerId = !!order.customerId;
      return (hasCustomerId || hasCustomer) && !(hasCustomerId && hasCustomer);
    },
    {
      message:
        'Provide either customerId or full customer details, but not both',
      path: ['customerId'],
    },
  )
  .partial({ trackingId: true });

export const updateOrderSchema = createUpdateSchema(orders, orderRefinements);

export type Order = z.infer<typeof selectOrderSchema>;
export type CreateOrder = z.infer<typeof createOrderSchema>;
export type UpdateOrder = z.infer<typeof updateOrderSchema>;

export const serviceTypes = orderServiceTypeEnum.enumValues;
export type ServiceType = (typeof serviceTypes)[number];

export const orderStatus = orderStatusEnum.enumValues;
export type OrderStatus = (typeof orderStatus)[number];

export const qchStatus = orderQchStatusEnum.enumValues;
export type QchStatus = (typeof qchStatus)[number];

export const orderStatusUpdateSchema = z.object({
  orderId: z.uuid('Order ID must be a valid UUID'),
  status: z.enum(orderStatus).optional(),
  qchStatus: z.enum(qchStatus).optional(),
  comment: z.string().max(1000).optional(),
});

export type OrderStatusUpdate = z.infer<typeof orderStatusUpdateSchema>;
