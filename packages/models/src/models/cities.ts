import { cities } from '@muraadso/db/schema/cities';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import { z } from 'zod/v4';
import type { Country } from './countries';
import type { District } from './districts';

const refinedSchema = {
  name: z.string().min(1, 'Name is required'),
  countryId: z.uuid('Valid country is required'),
};

export const selectCitySchema = createSelectSchema(cities, refinedSchema);
export const createCitySchema = createInsertSchema(cities, refinedSchema);
export const updateCitySchema = createUpdateSchema(cities, refinedSchema);

export type City = z.infer<typeof selectCitySchema> & {
  country?: Country;
  districts?: District[];
};
export type CreateCity = z.infer<typeof createCitySchema>;
export type UpdateCity = z.infer<typeof updateCitySchema>;
