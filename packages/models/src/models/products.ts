import { products } from '@muraadso/db/schema/products';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import { z } from 'zod/v4';
import { slugSchema } from '../utils';
import type { Brand } from './brands';
import type { Category } from './categories';
import type { Image } from './images';
import { type OptionValue, createOptionValueSchema } from './option-values';
import { type Option, createOptionSchema } from './options';
import { type Variant, createVariantSchema } from './variants';

const refinedSchema = {
  name: (s: z.ZodString) => s.nonempty('Name is required').max(64),
  slug: slugSchema,
  description: (s: z.ZodString) => s.max(500),
};

export const selectProductSchema = createSelectSchema(products, refinedSchema);
export const createProductSchema = createInsertSchema(
  products,
  refinedSchema,
).extend({
  variantsEnabled: z.boolean(),
  options: createOptionSchema
    .partial({ productId: true })
    .extend({
      values: createOptionValueSchema
        .pick({ value: true })
        .array()
        .nonempty('At least one Option value is required'),
    })
    .array(),
  variants: createVariantSchema.array(),
});
export const updateProductSchema = createUpdateSchema(products, refinedSchema);

export type Product = z.infer<typeof selectProductSchema> & {
  images: Image[];
  category: Category;
  brand: Brand;
  options: (Option & {
    values: OptionValue[];
  })[];
  variants: Variant[];
};
export type CreateProduct = z.infer<typeof createProductSchema>;
export type UpdateProduct = z.infer<typeof updateProductSchema>;
