import { inventory } from '@muraadso/db/schema/inventory';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import { z } from 'zod/v4';

const refinedSchema = {
  quantity: z.number().int().min(0).max(999),
};

export const selectInventorySchema = createSelectSchema(
  inventory,
  refinedSchema,
);
export const createInventorySchema = createInsertSchema(
  inventory,
  refinedSchema,
);
export const updateInventorySchema = createUpdateSchema(
  inventory,
  refinedSchema,
);

export type Inventory = z.infer<typeof selectInventorySchema>;
export type InventoryMatrixItem = {
  productId: string;
  productName: string;
  variantId: string;
  variantName: string;
  variantSKU: string;
  locationId: string;
  locationName: string;
  inventoryId?: string;
  quantity: number;
  updatedAt?: string;
};
export type CreateInventory = z.infer<typeof createInventorySchema>;
export type UpdateInventory = z.infer<typeof updateInventorySchema>;
