import { optionValues } from '@muraadso/db/schema/option-values';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import type { z } from 'zod/v4';

const refinedSchema = {
  value: (s: z.ZodString) => s.nonempty('Value is required').max(64),
  optionId: (s: z.ZodUUID) => s.describe('Option ID must be a valid UUID'),
};

export const selectOptionValueSchema = createSelectSchema(
  optionValues,
  refinedSchema,
);
export const createOptionValueSchema = createInsertSchema(
  optionValues,
  refinedSchema,
);
export const updateOptionValueSchema = createUpdateSchema(
  optionValues,
  refinedSchema,
);

export type OptionValue = z.infer<typeof selectOptionValueSchema>;
export type CreateOptionValue = z.infer<typeof createOptionValueSchema>;
export type UpdateOptionValue = z.infer<typeof updateOptionValueSchema>;
