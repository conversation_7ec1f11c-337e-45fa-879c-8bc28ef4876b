import { orderChanges } from '@muraadso/db/schema/orders';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import type { z } from 'zod/v4';
import type { Order } from './orders';
import type { Sysop } from './sysops';

const refinedSchema = {
  comment: (s: z.ZodString) => s.max(1000).optional(),
};

export const selectOrderChangeSchema = createSelectSchema(
  orderChanges,
  refinedSchema,
);
export const createOrderChangeSchema = createInsertSchema(
  orderChanges,
  refinedSchema,
);
export const updateOrderChangeSchema = createUpdateSchema(
  orderChanges,
  refinedSchema,
);

export type OrderChange = z.infer<typeof selectOrderChangeSchema> & {
  order?: Order;
  sysop?: Sysop;
};
export type CreateOrderChange = z.infer<typeof createOrderChangeSchema>;
export type UpdateOrderChange = z.infer<typeof updateOrderChangeSchema>;
