import { options } from '@muraadso/db/schema/options';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import type { z } from 'zod/v4';
import { createOptionValueSchema } from './option-values';

const refinedSchema = {};

export const selectOptionSchema = createSelectSchema(options, refinedSchema);
export const createOptionSchema = createInsertSchema(options, refinedSchema)
  .partial({ productId: true })
  .extend({
    values: createOptionValueSchema
      .pick({ value: true })
      .array()
      .nonempty('At least one Option value is required'),
  });
export const updateOptionSchema = createUpdateSchema(options, refinedSchema)
  .partial({ productId: true })
  .extend({
    values: createOptionValueSchema
      .pick({ value: true })
      .array()
      .nonempty('At least one Option value is required'),
  });

export type Option = z.infer<typeof selectOptionSchema>;
export type CreateOption = z.infer<typeof createOptionSchema>;
export type UpdateOption = z.infer<typeof updateOptionSchema>;
