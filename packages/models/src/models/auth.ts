import { z } from 'zod/v4';

export const signInSchema = z.object({
  email: z.email({
    message: 'Invalid email address!',
  }),
  password: z.string({ message: 'Password is required!' }).nonempty({
    message: 'Password cannot be empty!',
  }),
});

export type SignIn = z.infer<typeof signInSchema>;

export const changePasswordSchema = z
  .object({
    password: z
      .string()
      .min(8, 'At least 8 characters')
      .regex(/[0-9]/, 'At least 1 number')
      .regex(/[a-z]/, 'At least 1 lowercase letter')
      .regex(/[A-Z]/, 'At least 1 uppercase letter'),
    confirm: z.string(),
  })
  .refine((data) => data.password === data.confirm, {
    message: "Passwords don't match",
    path: ['confirm'],
  });

export type ChangePassword = z.infer<typeof changePasswordSchema>;

export const inviteUserSchema = z.object({
  email: z.email({
    message: 'Invalid email address!',
  }),
  name: z.string().min(1, 'Name is required'),
  locationId: z.uuid().optional(),
  isSuperAdmin: z.boolean(),
});

export type InviteUser = z.infer<typeof inviteUserSchema>;

export const verifyTokenSchema = z.object({
  // either reset password token
  // or verify invitation token
  type: z.string(),
  token: z.string(),
});

export type VerifyToken = z.infer<typeof verifyTokenSchema>;
