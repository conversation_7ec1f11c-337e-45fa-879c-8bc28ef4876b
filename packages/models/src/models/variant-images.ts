import { variantImages } from '@muraadso/db/schema/variant-images';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import type { z } from 'zod/v4';

export const selectVariantImageSchema = createSelectSchema(variantImages, {});
export const createVariantImageSchema = createInsertSchema(variantImages, {});
export const updateVariantImageSchema = createUpdateSchema(variantImages, {});

export type VariantImage = z.infer<typeof selectVariantImageSchema>;
export type CreateVariantImage = z.infer<typeof createVariantImageSchema>;
export type UpdateVariantImage = z.infer<typeof updateVariantImageSchema>;
