import { locations } from '@muraadso/db/schema/locations';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import { z } from 'zod/v4';

const refinedSchema = {
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional().nullable(),
  address: z.string().min(1, 'Address is required'),
  city: z.string().min(1, 'City is required'),
  contactNumber: z.string().min(1, 'Contact number is required'),
};

export const selectLocationSchema = createSelectSchema(locations, refinedSchema);
export const createLocationSchema = createInsertSchema(
  locations,
  refinedSchema,
);
export const updateLocationSchema = createUpdateSchema(locations, refinedSchema);

export type Location = z.infer<typeof selectLocationSchema>;
export type CreateLocation = z.infer<typeof createLocationSchema>;
export type UpdateLocation = z.infer<typeof updateLocationSchema>;
