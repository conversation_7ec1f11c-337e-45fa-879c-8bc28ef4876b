import { sysops } from '@muraadso/db/schema/sysops';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import type { z } from 'zod/v4';
import type { Location } from './locations';
import type { User } from './users';

const refinedSchema = {
  name: (s: z.ZodString) => s.min(1, 'Name is required').max(100),
  locationId: (s: z.ZodUUID) => s.optional(),
};

export const selectSysopSchema = createSelectSchema(sysops, refinedSchema);
export const createSysopSchema = createInsertSchema(sysops, refinedSchema);
export const updateSysopSchema = createUpdateSchema(sysops, refinedSchema);

export type Sysop = z.infer<typeof selectSysopSchema> & {
  user: User;
  location?: Location | null;
};
export type CreateSysop = z.infer<typeof createSysopSchema>;
export type UpdateSysop = z.infer<typeof updateSysopSchema>;
