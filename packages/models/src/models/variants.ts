import { variants } from '@muraadso/db/schema/variants';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import { z } from 'zod/v4';
import type { Image } from './images';
import type { Inventory } from './inventory';
import type { OptionValue } from './option-values';
import type { VariantImage } from './variant-images';

const refinedSchema = {
  name: (s: z.ZodString) => s.nonempty('Name is required').max(64),
};

export const selectVariantSchema = createSelectSchema(variants, refinedSchema);
export const createVariantSchema = createInsertSchema(variants, refinedSchema)
  .extend({
    isDefault: z.boolean(),
    shouldCreate: z.boolean(),
    options: z.record(z.string(), z.string()),
    prices: z.object({
      tradeInPrice: z.number().transform((val) => Number(val)),
      consignmentPrice: z.number().transform((val) => Number(val)),
      buybackPrice: z.number().transform((val) => Number(val)),
      retailPrice: z.number().transform((val) => Number(val)),
    }),
    imageIndices: z.array(z.number()).optional(),
  })
  .partial({ productId: true });
export const updateVariantSchema = createUpdateSchema(variants, refinedSchema)
  .extend({
    options: z.record(z.string(), z.string()).optional(),
    prices: z
      .object({
        tradeInPrice: z.number().transform((val) => Number(val)),
        consignmentPrice: z.number().transform((val) => Number(val)),
        buybackPrice: z.number().transform((val) => Number(val)),
        retailPrice: z.number().transform((val) => Number(val)),
      })
      .optional(),
    imageIndices: z.array(z.number()).optional(),
  })
  .partial({ productId: true });

export type Variant = z.infer<typeof selectVariantSchema> & {
  images: Image[];
  optionValues: OptionValue[];
  prices: {
    tradeInPrice: number;
    consignmentPrice: number;
    buybackPrice: number;
    retailPrice: number;
  };
  inventory: Inventory[];
  variantImages: (VariantImage & { image: Image })[];
};
export type CreateVariant = z.infer<typeof createVariantSchema>;
export type UpdateVariant = z.infer<typeof updateVariantSchema>;
