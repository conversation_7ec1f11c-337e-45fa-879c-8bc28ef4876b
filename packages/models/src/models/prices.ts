import { prices } from '@muraadso/db/schema/prices';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import type { z } from 'zod/v4';

const refinedSchema = {};

export const selectPriceSchema = createSelectSchema(prices, refinedSchema);
export const createPriceSchema = createInsertSchema(prices, refinedSchema);
export const updatePriceSchema = createUpdateSchema(prices, refinedSchema);

export type Price = z.infer<typeof selectPriceSchema>;
export type CreatePrice = z.infer<typeof createPriceSchema>;
export type UpdatePrice = z.infer<typeof updatePriceSchema>;
