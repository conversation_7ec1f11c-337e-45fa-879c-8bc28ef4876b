import { users } from '@muraadso/db/schema/users';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import type { z } from 'zod/v4';

const refinedSchema = {};

export const selectUserSchema = createSelectSchema(users, refinedSchema);
export const createUserSchema = createInsertSchema(users, refinedSchema);
export const updateUserSchema = createUpdateSchema(users, refinedSchema);

export type User = z.infer<typeof selectUserSchema>;
export type CreateUser = z.infer<typeof createUserSchema>;
export type UpdateUser = z.infer<typeof updateUserSchema>;

export type SafeUser = Pick<
  User,
  | 'id'
  | 'createdAt'
  | 'updatedAt'
  | 'email'
  | 'emailVerifiedAt'
  | 'phone'
  | 'phoneVerifiedAt'
  | 'invitedAt'
  | 'lastSignInAt'
  | 'isAnonymous'
  | 'isBanned'
  | 'isSysop'
>;
