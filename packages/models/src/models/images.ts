import { images } from '@muraadso/db/schema/images';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import type { z } from 'zod/v4';

export const selectImageSchema = createSelectSchema(images, {});
export const createImageSchema = createInsertSchema(images, {});
export const updateImageSchema = createUpdateSchema(images, {});

export type Image = z.infer<typeof selectImageSchema>;
export type CreateImage = z.infer<typeof createImageSchema>;
export type UpdateImage = z.infer<typeof updateImageSchema>;
