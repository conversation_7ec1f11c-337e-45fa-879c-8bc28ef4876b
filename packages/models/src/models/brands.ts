import { brands } from '@muraadso/db/schema/brands';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import { z } from 'zod/v4';
import { slugSchema } from '../utils';
import type { Category } from './categories';

const refinedSchema = {
  name: (s: z.ZodString) => s.nonempty('Name is required').max(64),
  slug: slugSchema,
  description: (s: z.ZodString) => s.max(500),
};

export const selectBrandSchema = createSelectSchema(brands, refinedSchema);
export const createBrandSchema = createInsertSchema(
  brands,
  refinedSchema,
).extend({
  categoryIds: z.array(z.uuid()).optional(),
});
export const updateBrandSchema = createUpdateSchema(
  brands,
  refinedSchema,
).extend({
  categoryIds: z.array(z.uuid()).optional(),
});

export type Brand = z.infer<typeof selectBrandSchema> & {
  categories?: Category[];
};
export type CreateBrand = z.infer<typeof createBrandSchema>;
export type UpdateBrand = z.infer<typeof updateBrandSchema>;
