import { sessions } from '@muraadso/db/schema/sessions';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import type { z } from 'zod/v4';

const refinedSchema = {};

export const selectSessionSchema = createSelectSchema(sessions, refinedSchema);
export const createSessionSchema = createInsertSchema(sessions, refinedSchema);
export const updateSessionSchema = createUpdateSchema(sessions, refinedSchema);

export type Session = z.infer<typeof selectSessionSchema>;
export type CreateSession = z.infer<typeof createSessionSchema>;
export type UpdateSession = z.infer<typeof updateSessionSchema>;
