{"name": "@muraadso/ui", "exports": {".": "./src/index.ts", "./blocks": "./src/blocks/index.ts", "./hooks": "./src/hooks/index.ts", "./utils": "./src/utils/index.ts"}, "scripts": {"storybook": "storybook dev -p 6006", "lint": "biome lint \"**/*.ts*\""}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@muraadso/icons": "workspace:*", "@tanstack/react-form": "^1.12.0", "@tanstack/react-table": "^8.21.3", "cmdk": "^1.1.1", "cva": "^1.0.0-beta.4", "input-otp": "^1.4.2", "motion": "^12.16.0", "radix-ui": "^1.4.2", "react-day-picker": "^9.8.1", "react-hook-form": "^7.57.0", "react-use-measure": "^2.1.7", "recharts": "^3.1.0", "sonner": "^2.0.5", "vaul": "^1.1.2"}, "devDependencies": {"@muraadso/tsconfig": "workspace:*", "@muraadso/ui-preset": "workspace:*", "@storybook/addon-a11y": "^8.6.12", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-links": "^8.6.12", "@storybook/addon-themes": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/react-vite": "^8.6.12", "@tailwindcss/postcss": "^4.1.4", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "clsx": "^2.1.1", "postcss": "^8.5.1", "storybook": "^8.6.12", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.3.0", "typescript": "^5", "vite": "^6.1.0", "vitest": "catalog:", "zod": "catalog:"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}}