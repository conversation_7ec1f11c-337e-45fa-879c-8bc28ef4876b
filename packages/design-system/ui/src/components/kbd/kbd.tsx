import React, { type ComponentPropsWithoutRef } from 'react';

import { cn } from '../../utils';

const Kbd = ({
  children,
  className,
  ...props
}: ComponentPropsWithoutRef<'kbd'>) => {
  return (
    <kbd
      className={cn(
        'inline-flex h-5 w-fit min-w-[20px] items-center justify-center rounded-md border border-border bg-accent px-1 font-display font-medium text-muted-foreground text-xs',
        className,
      )}
      {...props}
    >
      {children}
    </kbd>
  );
};

export { Kbd };
