import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import React from 'react';
import type { Textarea } from '../textarea';
import { Tooltip, TooltipProvider } from './tooltip';

const meta: Meta<typeof Textarea> = {
  title: 'Components/Tooltip',
  component: Tooltip,
  parameters: {
    layout: 'centered',
  },
};

export default meta;

type Story = StoryObj<typeof Tooltip>;

export const Default: Story = {
  render: () => (
    <TooltipProvider>
      <Tooltip>
        <Tooltip>
          <Tooltip.Trigger>
            <button type="button">Hover me</button>
          </Tooltip.Trigger>
          <Tooltip.Content>Tooltip content</Tooltip.Content>
        </Tooltip>
      </Tooltip>
    </TooltipProvider>
  ),
};
