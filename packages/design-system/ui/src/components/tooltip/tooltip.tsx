'use client';

import { Tooltip as TooltipPrimitive } from 'radix-ui';
// biome-ignore lint/style/useImportType: <explanation>
import React from 'react';
import { cn } from '../../utils';

const Root = TooltipPrimitive.Root;

const TooltipProvider = TooltipPrimitive.Provider;

const Trigger = TooltipPrimitive.Trigger;

const Content: React.FC<
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>
> = ({ className, sideOffset = 4, ...props }) => (
  <TooltipPrimitive.Content
    sideOffset={sideOffset}
    className={cn(
      'fade-in-0 zoom-in-in-95 data-[state=closed]:fade-out-0 data-[state=closed]:zoom-in-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 animate-in overflow-hidden rounded-md border border-border bg-popover px-3 py-1.5 text-popover-foreground text-sm shadow-md invert data-[state=closed]:animate-out',
      className,
    )}
    {...props}
  />
);

const Tooltip = Object.assign(Root, {
  Trigger,
  Content,
});

export { Tooltip, TooltipProvider };
