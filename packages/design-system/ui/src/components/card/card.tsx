import React, { type ComponentProps } from 'react';

import { cn } from '../../utils';

const Root = ({ className, ...props }: ComponentProps<'div'>) => {
  return (
    <div
      data-slot="card"
      className={cn(
        'flex flex-col gap-5 rounded-xl border border-border bg-card py-5 text-card-foreground shadow-xs',
        className,
      )}
      {...props}
    />
  );
};

const Header = ({ className, ...props }: ComponentProps<'div'>) => {
  return (
    <div
      data-slot="card-header"
      className={cn(
        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-5 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-5',
        className,
      )}
      {...props}
    />
  );
};

const Title = ({ className, ...props }: ComponentProps<'div'>) => {
  return (
    <div
      data-slot="card-title"
      className={cn('font-semibold leading-none', className)}
      {...props}
    />
  );
};

const Description = ({ className, ...props }: ComponentProps<'div'>) => {
  return (
    <div
      data-slot="card-description"
      className={cn('text-muted-foreground text-sm', className)}
      {...props}
    />
  );
};

const Action = ({ className, ...props }: ComponentProps<'div'>) => {
  return (
    <div
      data-slot="card-action"
      className={cn(
        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',
        className,
      )}
      {...props}
    />
  );
};

const Content = ({ className, ...props }: ComponentProps<'div'>) => {
  return (
    <div
      data-slot="card-content"
      className={cn('px-5', className)}
      {...props}
    />
  );
};

const Footer = ({ className, ...props }: ComponentProps<'div'>) => {
  return (
    <div
      data-slot="card-footer"
      className={cn('flex items-center px-5 [.border-t]:pt-5', className)}
      {...props}
    />
  );
};

const Card = Object.assign(Root, {
  Content,
  Header,
  Title,
  Description,
  Action,
  Footer,
});

export { Card };
