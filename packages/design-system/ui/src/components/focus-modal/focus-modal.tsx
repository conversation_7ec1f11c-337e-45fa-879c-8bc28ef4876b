'use client';

import { CloseIcon } from '@muraadso/icons';
import { Dialog as DialogPrimitive } from 'radix-ui';
import React, { type ComponentPropsWithoutRef } from 'react';

import { cn } from '../../utils';
import { Button } from '../button';
import { Kbd } from '../kbd';

const Root = (props: ComponentPropsWithoutRef<typeof DialogPrimitive.Root>) => (
  <DialogPrimitive.Root {...props} />
);

const Trigger = (
  props: ComponentPropsWithoutRef<typeof DialogPrimitive.Trigger>,
) => <DialogPrimitive.Trigger {...props} />;

const Close = DialogPrimitive.Close;

const Portal = DialogPrimitive.Portal;

const Content = ({
  className,
  showOverlyBackground = true,
  showOverlayFilter = true,
  ...props
}: ComponentPropsWithoutRef<typeof DialogPrimitive.Content> & {
  showOverlyBackground?: boolean;
  showOverlayFilter?: boolean;
}) => {
  return (
    <>
      <DialogPrimitive.Overlay
        className={cn(
          'fixed inset-0 z-50',
          'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 overflow-y-auto data-[state=closed]:animate-out data-[state=open]:animate-in',
          showOverlyBackground ? 'bg-black/30' : 'bg-black/0',
          // showOverlayFilter ? 'backdrop-blur-sm' : 'backdrop-blur-none',
        )}
      />
      <DialogPrimitive.Content
        className={cn(
          'fixed inset-2 z-50 flex flex-col overflow-hidden rounded-xl border border-ring bg-background shadow-lg outline-none',
          'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-in-out-95 data-[state=open]:zoom-in-in-95 data-[state=closed]:animate-out data-[state=open]:animate-in',
          className,
        )}
        {...props}
      />
    </>
  );
};

const Header = ({
  children,
  className,
  ...props
}: ComponentPropsWithoutRef<'div'>) => {
  return (
    <div
      className={cn(
        'flex items-center justify-between gap-x-4 border-border border-b px-4 py-2',
        className,
      )}
      {...props}
    >
      <div className="flex items-center gap-x-2">
        <DialogPrimitive.Close asChild>
          <Button type="button" size="icon" variant="ghost">
            <CloseIcon className="size-4 text-muted-foreground" />
          </Button>
        </DialogPrimitive.Close>
        <Kbd>esc</Kbd>
      </div>
      <DialogPrimitive.Title className="sr-only">Dialog</DialogPrimitive.Title>
      {children}
    </div>
  );
};

const Footer = React.forwardRef<
  HTMLDivElement,
  React.ComponentPropsWithoutRef<'div'>
>(({ children, className, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        'flex items-center justify-end gap-x-2 border-border border-t p-4',
        className,
      )}
      {...props}
    >
      {children}
    </div>
  );
});

const Title = ({
  className,
  ...props
}: ComponentPropsWithoutRef<typeof DialogPrimitive.Title>) => (
  <DialogPrimitive.Title {...props} />
);

const Description = DialogPrimitive.Description;

const Body = ({ className, ...props }: ComponentPropsWithoutRef<'div'>) => (
  <div className={cn('flex-1', className)} {...props} />
);

const FocusModal = Object.assign(Root, {
  Trigger,
  Close,
  Portal,
  Title,
  Description,
  Content,
  Header,
  Body,
  Footer,
});

export { FocusModal };
