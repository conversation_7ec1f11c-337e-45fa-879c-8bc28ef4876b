import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import React, { useState, useEffect } from 'react';

import { Button } from '../button';
import { ProgressTabs, type ProgressTabsStatus } from '../progress-tabs';
import { FocusModal } from './focus-modal';

const meta: Meta<typeof FocusModal> = {
  title: 'Components/FocusModal',
  component: FocusModal,
  parameters: {
    layout: 'centered',
  },
};

export default meta;

type Story = StoryObj<typeof FocusModal>;

enum Tab {
  DETAILS = 'details',
  ORGANIZE = 'organize',
  VARIANTS = 'variants',
  INVENTORY = 'inventory',
}

type TabState = Record<Tab, ProgressTabsStatus>;

export const Default: Story = {
  render: () => {
    const [tab, setTab] = useState<Tab>(Tab.DETAILS);
    const [tabState, setTabState] = useState<TabState>({
      [Tab.DETAILS]: 'in-progress',
      [Tab.ORGANIZE]: 'not-started',
      [Tab.VARIANTS]: 'not-started',
      [Tab.INVENTORY]: 'not-started',
    });

    const onNext = async (currentTab: Tab) => {
      if (currentTab === Tab.DETAILS) {
        setTab(Tab.ORGANIZE);
      }

      if (currentTab === Tab.ORGANIZE) {
        setTab(Tab.VARIANTS);
      }

      if (currentTab === Tab.VARIANTS) {
        setTab(Tab.INVENTORY);
      }
    };

    // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
    useEffect(() => {
      const currentState = { ...tabState };
      if (tab === Tab.DETAILS) {
        currentState[Tab.DETAILS] = 'in-progress';
      }
      if (tab === Tab.ORGANIZE) {
        currentState[Tab.DETAILS] = 'completed';
        currentState[Tab.ORGANIZE] = 'in-progress';
      }
      if (tab === Tab.VARIANTS) {
        currentState[Tab.DETAILS] = 'completed';
        currentState[Tab.ORGANIZE] = 'completed';
        currentState[Tab.VARIANTS] = 'in-progress';
      }
      if (tab === Tab.INVENTORY) {
        currentState[Tab.DETAILS] = 'completed';
        currentState[Tab.ORGANIZE] = 'completed';
        currentState[Tab.VARIANTS] = 'completed';
        currentState[Tab.INVENTORY] = 'in-progress';
      }

      setTabState({ ...currentState });
    }, [tab]);

    return (
      <FocusModal>
        <FocusModal.Trigger asChild>
          <Button>Edit Variant</Button>
        </FocusModal.Trigger>
        <FocusModal.Content>
          <ProgressTabs
            value={tab}
            onValueChange={(value) => {
              setTab(value as Tab);
            }}
            className="flex h-full flex-col overflow-hidden"
          >
            <FocusModal.Header>
              <div className="-my-2 w-full border-border border-l">
                <ProgressTabs.List className="justify-start-start flex w-full items-center">
                  <ProgressTabs.Trigger
                    status={tabState[Tab.DETAILS]}
                    value={Tab.DETAILS}
                    className="max-w-[200px] truncate"
                  >
                    Details
                  </ProgressTabs.Trigger>
                  <ProgressTabs.Trigger
                    status={tabState[Tab.ORGANIZE]}
                    value={Tab.ORGANIZE}
                    className="max-w-[200px] truncate"
                  >
                    Organize
                  </ProgressTabs.Trigger>
                  <ProgressTabs.Trigger
                    status={tabState[Tab.VARIANTS]}
                    value={Tab.VARIANTS}
                    className="max-w-[200px] truncate"
                  >
                    Variants
                  </ProgressTabs.Trigger>
                  <ProgressTabs.Trigger
                    status={tabState[Tab.INVENTORY]}
                    value={Tab.INVENTORY}
                    className="max-w-[200px] truncate"
                  >
                    Inventory
                  </ProgressTabs.Trigger>
                </ProgressTabs.List>
              </div>
            </FocusModal.Header>
            <FocusModal.Body className="size-full overflow-hidden">
              <ProgressTabs.Content
                value={Tab.DETAILS}
                className="size-full overflow-y-auto"
              >
                Hello
              </ProgressTabs.Content>
              <ProgressTabs.Content
                value={Tab.ORGANIZE}
                className="size-full overflow-y-auto"
              >
                Hello
              </ProgressTabs.Content>
              <ProgressTabs.Content
                value={Tab.VARIANTS}
                className="size-full overflow-y-auto"
              >
                Hello
              </ProgressTabs.Content>
              <ProgressTabs.Content
                value={Tab.INVENTORY}
                className="size-full overflow-y-auto"
              >
                Hello
              </ProgressTabs.Content>
            </FocusModal.Body>
            <FocusModal.Footer>
              <div className="flex items-center justify-end gap-x-2">
                <Button variant="outline">Cancel</Button>
                <Button type="button" onClick={() => onNext(tab)}>
                  Next
                </Button>
              </div>
            </FocusModal.Footer>
          </ProgressTabs>
        </FocusModal.Content>
      </FocusModal>
    );
  },
};
