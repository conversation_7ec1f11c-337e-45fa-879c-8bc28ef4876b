'use client';

import { EyeIcon, EyeOffIcon } from '@muraadso/icons';
import React, { type ReactNode, useState, type ComponentProps } from 'react';
import { cn } from '../../utils';

type InputProps = ComponentProps<'input'> & {
  leading?: ReactNode;
  trailing?: ReactNode;
  containerClassName?: string;
};

const Input = ({
  type,
  leading,
  trailing,
  className,
  containerClassName,
  ...props
}: InputProps) => {
  const [typeState, setTypeState] = useState(type);
  const isPassword = type === 'password';

  return (
    <div className={cn('relative size-auto', containerClassName)}>
      {leading && (
        <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 text-muted-foreground/80 peer-disabled:opacity-50">
          {leading}
        </div>
      )}
      <input
        type={isPassword ? typeState : type}
        className={cn(
          'flex h-9 w-full rounded-lg border border-input bg-transparent px-3 py-2 text-foreground text-sm transition-all duration-300 placeholder:text-muted-foreground',
          'hover:border-transparent active:bg-transparent hover:[&:not(:focus)]:bg-accent',
          'focus-visible:border-ring focus-visible:outline-none focus-visible:ring-[3px] focus-visible:ring-ring/20',
          'disabled:cursor-not-allowed disabled:opacity-50',
          type === 'search' &&
            '[&::-webkit-search-cancel-button]:appearance-none [&::-webkit-search-decoration]:appearance-none [&::-webkit-search-results-button]:appearance-none [&::-webkit-search-results-decoration]:appearance-none',
          type === 'file' &&
            'p-0 pe-3 text-muted-foreground/70 italic file:me-3 file:h-full file:border-0 file:border-input file:border-r file:border-solid file:bg-transparent file:px-3 file:font-medium file:text-foreground file:text-sm file:not-italic',
          leading && 'ps-9',
          (isPassword || trailing) && 'pe-9',
          'aria-[invalid=true]:!text-destructive aria-[invalid=true]:border-destructive/80 aria-[invalid=true]:focus-visible:border-destructive/80 aria-[invalid=true]:focus-visible:ring-destructive/20',
          className,
        )}
        {...props}
      />

      {isPassword && !trailing && (
        <button
          className="absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-lg text-muted-foreground/80 outline-offset-2 transition-colors hover:text-foreground focus:z-10 focus-visible:outline-ring/70 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
          type="button"
          onClick={() =>
            setTypeState(typeState === 'password' ? 'text' : 'password')
          }
        >
          {typeState === 'password' ? (
            <EyeOffIcon size={16} strokeWidth={2} aria-hidden="true" />
          ) : (
            <EyeIcon size={16} strokeWidth={2} aria-hidden="true" />
          )}
        </button>
      )}

      {trailing && (
        <div
          className={cn(
            'pointer-events-none absolute inset-y-0 end-0 flex items-center justify-center pe-3 text-muted-foreground/80 peer-disabled:opacity-50',
          )}
        >
          {trailing}
        </div>
      )}
    </div>
  );
};

export { Input };
