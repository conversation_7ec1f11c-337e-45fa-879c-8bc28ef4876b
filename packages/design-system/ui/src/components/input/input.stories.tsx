import type { <PERSON>a, StoryObj } from '@storybook/react';
import React, { useState, useMemo } from 'react';

import {
  CheckIcon,
  ChevronDownIcon,
  CloseIcon,
  EyeIcon,
  EyeOffIcon,
  FingerprintIcon,
  MailIcon,
  ShareIcon,
} from '@muraadso/icons';
import { Button } from '../button';
import { Label } from '../label';
import { Input } from './index';

const meta: Meta<typeof Input> = {
  title: 'Components/Input',
  component: Input,
  parameters: {
    layout: 'centered',
  },
};

export default meta;

type Story = StoryObj<typeof Input>;

export const Default: Story = {
  render: () => {
    return (
      <div className="grid gap-2">
        <Label htmlFor="simple_input">Simple input</Label>
        <Input
          id="simple_input"
          placeholder="Email"
          type="email"
          className="w-64"
        />
      </div>
    );
  },
};

export const Icon: Story = {
  render: () => {
    return (
      <div className="flex flex-col-reverse gap-2">
        <div className="grid gap-2">
          <Label htmlFor="1">Input with end icon</Label>
          <div className="relative">
            <Input
              id="1"
              placeholder="Email"
              className="peer w-64 pe-9"
              type="email"
            />
            <div className="pointer-events-none absolute inset-y-0 end-0 flex items-center justify-center pe-3 text-muted-foreground/80 peer-disabled:opacity-50">
              <MailIcon size={16} strokeWidth={2} aria-hidden="true" />
            </div>
          </div>
        </div>
      </div>
    );
  },
};

export const Addons: Story = {
  render: () => {
    return (
      <div className="flex flex-wrap items-center justify-center gap-8">
        <div className="grid gap-2">
          <Label htmlFor="1">Input with start inline add-on</Label>
          <div className="relative">
            <Input
              id="1"
              className="peer ps-16"
              placeholder="google.com"
              type="text"
            />
            <span className="pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 text-muted-foreground text-sm peer-disabled:opacity-50">
              https://
            </span>
          </div>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="2">Input with end inline add-on</Label>
          <div className="relative">
            <Input
              id="2"
              className="peer pe-12"
              placeholder="google"
              type="text"
            />
            <span className="pointer-events-none absolute inset-y-0 end-0 flex items-center justify-center pe-3 text-muted-foreground text-sm peer-disabled:opacity-50">
              .com
            </span>
          </div>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="3">Input with inline add-ons</Label>
          <div className="relative">
            <Input
              id="3"
              className="peer ps-6 pe-12"
              placeholder="0.00"
              type="text"
            />
            <span className="pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 text-muted-foreground text-sm peer-disabled:opacity-50">
              €
            </span>
            <span className="pointer-events-none absolute inset-y-0 end-0 flex items-center justify-center pe-3 text-muted-foreground text-sm peer-disabled:opacity-50">
              EUR
            </span>
          </div>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="4">Input with start add-on</Label>
          <div className="flex rounded-lg shadow-black/5 shadow-sm">
            <span className="-z-10 inline-flex items-center rounded-s-lg border border-input bg-background px-3 text-muted-foreground text-sm">
              https://
            </span>
            <Input
              id="4"
              className="-ms-px rounded-s-none shadow-none"
              placeholder="google.com"
              type="text"
            />
          </div>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="5">Input with end add-on</Label>
          <div className="flex rounded-lg shadow-black/5 shadow-sm">
            <Input
              id="5"
              className="-me-px rounded-e-none shadow-none"
              placeholder="google"
              type="text"
            />
            <span className="-z-10 inline-flex items-center rounded-e-lg border border-input bg-background px-3 text-muted-foreground text-sm">
              .com
            </span>
          </div>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="6">Input with inline start and end add-on</Label>
          <div className="relative flex rounded-lg shadow-black/5 shadow-sm">
            <span className="pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 text-muted-foreground text-sm">
              €
            </span>
            <Input
              id="6"
              className="-me-px rounded-e-none ps-6 shadow-none"
              placeholder="0.00"
              type="text"
            />
            <span className="-z-10 inline-flex items-center rounded-e-lg border border-input bg-background px-3 text-muted-foreground text-sm">
              EUR
            </span>
          </div>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="7">Input with start select</Label>
          <div className="flex rounded-lg shadow-black/5 shadow-sm">
            <div className="relative">
              <select
                className="peer inline-flex h-full appearance-none items-center rounded-none rounded-s-lg border border-input bg-background ps-3 pe-8 text-muted-foreground text-sm transition-shadow hover:bg-accent hover:text-accent-foreground focus:z-10 focus-visible:border-ring focus-visible:text-foreground focus-visible:outline-none focus-visible:ring-[3px] focus-visible:ring-ring/20 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
                aria-label="Protocol"
              >
                <option value="https://">https://</option>
                <option value="http://">http://</option>
                <option value="ftp://">ftp://</option>
                <option value="sftp://">sftp://</option>
                <option value="ws://">ws://</option>
                <option value="wss://">wss://</option>
              </select>
              <span className="pointer-events-none absolute inset-y-0 end-0 z-10 flex h-full w-9 items-center justify-center text-muted-foreground/80 peer-disabled:opacity-50">
                <ChevronDownIcon
                  size={16}
                  strokeWidth={2}
                  aria-hidden="true"
                  role="img"
                />
              </span>
            </div>
            <Input
              id="7"
              className="-ms-px rounded-s-none shadow-none focus-visible:z-10"
              placeholder="192.168.1.1"
              type="text"
            />
          </div>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="8">Input with end inline button</Label>
          <div className="relative">
            <Input id="8" className="pe-9" placeholder="Email" type="email" />
            <button
              type="button"
              className="absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-lg border border-transparent text-muted-foreground/80 outline-offset-2 transition-colors hover:text-foreground focus-visible:outline-ring/70 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
              aria-label="Subscribe"
            >
              <ShareIcon size={16} strokeWidth={2} aria-hidden="true" />
            </button>
          </div>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="9">Input with end icon button</Label>
          <div className="flex rounded-lg shadow-black/5 shadow-sm">
            <Input
              id="9"
              className="-me-px flex-1 rounded-e-none shadow-none focus-visible:z-10"
              placeholder="Email"
              type="email"
            />
            <button
              type="button"
              className="inline-flex w-9 items-center justify-center rounded-e-lg border border-input bg-background text-muted-foreground/80 text-sm outline-offset-2 transition-colors hover:bg-accent hover:text-accent-foreground focus:z-10 focus-visible:outline-ring/70 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
              aria-label="Subscribe"
            >
              <FingerprintIcon size={16} strokeWidth={2} aria-hidden="true" />
            </button>
          </div>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="10">Input with end button</Label>
          <div className="flex rounded-lg shadow-black/5 shadow-sm">
            <Input
              id="10"
              className="-me-px flex-1 rounded-e-none shadow-none focus-visible:z-10"
              placeholder="Email"
              type="email"
            />
            <button
              type="button"
              className="inline-flex items-center rounded-e-lg border border-input bg-background px-3 font-medium text-foreground text-sm outline-offset-2 transition-colors hover:bg-accent hover:text-foreground focus:z-10 focus-visible:outline-ring/70 disabled:cursor-not-allowed disabled:opacity-50"
            >
              Send
            </button>
          </div>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="11">Input with button</Label>
          <div className="flex gap-2">
            <Input
              id="11"
              className="flex-1"
              placeholder="Email"
              type="email"
            />
            <Button variant="outline">Send</Button>
          </div>
        </div>
      </div>
    );
  },
};

export const File: Story = {
  render: () => {
    return (
      <div className="grid gap-2">
        <Label htmlFor="disabled_input">File input</Label>
        <Input
          type="file"
          id="disabled_input"
          placeholder="Email"
          className="peer w-64"
        />
      </div>
    );
  },
};

export const Password: Story = {
  render: () => {
    const [password, setPassword] = useState('');

    const checkStrength = (pass: string) => {
      const requirements = [
        { regex: /.{8,}/, text: 'At least 8 characters' },
        { regex: /[0-9]/, text: 'At least 1 number' },
        { regex: /[a-z]/, text: 'At least 1 lowercase letter' },
        { regex: /[A-Z]/, text: 'At least 1 uppercase letter' },
      ];

      return requirements.map((req) => ({
        met: req.regex.test(pass),
        text: req.text,
      }));
    };

    const strength = checkStrength(password);

    const strengthScore = useMemo(() => {
      return strength.filter((req) => req.met).length;
    }, [strength]);

    const getStrengthColor = (score: number) => {
      if (score === 0) return 'bg-border';
      if (score <= 1) return 'bg-red-500';
      if (score <= 2) return 'bg-orange-500';
      if (score === 3) return 'bg-amber-500';
      return 'bg-emerald-500';
    };

    const getStrengthText = (score: number) => {
      if (score === 0) return 'Enter a password';
      if (score <= 2) return 'Weak password';
      if (score === 3) return 'Medium password';
      return 'Strong password';
    };

    return (
      <div className="flex flex-wrap items-center justify-center gap-8">
        <div className="grid gap-2">
          <Label htmlFor="12">Show/hide password input</Label>
          <Input
            id="12"
            className="pe-9"
            placeholder="Password"
            type="password"
          />
        </div>

        <div>
          {/* Password input field with toggle visibility button */}
          <div className="grid gap-2">
            <Label htmlFor="2">Input with password strength indicator</Label>
            <Input
              id="2"
              className="pe-9"
              placeholder="Password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              aria-invalid={strengthScore < 4}
              aria-describedby="2-description"
            />
          </div>

          {/* Password strength indicator */}
          <div
            className="mt-3 mb-4 h-1 w-full overflow-hidden rounded-full bg-border"
            aria-valuenow={strengthScore}
            aria-valuemin={0}
            aria-valuemax={4}
            aria-label="Password strength"
          >
            <div
              className={`h-full ${getStrengthColor(strengthScore)} transition-all duration-500 ease-out`}
              style={{ width: `${(strengthScore / 4) * 100}%` }}
            />
          </div>

          {/* Password strength description */}
          <p className="mb-2 font-medium text-foreground text-sm">
            {getStrengthText(strengthScore)}. Must contain:
          </p>

          {/* Password requirements list */}
          <ul className="space-y-1.5" aria-label="Password requirements">
            {strength.map((req, index) => (
              <li key={`key-${index * 2}`} className="flex items-center gap-2">
                {req.met ? (
                  <CheckIcon
                    size={16}
                    className="text-emerald-500"
                    aria-hidden="true"
                  />
                ) : (
                  <CloseIcon
                    size={16}
                    className="text-muted-foreground/80"
                    aria-hidden="true"
                  />
                )}
                <span
                  className={`text-xs ${req.met ? 'text-emerald-600' : 'text-muted-foreground'}`}
                >
                  {req.text}
                  <span className="sr-only">
                    {req.met ? ' - Requirement met' : ' - Requirement not met'}
                  </span>
                </span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    );
  },
};

export const Disabled: Story = {
  render: () => {
    return (
      <div className="grid gap-2">
        <Input
          disabled
          id="disabled_input"
          placeholder="Email"
          type="email"
          className="peer w-64"
        />
        <Label htmlFor="disabled_input">Disabled input</Label>
      </div>
    );
  },
};
