import { CheckIcon, ExpandUpIcon } from '@muraadso/icons';
import React from 'react';

import { cn } from '../../utils';
import { Button } from '../button';
import { Command } from '../command';
import { Popover } from '../popover';

type Item = {
  label: string;
  value: string;
};

export const Combobox = ({
  items,
  onSelectItem,
  placeholder,
  loading,
  className,
}: {
  items: Item[];
  onSelectItem: (item: Item) => void;
  placeholder?: string;
  loading?: boolean;
  className?: string;
}) => {
  const [open, setOpen] = React.useState(false);
  const [value, setValue] = React.useState('');

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <Popover.Trigger asChild>
        <Button
          variant="outline"
          aria-expanded={open}
          className={cn('w-[200px] justify-between', className)}
        >
          {value
            ? items.find((item) => item.value === value)?.label
            : placeholder
              ? placeholder
              : 'Select...'}
          <ExpandUpIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </Popover.Trigger>
      <Popover.Content className="w-(--radix-popover-trigger-width) p-0">
        <Command>
          <Command.Input loading={loading} placeholder="Search..." />
          <Command.List>
            {loading ? (
              <Command.Empty>Loading...</Command.Empty>
            ) : items.length === 0 ? (
              <Command.Empty>No result</Command.Empty>
            ) : (
              <Command.Group>
                {items.map((item) => (
                  <Command.Item
                    key={item.value}
                    value={item.value}
                    onSelect={(currentValue) => {
                      setValue(currentValue === value ? '' : currentValue);
                      setOpen(false);
                    }}
                  >
                    <CheckIcon
                      className={cn(
                        'mr-2 h-4 w-4',
                        value === item.value ? 'opacity-100' : 'opacity-0',
                      )}
                    />
                    {item.label}
                  </Command.Item>
                ))}
              </Command.Group>
            )}
          </Command.List>
        </Command>
      </Popover.Content>
    </Popover>
  );
};
