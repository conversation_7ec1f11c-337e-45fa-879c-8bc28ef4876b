import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';
import { Combobox } from './combobox';

const meta: Meta<typeof Combobox> = {
  title: 'Components/Combobox',
  component: Combobox,
  parameters: {
    layout: 'centered',
  },
};

export default meta;

type Story = StoryObj<typeof Combobox>;

const fruitItems = [
  { label: 'Apple', value: 'apple' },
  { label: 'Banana', value: 'banana' },
  { label: 'Blueberry', value: 'blueberry' },
  { label: 'Grapes', value: 'grapes' },
  { label: 'Pineapple', value: 'pineapple' },
];

export const Default: Story = {
  render: () => {
    return (
      <Combobox
        items={fruitItems}
        onSelectItem={(item) => console.log('Selected:', item)}
      />
    );
  },
};

export const WithPlaceholder: Story = {
  render: () => {
    return (
      <Combobox
        items={fruitItems}
        placeholder="Select a fruit"
        onSelectItem={(item) => console.log('Selected:', item)}
      />
    );
  },
};

export const CustomWidth: Story = {
  render: () => {
    return (
      <Combobox
        items={fruitItems}
        placeholder="Select a fruit"
        className="w-[300px]"
        onSelectItem={(item) => console.log('Selected:', item)}
      />
    );
  },
};

export const Loading: Story = {
  render: () => {
    return (
      <Combobox
        items={[]}
        loading={true}
        placeholder="Loading fruits..."
        onSelectItem={(item) => console.log('Selected:', item)}
      />
    );
  },
};

export const Empty: Story = {
  render: () => {
    return (
      <Combobox
        items={[]}
        placeholder="No fruits available"
        onSelectItem={(item) => console.log('Selected:', item)}
      />
    );
  },
};

export const WithInitialSelection: Story = {
  render: () => {
    const [selectedItem, setSelectedItem] = React.useState(fruitItems[2]);
    
    return (
      <div className="flex flex-col items-center gap-4">
        <Combobox
          items={fruitItems}
          placeholder="Select a fruit"
          onSelectItem={(item) => {
            console.log('Selected:', item);
            setSelectedItem(item);
          }}
        />
        {selectedItem && (
          <div className="text-sm">
            Selected: <strong>{selectedItem.label}</strong>
          </div>
        )}
      </div>
    );
  },
};
