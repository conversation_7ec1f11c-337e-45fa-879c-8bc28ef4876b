import { Label as LabelPrimitive } from 'radix-ui';
import { cn } from '../../utils';

import React, { type FC, type LabelHTMLAttributes } from 'react';

type LabelProps = LabelHTMLAttributes<HTMLLabelElement>;

const Label: FC<LabelProps> = ({ className, ...props }) => {
  return (
    <LabelPrimitive.Root
      className={cn(
        'font-medium text-foreground text-sm leading-4 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',
        className,
      )}
      {...props}
    />
  );
};

export { Label };
