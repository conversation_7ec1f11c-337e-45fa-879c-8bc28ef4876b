import { ChevronDownIcon } from '@muraadso/icons';
import { cva } from 'cva';
import { NavigationMenu as NavigationMenuPrimitive } from 'radix-ui';
import React, { type FC, type ComponentProps } from 'react';
import { cn } from '../../utils';

const Root: FC<ComponentProps<typeof NavigationMenuPrimitive.Root>> = ({
  className,
  children,
  ...props
}) => (
  <NavigationMenuPrimitive.Root
    className={cn(
      'relative z-10 flex max-w-max flex-1 items-center justify-center',
      className,
    )}
    {...props}
  >
    {children}
    <Viewport />
  </NavigationMenuPrimitive.Root>
);

const Viewport: FC<ComponentProps<typeof NavigationMenuPrimitive.Viewport>> = ({
  className,
  ...props
}) => (
  <div className={cn('absolute top-full left-0 flex justify-center')}>
    <NavigationMenuPrimitive.Viewport
      className={cn(
        'data-[state=closed]:zoom-in-out-95 data-[state=open]:zoom-in-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full origin-top-center overflow-hidden rounded-md border border-border bg-popover text-popover-foreground shadow-lg data-[state=closed]:animate-out data-[state=open]:animate-in md:w-[var(--radix-navigation-menu-viewport-width)]',
        className,
      )}
      {...props}
    />
  </div>
);

const navigationMenuTriggerStyle = cva({
  base: 'group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 font-medium text-sm transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50',
});

const Trigger: FC<ComponentProps<typeof NavigationMenuPrimitive.Trigger>> = ({
  className,
  children,
  ...props
}) => (
  <NavigationMenuPrimitive.Trigger
    className={cn(navigationMenuTriggerStyle(), 'group', className)}
    {...props}
  >
    {children}{' '}
    <ChevronDownIcon
      className="relative top-[1px] left-1 h-4 w-4 opacity-50 transition duration-200 group-data-[state=open]:rotate-180"
      aria-hidden="true"
    />
  </NavigationMenuPrimitive.Trigger>
);

const Content: FC<ComponentProps<typeof NavigationMenuPrimitive.Content>> = ({
  className,
  ...props
}) => (
  <NavigationMenuPrimitive.Content
    className={cn(
      'data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out md:absolute md:w-auto',
      className,
    )}
    {...props}
  />
);

const List: FC<ComponentProps<typeof NavigationMenuPrimitive.List>> = ({
  className,
  ...props
}) => (
  <NavigationMenuPrimitive.List
    className={cn(
      'group flex flex-1 list-none items-center justify-center space-x-1',
      className,
    )}
    {...props}
  />
);

const Item = NavigationMenuPrimitive.Item;

const Link = NavigationMenuPrimitive.Link;

const Indicator: FC<
  ComponentProps<typeof NavigationMenuPrimitive.Indicator>
> = ({ className, ...props }) => (
  <NavigationMenuPrimitive.Indicator
    className={cn(
      'data-[state=hidden]:fade-out data-[state=visible]:fade-in top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden data-[state=hidden]:animate-out data-[state=visible]:animate-in',
      className,
    )}
    {...props}
  >
    <div className="relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md" />
  </NavigationMenuPrimitive.Indicator>
);

const NavigationMenu = Object.assign(Root, {
  Trigger,
  Viewport,
  Content,
  List,
  Item,
  Link,
  Indicator,
});

export { NavigationMenu, navigationMenuTriggerStyle };
