import { RadioGroup as RadioGroupPrimitive } from 'radix-ui';
import React, { type ComponentProps, type FC } from 'react';
import { cn } from '../../utils';

type RadioGroupProps = ComponentProps<typeof RadioGroupPrimitive.Root>;

const Root: FC<RadioGroupProps> = ({ className, ...props }) => {
  return (
    <RadioGroupPrimitive.Root
      className={cn('grid gap-3', className)}
      {...props}
    />
  );
};

type ItemProps = ComponentProps<typeof RadioGroupPrimitive.Item>;

const Item: FC<ItemProps> = ({ className, ...props }) => {
  return (
    <RadioGroupPrimitive.Item
      className={cn(
        'aspect-square size-4 rounded-full border border-input shadow-black/5 shadow-sm outline-offset-2',
        'focus-visible:outline-2 focus-visible:outline-ring/70',
        'disabled:cursor-not-allowed disabled:opacity-50',
        'data-[state=checked]:!border-primary data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground',
        className,
      )}
      {...props}
    >
      <RadioGroupPrimitive.Indicator className="flex items-center justify-center text-current">
        <svg
          width="6"
          height="6"
          viewBox="0 0 6 6"
          fill="currentcolor"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle cx="3" cy="3" r="3" />
        </svg>
      </RadioGroupPrimitive.Indicator>
    </RadioGroupPrimitive.Item>
  );
};

const RadioGroup = Object.assign(Root, {
  Item,
});

export { RadioGroup };
