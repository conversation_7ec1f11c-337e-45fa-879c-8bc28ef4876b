import type { <PERSON>a, StoryObj } from '@storybook/react';
import React, { useId } from 'react';

import { Label } from '../label';
import { RadioGroup } from './radio-group';

const meta: Meta<typeof RadioGroup> = {
  title: 'Components/RadioGroup',
  component: RadioGroup,
  parameters: {
    layout: 'centered',
  },
};

export default meta;

type Story = StoryObj<typeof RadioGroup>;

export const Default: Story = {
  render: () => {
    const id = useId();
    return (
      <RadioGroup defaultValue="1" className="flex flex-col gap-4">
        <div className="flex items-center gap-2">
          <RadioGroup.Item value="1" id={`${id}-1`} />
          <Label htmlFor={`${id}-1`}>Option 1</Label>
        </div>
        <div className="flex items-center gap-2">
          <RadioGroup.Item value="2" id={`${id}-2`} />
          <Label htmlFor={`${id}-2`}>Option 2</Label>
        </div>
        <div className="flex items-center gap-2">
          <RadioGroup.Item value="3" id={`${id}-3`} />
          <Label htmlFor={`${id}-3`}>Option 3</Label>
        </div>
      </RadioGroup>
    );
  },
};
