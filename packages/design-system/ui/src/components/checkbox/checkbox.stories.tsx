import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import React from 'react';
import { Label } from '../label';
import { Checkbox } from './checkbox';

const meta: Meta<typeof Checkbox> = {
  title: 'Components/Checkbox',
  component: Checkbox,
  parameters: {
    layout: 'centered',
  },
};

export default meta;

type Story = StoryObj<typeof Checkbox>;

export const Default: Story = {
  render: () => {
    return (
      <div className="flex items-center gap-2">
        <Checkbox id="checkbox" />
        <Label htmlFor="checkbox">Simple checkbox</Label>
      </div>
    );
  },
};

export const Disabled: Story = {
  render: () => {
    return (
      <div className="flex items-center gap-2">
        <Checkbox id="checkbox" disabled />
        <Label htmlFor="checkbox">Disabled checkbox</Label>
      </div>
    );
  },
};
