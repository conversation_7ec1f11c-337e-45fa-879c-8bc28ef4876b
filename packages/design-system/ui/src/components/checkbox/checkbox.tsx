import { Checkbox as CheckboxPrimitive } from 'radix-ui';
import React, { type ComponentProps, type FC } from 'react';

import { CheckIcon } from '@muraadso/icons';
import { cn } from '../../utils';

type CheckboxProps = ComponentProps<typeof CheckboxPrimitive.Root>;

const Checkbox: FC<CheckboxProps> = ({ className, ...props }) => {
  return (
    <CheckboxPrimitive.Root
      className={cn(
        'peer grid size-4 shrink-0 place-content-center rounded border border-input bg-background shadow-black/5 shadow-sm outline-offset-2',
        'focus-visible:outline-2 focus-visible:outline-ring/70',
        'disabled:cursor-not-allowed disabled:opacity-50',
        'data-[state=checked]:!border-primary data-[state=indeterminate]:border-primary data-[state=checked]:bg-primary data-[state=indeterminate]:bg-primary data-[state=checked]:text-primary-foreground data-[state=indeterminate]:text-primary-foreground',
        className,
      )}
      {...props}
    >
      <CheckboxPrimitive.Indicator className="grid place-content-center text-current">
        <CheckIcon className="size-3.5" />
      </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
  );
};

export { Checkbox };
