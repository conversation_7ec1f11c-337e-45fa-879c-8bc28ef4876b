'use client';

import { CheckIcon, ChevronRightIcon, CircleIcon } from '@muraadso/icons';
import { DropdownMenu as DropdownMenuPrimitive } from 'radix-ui';
import React, {
  type FC,
  type ComponentProps,
  type HTMLAttributes,
} from 'react';
import { cn } from '../../utils';

type PointerDownEvent = Parameters<
  NonNullable<DropdownMenuPrimitive.DropdownMenuContentProps['onPointerDown']>
>[0];
type PointerDownOutsideEvent = Parameters<
  NonNullable<
    DropdownMenuPrimitive.DropdownMenuContentProps['onPointerDownOutside']
  >
>[0];

const Root = DropdownMenuPrimitive.Root;

const Portal = DropdownMenuPrimitive.Portal;

const Trigger = DropdownMenuPrimitive.Trigger;

const Content: FC<ComponentProps<typeof DropdownMenuPrimitive.Content>> = ({
  onPointerDown,
  onPointerDownOutside,
  onCloseAutoFocus,
  sideOffset = 4,
  className,
  ...props
}) => {
  const isCloseFromMouse = React.useRef<boolean>(false);

  const handlePointerDown = React.useCallback(
    (e: PointerDownEvent) => {
      isCloseFromMouse.current = true;
      onPointerDown?.(e);
    },
    [onPointerDown],
  );

  const handlePointerDownOutside = React.useCallback(
    (e: PointerDownOutsideEvent) => {
      isCloseFromMouse.current = true;
      onPointerDownOutside?.(e);
    },
    [onPointerDownOutside],
  );

  const handleCloseAutoFocus = React.useCallback(
    (e: Event) => {
      if (onCloseAutoFocus) {
        return onCloseAutoFocus(e);
      }

      if (!isCloseFromMouse.current) {
        return;
      }

      e.preventDefault();
      isCloseFromMouse.current = false;
    },
    [onCloseAutoFocus],
  );

  return (
    <DropdownMenuPrimitive.Portal>
      <DropdownMenuPrimitive.Content
        sideOffset={sideOffset}
        className={cn(
          'data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 data-[state=open]:zoom-in-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-in-out-95 z-50 min-w-40 overflow-hidden rounded-lg border border-border bg-popover p-1 text-popover-foreground shadow-black/5 shadow-lg data-[state=closed]:animate-out data-[state=open]:animate-in',
          className,
        )}
        onPointerDown={handlePointerDown}
        onPointerDownOutside={handlePointerDownOutside}
        onCloseAutoFocus={handleCloseAutoFocus}
        {...props}
      />
    </DropdownMenuPrimitive.Portal>
  );
};

const Group = DropdownMenuPrimitive.Group;

const RadioGroup = DropdownMenuPrimitive.RadioGroup;

const Sub = DropdownMenuPrimitive.Sub;

const SubTrigger: FC<
  ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {
    inset?: boolean;
  }
> = ({ inset, className, children, ...props }) => {
  return (
    <DropdownMenuPrimitive.SubTrigger
      className={cn(
        'flex cursor-default select-none items-center gap-2 rounded-md px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0',
        inset && 'pl-8',
        className,
      )}
      {...props}
    >
      {children}
      <ChevronRightIcon className="ml-auto text-muted-foreground/80" />
    </DropdownMenuPrimitive.SubTrigger>
  );
};

const SubContent: FC<
  ComponentProps<typeof DropdownMenuPrimitive.SubContent>
> = ({ className, ...props }) => {
  return (
    <DropdownMenuPrimitive.SubContent
      className={cn(
        'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-in-out-95 data-[state=open]:zoom-in-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-40 overflow-hidden rounded-md border border-border bg-popover p-1 text-popover-foreground shadow-black/5 shadow-lg data-[state=closed]:animate-out data-[state=open]:animate-in',
        className,
      )}
      {...props}
    />
  );
};

const Item: FC<
  ComponentProps<typeof DropdownMenuPrimitive.Item> & {
    inset?: boolean;
  }
> = ({ inset, className, ...props }) => {
  return (
    <DropdownMenuPrimitive.Item
      className={cn(
        'relative flex cursor-default select-none items-center gap-2 rounded-md px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0',
        inset && 'pl-8',
        className,
      )}
      {...props}
    />
  );
};

const CheckboxItem: FC<
  ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>
> = ({ checked, className, children, ...props }) => {
  return (
    <DropdownMenuPrimitive.CheckboxItem
      className={cn(
        'relative flex cursor-default select-none items-center rounded-md py-1.5 pr-2 pl-8 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
        className,
      )}
      checked={checked}
      {...props}
    >
      <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
        <DropdownMenuPrimitive.ItemIndicator>
          <CheckIcon className="h-4 w-4" />
        </DropdownMenuPrimitive.ItemIndicator>
      </span>
      {children}
    </DropdownMenuPrimitive.CheckboxItem>
  );
};

const RadioItem: FC<ComponentProps<typeof DropdownMenuPrimitive.RadioItem>> = ({
  className,
  children,
  ...props
}) => {
  return (
    <DropdownMenuPrimitive.RadioItem
      className={cn(
        'relative flex cursor-default select-none items-center rounded-md py-1.5 pr-2 pl-8 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
        className,
      )}
      {...props}
    >
      <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
        <DropdownMenuPrimitive.ItemIndicator>
          <CircleIcon className="h-2 w-2 fill-current" />
        </DropdownMenuPrimitive.ItemIndicator>
      </span>
      {children}
    </DropdownMenuPrimitive.RadioItem>
  );
};

const Label: FC<
  ComponentProps<typeof DropdownMenuPrimitive.Label> & {
    inset?: boolean;
  }
> = ({ inset, className, ...props }) => {
  return (
    <DropdownMenuPrimitive.Label
      className={cn(
        'px-2 py-1.5 font-medium text-muted-foreground text-xs',
        inset && 'pl-8',
        className,
      )}
      {...props}
    />
  );
};

const Separator: FC<ComponentProps<typeof DropdownMenuPrimitive.Separator>> = ({
  className,
  ...props
}) => {
  return (
    <DropdownMenuPrimitive.Separator
      className={cn('-mx-1 my-1 h-px bg-border', className)}
      {...props}
    />
  );
};

const Shortcut: FC<HTMLAttributes<HTMLSpanElement>> = ({
  className,
  ...props
}) => {
  return (
    <span
      className={cn(
        '-me-1 ms-auto inline-flex h-5 max-h-full items-center rounded border border-border bg-background px-1 font-[inherit] font-medium text-[0.625rem] text-muted-foreground/70',
        className,
      )}
      {...props}
    />
  );
};

const DropdownMenu = Object.assign(Root, {
  Portal,
  Trigger,
  Content,
  Label,
  Separator,
  Shortcut,
  Group,
  RadioGroup,
  Item,
  CheckboxItem,
  RadioItem,
  Sub,
  SubTrigger,
  SubContent,
});

export { DropdownMenu };
