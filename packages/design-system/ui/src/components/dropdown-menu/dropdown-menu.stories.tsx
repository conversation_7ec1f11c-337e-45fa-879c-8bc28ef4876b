import {
  BellIcon,
  ChevronDownIcon,
  EditIcon,
  ExpandUpIcon,
  TrashIcon,
} from '@muraadso/icons';
import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';
import { Button } from '../button';
import { DropdownMenu } from './dropdown-menu';

const meta: Meta<typeof DropdownMenu> = {
  title: 'Components/Dropdown Menu',
  component: DropdownMenu,
  parameters: {
    layout: 'centered',
  },
};

export default meta;

type Story = StoryObj<typeof DropdownMenu>;

export const Default: Story = {
  render: () => {
    return (
      <DropdownMenu>
        <DropdownMenu.Trigger asChild>
          <Button variant="outline">
            Simple menu
            <ChevronDownIcon
              className="-me-1 ms-2 opacity-60"
              size={16}
              strokeWidth={2}
              aria-hidden="true"
            />
          </Button>
        </DropdownMenu.Trigger>
        <DropdownMenu.Content className="min-w-(--radix-dropdown-menu-trigger-width)">
          <DropdownMenu.Item>Option 1</DropdownMenu.Item>
          <DropdownMenu.Item>Option 2</DropdownMenu.Item>
          <DropdownMenu.Item>Option 3</DropdownMenu.Item>
          <DropdownMenu.Item>Option 4</DropdownMenu.Item>
        </DropdownMenu.Content>
      </DropdownMenu>
    );
  },
};

export const WithIcon: Story = {
  render: () => {
    return (
      <DropdownMenu>
        <DropdownMenu.Trigger asChild>
          <Button variant="outline">
            Menu with icons
            <ChevronDownIcon
              className="-me-1 ms-2 opacity-60"
              size={16}
              strokeWidth={2}
              aria-hidden="true"
            />
          </Button>
        </DropdownMenu.Trigger>
        <DropdownMenu.Content className="min-w-(--radix-dropdown-menu-trigger-width)">
          <DropdownMenu.Item>
            <EditIcon
              size={16}
              strokeWidth={2}
              className="opacity-60"
              aria-hidden="true"
            />
            Edit
          </DropdownMenu.Item>
          <DropdownMenu.Item>
            <BellIcon
              size={16}
              strokeWidth={2}
              className="opacity-60"
              aria-hidden="true"
            />
            Notifications
          </DropdownMenu.Item>
          <DropdownMenu.Separator />
          <DropdownMenu.Item className="text-destructive focus:text-destructive">
            <TrashIcon size={16} strokeWidth={2} aria-hidden="true" />
            Delete
          </DropdownMenu.Item>
        </DropdownMenu.Content>
      </DropdownMenu>
    );
  },
};
