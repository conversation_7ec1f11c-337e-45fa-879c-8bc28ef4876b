'use client';

import React, { type FC, type TextareaHTMLAttributes } from 'react';
import { cn } from '../../utils';

type TextareaProps = TextareaHTMLAttributes<HTMLTextAreaElement>;

const Textarea: FC<TextareaProps> = ({ className, ...props }) => {
  return (
    <textarea
      className={cn(
        'flex w-full rounded-lg border border-input bg-transparent px-3 py-2 text-foreground text-sm transition-shadow placeholder:text-muted-foreground',
        'hover:border-transparent active:bg-transparent hover:[&:not(:focus)]:bg-accent',
        'focus-visible:border-ring focus-visible:outline-none focus-visible:ring-[3px] focus-visible:ring-ring/20',
        'disabled:cursor-not-allowed disabled:opacity-50',
        'aria-[invalid=true]:!text-destructive aria-[invalid=true]:border-destructive/80 aria-[invalid=true]:focus-visible:border-destructive/80 aria-[invalid=true]:focus-visible:ring-destructive/20',
        className,
      )}
      {...props}
    />
  );
};

export { Textarea };
