import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';

import { Label } from '../label';
import { Textarea } from './index';

const meta: Meta<typeof Textarea> = {
  title: 'Components/Textarea',
  component: Textarea,
  parameters: {
    layout: 'centered',
  },
};

export default meta;

type Story = StoryObj<typeof Textarea>;

export const Default: Story = {
  render: () => {
    return (
      <div className="grid gap-2">
        <Label htmlFor="simple_textarea">Simple textarea</Label>
        <Textarea
          id="simple_textarea"
          placeholder="Leave a comment"
          className="w-64"
        />
      </div>
    );
  },
};

export const WithError: Story = {
  render: () => {
    return (
      <div className="grid gap-2">
        <Label htmlFor="simple_textarea">Simple textarea</Label>
        <Textarea
          id="simple_textarea"
          aria-invalid="true"
          placeholder="Leave a comment"
          className="w-64"
        />
      </div>
    );
  },
};
