'use client';

import { Popover as PopoverPrimitive } from 'radix-ui';
import React, { type ComponentPropsWithoutRef } from 'react';
import { cn } from '../../utils';

const Root = PopoverPrimitive.Root;

const Trigger = PopoverPrimitive.Trigger;

const Anchor = PopoverPrimitive.Anchor;

const Close = PopoverPrimitive.Close;

const Content = ({
  className,
  align = 'center',
  sideOffset = 4,
  ...props
}: ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>) => (
  <PopoverPrimitive.Portal>
    <PopoverPrimitive.Content
      align={align}
      sideOffset={sideOffset}
      className={cn(
        'z-50 w-72 rounded-lg border border-border bg-popover p-4 text-foreground shadow-lg outline-none duration-200 sm:max-w-100',
        'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-in-out-95 data-[state=open]:zoom-in-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 data-[state=closed]:animate-out data-[state=open]:animate-in',
        className,
      )}
      {...props}
    />
  </PopoverPrimitive.Portal>
);

const Popover = Object.assign(Root, {
  Trigger,
  Anchor,
  Content,
  Close,
});

export { Popover };
