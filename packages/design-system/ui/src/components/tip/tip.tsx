import React, { type ComponentPropsWithoutRef } from 'react';
import { cn } from '../../utils';

type TipProps = ComponentPropsWithoutRef<'div'> & {
  label: string;
  variant?: 'info' | 'warning' | 'error' | 'success';
};

const Tip = ({
  variant = 'info',
  label,
  className,
  children,
  ...props
}: TipProps) => {
  return (
    <div
      className={cn(
        'grid grid-cols-[4px_1fr] items-start gap-3 rounded-lg border border-border bg-muted/70 p-3 text-muted-foreground text-sm',
        className,
      )}
      {...props}
    >
      <div
        role="presentation"
        className={cn('h-full w-1 rounded-full bg-foreground/30', {
          'bg-ui-tag-orange-icon': variant === 'warning',
          'bg-ui-tag-red-icon': variant === 'error',
          'bg-ui-tag-green-icon': variant === 'success',
        })}
      />
      <div className="text-pretty">
        <span className="text-foreground">{label}:</span> {children}
      </div>
    </div>
  );
};

export { Tip };
