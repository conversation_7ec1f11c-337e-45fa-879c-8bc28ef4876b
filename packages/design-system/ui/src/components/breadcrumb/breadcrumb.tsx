import { ChevronRightIcon, MoreHorizontalIcon } from '@muraadso/icons';
import { Slot } from 'radix-ui';
import React, {
  type ComponentPropsWithoutRef,
  type ReactNode,
  type ComponentProps,
} from 'react';

import { cn } from '../../utils';

const Root = ({
  ...props
}: ComponentPropsWithoutRef<'nav'> & {
  separator?: ReactNode;
}) => <nav aria-label="breadcrumb" {...props} />;

const List = ({ className, ...props }: ComponentPropsWithoutRef<'ol'>) => (
  <ol
    className={cn(
      'flex flex-wrap items-center gap-1.5 break-words text-muted-foreground text-sm sm:gap-2.5',
      className,
    )}
    {...props}
  />
);

const Item = ({ className, ...props }: ComponentPropsWithoutRef<'li'>) => (
  <li
    className={cn('inline-flex items-center gap-1.5', className)}
    {...props}
  />
);

const Link = ({
  asChild,
  className,
  ...props
}: ComponentPropsWithoutRef<'a'> & {
  asChild?: boolean;
}) => {
  const Comp = asChild ? Slot.Root : 'a';

  return (
    <Comp
      className={cn('transition-colors hover:text-foreground', className)}
      {...props}
    />
  );
};

const Page = ({ className, ...props }: ComponentPropsWithoutRef<'span'>) => (
  <span
    aria-current="page"
    aria-disabled="true"
    className={cn('font-normal text-foreground', className)}
    {...props}
  />
);

const Separator = ({ children, className, ...props }: ComponentProps<'li'>) => (
  <li
    role="presentation"
    aria-hidden="true"
    className={cn('[&>svg]:h-3.5 [&>svg]:w-3.5', className)}
    {...props}
  >
    {children ?? <ChevronRightIcon />}
  </li>
);

const Ellipsis = ({ className, ...props }: ComponentProps<'span'>) => (
  <span
    role="presentation"
    aria-hidden="true"
    className={cn('flex h-9 w-9 items-center justify-center', className)}
    {...props}
  >
    <MoreHorizontalIcon className="h-4 w-4" />
    <span className="sr-only">More</span>
  </span>
);

const Breadcrumb = Object.assign(Root, {
  List,
  Item,
  Link,
  Page,
  Separator,
  Ellipsis,
});

export { Breadcrumb };
