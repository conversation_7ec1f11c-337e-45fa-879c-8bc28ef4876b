import React, { type HTMLAttributes } from 'react';

import { cn } from '../../utils';

const Root = ({ className, ...props }: HTMLAttributes<HTMLTableElement>) => (
  <div className="relative w-full overflow-auto">
    <table
      className={cn('w-full caption-bottom text-sm', className)}
      {...props}
    />
  </div>
);

const Header = ({
  className,
  ...props
}: HTMLAttributes<HTMLTableSectionElement>) => (
  <thead
    // className={cn('border-border [&_tr]:border-b', className)}
    className={cn('', className)}
    {...props}
  />
);

const Body = ({
  className,
  ...props
}: HTMLAttributes<HTMLTableSectionElement>) => (
  <tbody
    className={cn('border-border [&_tr:last-child]:border-0', className)}
    {...props}
  />
);

const Footer = ({
  className,
  ...props
}: HTMLAttributes<HTMLTableSectionElement>) => (
  <tfoot
    className={cn(
      'border-border border-t bg-muted/50 font-medium [&>tr]:last:border-b-0',
      className,
    )}
    {...props}
  />
);

const Row = ({ className, ...props }: HTMLAttributes<HTMLTableRowElement>) => (
  <tr
    className={cn(
      'group transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted [&>td]:border-border [&>td]:border-b last:[&>td]:border-b-0',
      className,
    )}
    {...props}
  />
);

const Head = ({
  className,
  ...props
}: HTMLAttributes<HTMLTableCellElement>) => (
  <th
    className={cn(
      'h-10 bg-accent px-2 text-left align-middle font-normal text-muted-foreground first:rounded-l-lg last:rounded-r-lg [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]',
      className,
    )}
    {...props}
  />
);

const Cell = ({
  className,
  ...props
}: HTMLAttributes<HTMLTableCellElement>) => (
  <td
    className={cn(
      'p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]',
      className,
    )}
    {...props}
  />
);

const Caption = ({
  className,
  ...props
}: HTMLAttributes<HTMLTableCaptionElement>) => (
  <caption
    className={cn('mt-4 text-muted-foreground text-sm', className)}
    {...props}
  />
);

const Table = Object.assign(Root, {
  Header,
  Body,
  Footer,
  Head,
  Row,
  Cell,
  Caption,
});

export { Table };
