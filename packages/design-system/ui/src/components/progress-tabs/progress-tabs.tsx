'use client';

import { CheckCircle2Icon, Circle2Icon, Circle3Icon } from '@muraadso/icons';
import { Tabs as TabsPrimitive } from 'radix-ui';
import React, { type ComponentPropsWithoutRef } from 'react';

import { cn } from '../../utils';

export type Status = 'not-started' | 'in-progress' | 'completed';

const Root = (props: TabsPrimitive.TabsProps) => (
  <TabsPrimitive.Root {...props} />
);

const Indicator = ({
  status,
  className,
  ...props
}: Omit<ComponentPropsWithoutRef<'span'>, 'children'> & {
  status?: Status;
}) => {
  const Icon = React.useMemo(() => {
    switch (status) {
      case 'not-started':
        return Circle3Icon;
      case 'in-progress':
        return Circle2Icon;
      case 'completed':
        return CheckCircle2Icon;
      default:
        return Circle3Icon;
    }
  }, [status]);

  return (
    <span
      className={cn(
        'text-muted-foreground group-data-[state=active]/trigger:text-foreground',
        className,
      )}
      {...props}
    >
      <Icon />
    </span>
  );
};

const Trigger = ({
  className,
  status = 'not-started',
  children,
  ...props
}: Omit<ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>, 'asChild'> & {
  status?: Status;
}) => (
  <TabsPrimitive.Trigger
    className={cn(
      'inline-flex h-[52px] w-full max-w-[200px] flex-1 cursor-pointer items-center gap-x-2 border-border border-r bg-accent px-4 text-left text-muted-foreground text-sm outline-none',
      'group/trigger overflow-hidden text-ellipsis whitespace-nowrap',
      'disabled:bg-muted disabled:text-muted-foreground',
      'focus:z-[1] focus-visible:bg-background',
      'data-[state=active]:bg-background data-[state=active]:text-foreground',
      className,
    )}
    {...props}
  >
    <Indicator status={status} />
    {children}
  </TabsPrimitive.Trigger>
);

const List = ({
  className,
  ...props
}: ComponentPropsWithoutRef<typeof TabsPrimitive.List>) => (
  <TabsPrimitive.List
    className={cn('flex items-center', className)}
    {...props}
  />
);

const Content = ({
  className,
  ...props
}: ComponentPropsWithoutRef<typeof TabsPrimitive.Content>) => {
  return (
    <TabsPrimitive.Content
      className={cn('outline-none', className)}
      {...props}
    />
  );
};

const ProgressTabs = Object.assign(Root, {
  Trigger,
  List,
  Content: Content,
});

export { ProgressTabs, type Status as ProgressTabsStatus };
