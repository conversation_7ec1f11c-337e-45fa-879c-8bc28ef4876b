'use client';

import { InfoCircleIcon } from '@muraadso/icons';
import { Slot } from 'radix-ui';
import React, {
  type ComponentProps,
  type FC,
  type HTMLAttributes,
  type LabelHTMLAttributes,
  createContext,
  type ReactNode,
  useContext,
  useId,
} from 'react';
import {
  Controller,
  type ControllerProps,
  type FieldPath,
  type FieldValues,
  FormProvider,
  useFormContext,
} from 'react-hook-form';
import { cn } from '../../utils';
import { Label as LabelComponent } from '../label';
import { Tooltip } from '../tooltip';

const Root = FormProvider;

type FieldContextValue<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = {
  name: TName;
};

const FieldContext = createContext<FieldContextValue>({} as FieldContextValue);

const Field = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  ...props
}: ControllerProps<TFieldValues, TName>) => {
  return (
    <FieldContext.Provider value={{ name: props.name }}>
      <Controller {...props} />
    </FieldContext.Provider>
  );
};

const useField = () => {
  const fieldContext = useContext(FieldContext);
  const itemContext = useContext(ItemContext);
  const { getFieldState, formState } = useFormContext();

  const fieldState = getFieldState(fieldContext.name, formState);

  if (!fieldContext) {
    throw new Error('useFormField should be used within <FormField>');
  }

  const { id } = itemContext;

  return {
    id,
    name: fieldContext.name,
    itemId: `${id}-item`,
    descriptionId: `${id}-item-description`,
    messageId: `${id}-item-message`,
    ...fieldState,
  };
};

type ItemContextValue = {
  id: string;
};

const ItemContext = createContext<ItemContextValue>({} as ItemContextValue);

const Item = ({ className, ...props }: HTMLAttributes<HTMLDivElement>) => {
  const id = useId();
  return (
    <ItemContext.Provider value={{ id }}>
      <div className={cn('space-y-2', className)} {...props} />
    </ItemContext.Provider>
  );
};

const Label = ({
  optional,
  tooltip,
  className,
  ...props
}: LabelHTMLAttributes<HTMLLabelElement> & {
  tooltip?: ReactNode;
  optional?: boolean;
}) => {
  const { error, itemId } = useField();

  return (
    <div className="flex items-center space-x-1">
      <LabelComponent htmlFor={itemId} className={cn(className)} {...props} />

      {tooltip && (
        <Tooltip>
          <Tooltip.Trigger>
            <InfoCircleIcon className="size-4 text-muted-foreground" />
          </Tooltip.Trigger>
          <Tooltip.Content>{tooltip}</Tooltip.Content>
        </Tooltip>
      )}

      {optional && (
        <span className="text-muted-foreground text-sm leading-none">
          (Optional)
        </span>
      )}
    </div>
  );
};

const Control = ({ ...props }: ComponentProps<typeof Slot.Root>) => {
  const { error, itemId, descriptionId, messageId } = useField();

  return (
    <Slot.Root
      id={itemId}
      aria-describedby={
        !error ? `${descriptionId}` : `${descriptionId} ${messageId}`
      }
      aria-invalid={!!error}
      {...props}
    />
  );
};

const Description = ({
  className,
  ...props
}: HTMLAttributes<HTMLParagraphElement>) => {
  const { descriptionId } = useField();

  return (
    <p
      id={descriptionId}
      className={cn('text-[0.8rem] text-muted-foreground', className)}
      {...props}
    />
  );
};

const Message = ({
  className,
  children,
  ...props
}: HTMLAttributes<HTMLParagraphElement>) => {
  const { error, messageId } = useField();
  const body = error ? String(error?.message) : children;

  if (!body) {
    return null;
  }

  return (
    <p
      id={messageId}
      className={cn(
        'font-medium text-destructive text-sm leading-none',
        className,
      )}
      {...props}
    >
      {body}
    </p>
  );
};

const Form = Object.assign(Root, {
  useField,
  Item,
  Label,
  Control,
  Description,
  Message,
  Field,
});

export { Form };
