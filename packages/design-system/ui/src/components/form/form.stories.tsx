import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import type { Meta, StoryObj } from '@storybook/react';
import * as React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '../button';
import { Input } from '../input';
import { Form } from './form';

const meta: Meta<typeof Form> = {
  title: 'Components/Form',
  component: Form,
  parameters: {
    layout: 'centered',
  },
};

export default meta;

type Story = StoryObj<typeof Form>;

export const Default: Story = {
  render: () => {
    const FormSchema = z.object({
      username: z.string().min(2, {
        message: 'Too short',
      }),
    });

    const form = useForm<z.infer<typeof FormSchema>>({
      resolver: zodResolver(FormSchema),
      defaultValues: {
        username: '',
      },
    });

    function onSubmit(data: z.infer<typeof FormSchema>) {
      alert(`You submitted the following values: ${JSON.stringify(data)}`);
    }

    return (
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-2/3 space-y-6"
        >
          <Form.Field
            control={form.control}
            name="username"
            render={({ field }) => (
              <Form.Item>
                <Form.Label>Username</Form.Label>
                <Form.Control>
                  <Input
                    className="mt-2 w-80"
                    placeholder="Enter username"
                    {...field}
                  />
                </Form.Control>
                <Form.Description>
                  This is your public display name.
                </Form.Description>
                <Form.Message />
              </Form.Item>
            )}
          />
          <Button type="submit">Submit</Button>
        </form>
      </Form>
    );
  },
};
