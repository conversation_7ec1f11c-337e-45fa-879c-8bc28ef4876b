import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import * as React from 'react';

import { EditIcon, ShareIcon } from '@muraadso/icons';
import { Button } from './button';

const meta: Meta<typeof Button> = {
  title: 'Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
};

export default meta;

type Story = StoryObj<typeof Button>;

export const Default: Story = {
  args: {
    variant: 'default',
    children: 'Button',
  },
};

export const Outline: Story = {
  args: {
    variant: 'outline',
    children: 'Button',
  },
};

export const Ghost: Story = {
  args: {
    variant: 'ghost',
    children: 'Button',
  },
};

export const Link: Story = {
  args: {
    variant: 'link',
    children: 'Button',
  },
};

export const Destructive: Story = {
  args: {
    variant: 'destructive',
    children: 'Button',
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
    children: 'Button',
  },
};

export const Icon: Story = {
  args: {
    variant: 'outline',
    children: [<ShareIcon key={0} size={14} className="aspect-square" />],
  },
};

export const WithIcon: Story = {
  render: () => {
    const [open, setOpen] = React.useState<boolean>(false);
    return (
      <div className="flex gap-4">
        <Button variant="outline">
          <EditIcon key={0} size={14} className="me-3 aspect-square" />
          <span>New</span>
        </Button>

        <Button
          variant="outline"
          size="icon"
          onClick={() => setOpen((prevState) => !prevState)}
          aria-expanded={open}
          aria-label={open ? 'Close menu' : 'Open menu'}
        >
          <svg
            className="pointer-events-none"
            width={16}
            height={16}
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M4 12L20 12"
              className="-translate-y-[7px] origin-center transition-all duration-300 [transition-timing-function:cubic-bezier(.5,.85,.25,1.1)] group-aria-expanded:translate-x-0 group-aria-expanded:translate-y-0 group-aria-expanded:rotate-[315deg]"
            />
            <path
              d="M4 12H20"
              className="origin-center transition-all duration-300 [transition-timing-function:cubic-bezier(.5,.85,.25,1.8)] group-aria-expanded:rotate-45"
            />
            <path
              d="M4 12H20"
              className="origin-center translate-y-[7px] transition-all duration-300 [transition-timing-function:cubic-bezier(.5,.85,.25,1.1)] group-aria-expanded:translate-y-0 group-aria-expanded:rotate-[135deg]"
            />
          </svg>
        </Button>
      </div>
    );
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    children: 'Button',
  },
};

export const Small: Story = {
  args: {
    size: 'sm',
    variant: 'default',
    children: 'Button',
  },
};

export const Large: Story = {
  args: {
    size: 'lg',
    variant: 'default',
    children: 'Button',
  },
};
