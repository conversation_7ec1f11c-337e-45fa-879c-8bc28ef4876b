import { type VariantProps, cva } from 'cva';
import { Slot } from 'radix-ui';
import React, { type ComponentProps } from 'react';

import { SpinnerIcon } from '@muraadso/icons';
import { cn } from '../../utils';

const buttonVariants = cva({
  base: cn(
    'group relative inline-flex cursor-pointer items-center justify-center whitespace-nowrap rounded-lg font-medium text-sm outline-offset-2 transition-colors [&_svg]:pointer-events-none [&_svg]:shrink-0',
    'focus-visible:outline focus-visible:outline-2 focus-visible:outline-ring/70',
    'disabled:pointer-events-none disabled:opacity-50',
  ),
  variants: {
    variant: {
      default: 'bg-primary text-primary-foreground',
      outline:
        'border border-input bg-background hover:border-transparent hover:bg-accent hover:bg-accent hover:text-accent-foreground',
      secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
      ghost: 'hover:bg-accent hover:text-accent-foreground',
      destructive:
        'bg-destructive text-destructive-foreground hover:bg-destructive/90',
      link: 'text-primary underline-offset-4 hover:underline',
    },
    size: {
      default: 'h-9 px-3 py-2',
      sm: 'h-8 rounded-lg px-3 text-xs',
      lg: 'h-10 rounded-lg px-8',
      icon: 'h-9 w-9',
    },
  },
  defaultVariants: {
    variant: 'default',
    size: 'default',
  },
});

type ButtonProps = ComponentProps<'button'> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
    loading?: boolean;
  };

const Button = ({
  asChild = false,
  loading = false,
  type = 'button',
  disabled,
  className,
  children,
  ...props
}: ButtonProps) => {
  const Component = asChild ? Slot.Root : 'button';

  return (
    <Component
      type={type}
      disabled={disabled || loading}
      className={cn(
        buttonVariants({
          variant: props.variant,
          size: props.size,
        }),
        className,
      )}
      {...props}
    >
      {loading && <SpinnerIcon size={16} className="me-2" />}
      {children}
    </Component>
  );
};

export { buttonVariants, Button };
