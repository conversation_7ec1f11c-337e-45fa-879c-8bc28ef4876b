'use client';

import { Separator as SeparatorPrimitive } from 'radix-ui';
import React, { type FC, type ComponentProps } from 'react';
import { cn } from '../../utils';

type SeparatorProps = ComponentProps<typeof SeparatorPrimitive.Root>;

const Separator: FC<SeparatorProps> = ({
  className,
  orientation = 'horizontal',
  decorative = true,
  ...props
}) => {
  return (
    <SeparatorPrimitive.Root
      decorative={decorative}
      orientation={orientation}
      className={cn(
        'shrink-0 bg-border',
        orientation === 'horizontal' ? 'h-px w-full' : 'h-full w-px',
        className,
      )}
      {...props}
    />
  );
};

export { Separator };
