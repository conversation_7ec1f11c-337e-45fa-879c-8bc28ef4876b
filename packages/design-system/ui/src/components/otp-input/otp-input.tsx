'use client';

import { MinusIcon } from '@muraadso/icons';
import { OTPInput as Component, OTPInputContext } from 'input-otp';
import * as React from 'react';

import { cn } from '../../utils';

const Root: React.FC<React.ComponentPropsWithoutRef<typeof Component>> = ({
  className,
  containerClassName,
  ...props
}) => (
  <Component
    containerClassName={cn(
      'flex items-center gap-2 has-[:disabled]:opacity-50',
      containerClassName,
    )}
    className={cn('disabled:cursor-not-allowed', className)}
    {...props}
  />
);

const Group: React.FC<React.ComponentPropsWithoutRef<'div'>> = ({
  className,
  ...props
}) => <div className={cn('flex items-center', className)} {...props} />;

const Slot: React.FC<
  React.ComponentPropsWithoutRef<'div'> & {
    index: number;
  }
> = ({ index, className, ...props }) => {
  const inputOTPContext = React.useContext(OTPInputContext);
  // Ensure slots and index are valid before accessing
  const slotInfo = inputOTPContext?.slots?.[index];
  if (slotInfo === undefined) throw new Error('Invalid Slot index');

  const { char, hasFakeCaret, isActive } = slotInfo;

  return (
    <div
      className={cn(
        'relative flex h-9 w-9 items-center justify-center border-input border-y border-r text-sm shadow-black/5 shadow-sm outline-none transition-all first:rounded-l-lg first:border-l last:rounded-r-lg',
        isActive && 'z-10 border-ring ring-[3px] ring-ring/20',
        className,
      )}
      {...props}
    >
      {char}
      {hasFakeCaret && (
        <div className="pointer-events-none absolute inset-0 flex items-center justify-center">
          <div className="h-4 w-px animate-caret-blink bg-foreground duration-1000" />
        </div>
      )}
    </div>
  );
};

const Separator: React.FC<React.ComponentPropsWithoutRef<'div'>> = ({
  ...props
}) => (
  <div {...props}>
    <MinusIcon className="text-muted-foreground" />
  </div>
);

const OtpInput = Object.assign(Root, {
  Group,
  Slot,
  Separator,
});

export { OtpInput };
