import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';

import { REGEXP_ONLY_DIGITS } from 'input-otp';
import { Label } from '../label';
import { OtpInput } from './otp-input';

const meta: Meta<typeof OtpInput> = {
  title: 'Components/OtpInput',
  component: OtpInput,
  parameters: {
    layout: 'centered',
  },
};

export default meta;

type Story = StoryObj<typeof OtpInput>;

export const Default: Story = {
  render: () => {
    return (
      <div className="grid gap-2">
        <Label htmlFor="simple_otp_input">Simple otp-input</Label>
        <OtpInput pattern={REGEXP_ONLY_DIGITS} maxLength={6}>
          <OtpInput.Group>
            <OtpInput.Slot index={0} />
            <OtpInput.Slot index={1} />
            <OtpInput.Slot index={2} />
          </OtpInput.Group>
          <OtpInput.Separator />
          <OtpInput.Group>
            <OtpInput.Slot index={3} />
            <OtpInput.Slot index={4} />
            <OtpInput.Slot index={5} />
          </OtpInput.Group>
        </OtpInput>
        <p>Hello</p>
      </div>
    );
  },
};
