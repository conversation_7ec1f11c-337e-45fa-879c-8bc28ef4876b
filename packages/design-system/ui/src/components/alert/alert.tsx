'use client';

import { CloseIcon, InfoCircleIcon } from '@muraadso/icons';
import React, { type ComponentPropsWithoutRef } from 'react';
import { cn } from '../../utils';
import { Button } from '../button';

interface AlertProps extends ComponentPropsWithoutRef<'div'> {
  variant?: 'error' | 'success' | 'warning' | 'info';
  dismissible?: boolean;
}

const Alert = ({
  variant = 'info',

  dismissible = false,
  className,
  children,
  ...props
}: AlertProps) => {
  const [dismissed, setDismissed] = React.useState(false);

  // const Icon = {
  //   info: InformationCircleSolid,
  //   error: XCircleSolid,
  //   success: CheckCircleSolid,
  //   warning: ExclamationCircleSolid,
  // }[variant];

  const Icon = InfoCircleIcon;

  const handleDismiss = () => {
    setDismissed(true);
  };

  if (dismissed) {
    return null;
  }

  return (
    <div
      className={cn(
        'grid items-start gap-x-2 text-pretty rounded-lg border border-border bg-muted p-3 text-sm',
        {
          'grid-cols-[20px_1fr]': !dismissible,
          'grid-cols-[20px_1fr_20px]': dismissible,
        },
        className,
      )}
      {...props}
    >
      <Icon
        className={cn('mt-0.5 size-4', {
          'text-destructive': variant === 'error',
          'text-green-500': variant === 'success',
          'text-yellow-500': variant === 'warning',
          'text-muted-foreground': variant === 'info',
        })}
      />
      <div>{children}</div>
      {dismissible && (
        <Button
          className="size-4"
          variant="ghost"
          type="button"
          onClick={handleDismiss}
        >
          <CloseIcon className="size-4 text-muted-foreground" />
        </Button>
      )}
    </div>
  );
};

export { Alert };
