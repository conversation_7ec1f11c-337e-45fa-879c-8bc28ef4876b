'use client';

import { CheckI<PERSON>, ChevronDownIcon, ChevronUpIcon } from '@muraadso/icons';
import { Select as SelectPrimitive } from 'radix-ui';
import React, { type ComponentPropsWithoutRef, type ReactNode } from 'react';
import { cn } from '../../utils';

const Root = SelectPrimitive.Root;

const Group = SelectPrimitive.Group;

const Value = SelectPrimitive.Value;

const Trigger = ({
  className,
  children,
  ...props
}: ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>) => (
  <SelectPrimitive.Trigger
    className={cn(
      'flex h-9 w-full cursor-pointer items-center justify-between rounded-lg border border-input bg-transparent px-3 py-2 text-start font-normal text-sm transition-shadow focus:outline-none data-[placeholder]:text-muted-foreground data-[placeholder]:text-sm [&>span]:line-clamp-1',
      'hover:border-transparent hover:bg-accent',
      'focus-visible:border-ring focus-visible:outline-none focus-visible:ring-[3px] focus-visible:ring-ring/20',
      'disabled:border-muted disabled:bg-muted disabled:text-muted-foreground',
      'disabled:has-[:invalid]:border-destructive',
      'aria-[invalid=true]:!text-destructive aria-[invalid=true]:border-destructive/80 aria-[invalid=true]:focus-visible:border-destructive/80 aria-[invalid=true]:focus-visible:ring-destructive/20',
      className,
    )}
    {...props}
  >
    {children}
    <SelectPrimitive.Icon asChild>
      <ChevronDownIcon className="ml-2 size-4 text-muted-foreground" />
    </SelectPrimitive.Icon>
  </SelectPrimitive.Trigger>
);

const Content = ({
  className,
  children,
  animated = true,
  position = 'popper',
  ...props
}: ComponentPropsWithoutRef<typeof SelectPrimitive.Content> & {
  animated?: boolean;
}) => (
  <SelectPrimitive.Portal>
    <SelectPrimitive.Content
      className={cn(
        animated &&
          'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-in-out-95 data-[state=open]:zoom-in-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 data-[state=closed]:animate-out data-[state=open]:animate-in',
        'relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] origin-[--radix-select-content-transform-origin] overflow-hidden overflow-y-auto overflow-x-hidden rounded-lg border border-input bg-popover text-popover-foreground shadow-md',
        position === 'popper' &&
          'data-[side=left]:-translate-x-1 data-[side=top]:-translate-y-1 data-[side=right]:translate-x-1 data-[side=bottom]:translate-y-1',
        className,
      )}
      position={position}
      {...props}
    >
      <SelectPrimitive.ScrollUpButton
        className={cn('flex cursor-default items-center justify-center py-1')}
      >
        <ChevronUpIcon className="h-4 w-4 text-muted-foreground" />
      </SelectPrimitive.ScrollUpButton>
      <SelectPrimitive.Viewport
        className={cn(
          'p-1',
          position === 'popper' &&
            'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]',
        )}
      >
        {children}
      </SelectPrimitive.Viewport>
      <SelectPrimitive.ScrollDownButton
        className={cn('flex cursor-default items-center justify-center py-1')}
      >
        <ChevronDownIcon className="h-4 w-4" />
      </SelectPrimitive.ScrollDownButton>
    </SelectPrimitive.Content>
  </SelectPrimitive.Portal>
);

const Label = ({
  className,
  ...props
}: ComponentPropsWithoutRef<typeof SelectPrimitive.Label>) => (
  <SelectPrimitive.Label
    className={cn('py-1.5 pr-2 pl-8 font-semibold text-sm', className)}
    {...props}
  />
);

const Item = ({
  className,
  children,
  ...props
}: ComponentPropsWithoutRef<typeof SelectPrimitive.Item>) => (
  <SelectPrimitive.Item
    className={cn(
      'relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pr-2 pl-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
      className,
    )}
    {...props}
  >
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <SelectPrimitive.ItemIndicator>
        <CheckIcon className="h-4 w-4 text-primary" />
      </SelectPrimitive.ItemIndicator>
    </span>

    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>
  </SelectPrimitive.Item>
);

const Separator = ({
  className,
  ...props
}: ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>) => (
  <SelectPrimitive.Separator
    className={cn('-mx-1 my-1 h-px bg-accent', className)}
    {...props}
  />
);

const Select = Object.assign(Root, {
  Trigger,
  Content,
  Label,
  Separator,
  Group,
  Value,
  Item,
});

export { Select };
