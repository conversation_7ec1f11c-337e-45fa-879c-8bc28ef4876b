import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { useRef } from 'react';
import * as React from 'react';
import { Button } from '../button';
import { Input } from '../input';
import { Label } from '../label';
import { Dialog } from './dialog';

const meta: Meta<typeof Dialog> = {
  title: 'Components/Dialog',
  component: Dialog,
  parameters: {
    layout: 'centered',
  },
};

export default meta;

type Story = StoryObj<typeof Dialog>;

export const Default: Story = {
  render: () => {
    return (
      <Dialog defaultOpen={true}>
        <Dialog.Trigger asChild>
          <Button>Open Dialog</Button>
        </Dialog.Trigger>

        <Dialog.Content>
          <Dialog.Body
            title="Create customer"
            description="Make changes to your profile here. Click save when you're done."
          >
            <div className="grid w-full gap-4 py-4">
              <div className="grid w-full grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  type="search"
                  defaultValue="<PERSON>"
                  containerClassName="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="username" className="text-right">
                  Username
                </Label>
                <Input
                  id="username"
                  defaultValue="@peduarte"
                  containerClassName="col-span-3"
                />
              </div>
            </div>
          </Dialog.Body>
          <Dialog.Footer>
            <Button variant="outline">Cancel</Button>
            <Button variant="default">Save changes</Button>
          </Dialog.Footer>
        </Dialog.Content>
      </Dialog>
    );
  },
};
