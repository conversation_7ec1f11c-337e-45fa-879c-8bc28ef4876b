'use client';

import { Dialog as DialogPrimitive } from 'radix-ui';
import React from 'react';
import { cn } from '../../utils';

const Root = (
  props: React.ComponentPropsWithoutRef<typeof DialogPrimitive.Root>,
) => {
  return <DialogPrimitive.Root {...props} />;
};

const Trigger: React.FC<
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Trigger>
> = ({ className, ...props }) => {
  return <DialogPrimitive.Trigger className={cn(className)} {...props} />;
};

const Close: React.FC<
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Close>
> = ({ className, ...props }) => {
  return <DialogPrimitive.Close className={cn(className)} {...props} />;
};

const Portal = DialogPrimitive.Portal;

const Title = DialogPrimitive.Title;

const Description = DialogPrimitive.Description;

const Content: React.FC<
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content> & {
    showOverlyBackground?: boolean;
    showOverlayFilter?: boolean;
  }
> = ({
  className,
  showOverlyBackground = true,
  showOverlayFilter = true,
  ...props
}) => (
  <DialogPrimitive.Portal>
    <DialogPrimitive.Overlay
      className={cn(
        'fixed inset-0 z-50',
        'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 overflow-y-auto data-[state=closed]:animate-out data-[state=open]:animate-in',
        showOverlyBackground ? 'bg-black/30' : 'bg-black/0',
        // showOverlayFilter ? 'backdrop-blur-sm' : 'backdrop-blur-none',
      )}
    />
    <DialogPrimitive.Content
      className={cn(
        '-translate-x-1/2 -translate-y-1/2 fixed top-1/2 left-1/2 z-50 grid max-h-[calc(100%-2rem)] w-full max-w-[calc(100%-2rem)] overflow-y-auto rounded-xl border border-ring bg-accent shadow-lg duration-200 sm:max-w-100',
        'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-in-out-95 data-[state=open]:zoom-in-in-95 data-[state=closed]:animate-out data-[state=open]:animate-in',
        className,
      )}
      {...props}
    />
  </DialogPrimitive.Portal>
);

const Body = React.forwardRef<
  HTMLDivElement,
  React.ComponentPropsWithoutRef<'div'> & {
    title?: string;
    description?: string;
  }
>(({ title, description, children, className, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        'flex-1 gap-y-4 overflow-clip rounded-xl bg-background p-6 shadow shadow-black/[.06] ring-1 ring-black/[0.06]',
        className,
      )}
      {...props}
    >
      <div className="mb-6 flex flex-col space-y-2 text-left">
        <Title className="font-medium text-lg leading-none">{title}</Title>
        <Description className="text-muted-foreground text-sm leading-snug">
          {description}
        </Description>
      </div>
      {children}
    </div>
  );
});

const Footer: React.FC<React.HTMLAttributes<HTMLDivElement>> = ({
  className,
  ...props
}) => (
  <div
    className={cn(
      'flex flex-col-reverse gap-3 rounded-b-xl px-6 py-5 sm:flex-row sm:justify-end',
      className,
    )}
    {...props}
  />
);

const Dialog = Object.assign(Root, {
  Trigger,
  Close,
  Portal,
  Title,
  Description,
  Content,
  Body,
  Footer,
});

export { Dialog };
