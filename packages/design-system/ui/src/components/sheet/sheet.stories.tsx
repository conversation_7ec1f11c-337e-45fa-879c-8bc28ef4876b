import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { useState } from 'react';
import React from 'react';
import { But<PERSON> } from '../button';
import { Sheet } from './sheet';

const meta: Meta<typeof Sheet> = {
  title: 'Components/Sheet',
  component: Sheet,
  parameters: {
    layout: 'centered',
  },
};

export default meta;

type Story = StoryObj<typeof Sheet>;

export const Default: Story = {
  render: () => {
    const [open, setOpen] = useState(false);
    return (
      <Sheet open={open} onOpenChange={setOpen}>
        <Sheet.Trigger asChild>
          <Button onClick={() => setOpen(true)}>Open Sheet</Button>
        </Sheet.Trigger>
        <Sheet.Content>
          <Sheet.Body
            title="Please confirm"
            description="Are you sure to continue?"
          />
          <Sheet.Footer>
            <Button>Hello</Button>
          </Sheet.Footer>
        </Sheet.Content>
      </Sheet>
    );
  },
};
