'use client';

import { CloseIcon } from '@muraadso/icons';
import { Dialog as SheetPrimitive } from 'radix-ui';
import React from 'react';
import { cn } from '../../utils';

const Root = (
  props: React.ComponentPropsWithoutRef<typeof SheetPrimitive.Root>,
) => {
  return <SheetPrimitive.Root {...props} />;
};

const Trigger: React.FC<
  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Trigger>
> = ({ className, ...props }) => {
  return <SheetPrimitive.Trigger className={cn(className)} {...props} />;
};

const Close: React.FC<
  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Close>
> = ({ className, ...props }) => {
  return <SheetPrimitive.Close className={cn(className)} {...props} />;
};

const Title = SheetPrimitive.Title;

const Description = SheetPrimitive.Description;

interface ContentProps
  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content> {
  side?: 'top' | 'right' | 'bottom' | 'left';
  showOverlyBackground?: boolean;
  showOverlayFilter?: boolean;
}

const Content: React.FC<ContentProps> = ({
  side = 'right',
  showOverlyBackground = true,
  showOverlayFilter = true,
  className,
  children,
  ...props
}) => (
  <SheetPrimitive.Portal>
    <SheetPrimitive.Overlay
      className={cn(
        'fixed inset-0 z-50',
        'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:animate-out data-[state=open]:animate-in',
        showOverlyBackground ? 'bg-black/30' : 'bg-black/0',
        // showOverlayFilter ? 'backdrop-blur-xs' : 'backdrop-blur-none',
      )}
    />
    <SheetPrimitive.Content
      className={cn(
        'fixed z-50 flex max-h-[calc(100%-1rem)] w-full max-w-[calc(100%-1rem)] flex-col overflow-y-auto rounded-xl border border-ring bg-accent shadow-black/[.06] shadow-lg transition duration-200 ease-in-out sm:max-w-100',
        'data-[state=closed]:animate-out data-[state=open]:animate-in data-[state=closed]:duration-200 data-[state=open]:duration-200',
        side === 'right' &&
          'data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-2 right-2 w-3/4 sm:max-w-sm',
        side === 'left' &&
          'data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-2 left-2 w-3/4 sm:max-w-sm',
        className,
      )}
      {...props}
    >
      {children}
      {/*<Close className="absolute top-5 right-5 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">*/}
      {/*  <CloseIcon className="size-4" />*/}
      {/*  <span className="sr-only">Close</span>*/}
      {/*</Close>*/}
    </SheetPrimitive.Content>
  </SheetPrimitive.Portal>
);

const Body = React.forwardRef<
  HTMLDivElement,
  React.ComponentPropsWithoutRef<'div'> & {
    title?: string;
    description?: string;
  }
>(({ title, description, children, className, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        'flex-1 gap-y-4 overflow-clip rounded-xl bg-background p-6 shadow shadow-black/[.06] ring-1 ring-black/[0.06]',
        className,
      )}
      {...props}
    >
      <div className="mb-2 flex flex-col space-y-2 text-left">
        <Title className="font-medium text-lg leading-none">{title}</Title>
        <Description className="text-muted-foreground text-sm leading-snug">
          {description}
        </Description>
      </div>
      {children}
    </div>
  );
});

const Footer: React.FC<React.HTMLAttributes<HTMLDivElement>> = ({
  className,
  ...props
}) => (
  <div
    className={cn(
      'flex flex-col-reverse gap-3 rounded-b-xl px-6 py-5 sm:flex-row sm:justify-end',
      className,
    )}
    {...props}
  />
);

const Sheet = Object.assign(Root, {
  Trigger,
  Close,
  Title,
  Description,
  Content,
  Body,
  Footer,
});

export { Sheet };
