import {
  ActivityIcon,
  CalendarIcon,
  // ChartBarIcon,
  // FolderIcon,
  HomeIcon,
  InboxIcon,
  PlusIcon,
  SearchIcon,
  SettingsIcon,
  UserIcon,
} from '@muraadso/icons';
import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';
import { vitest } from 'vitest';
import { Sidebar, SidebarProvider } from './sidebar';

// Mock the useNavigation hook for the custom sidebar
const mockNavigation = {
  mainMenu: [
    {
      title: 'Main',
      items: [
        {
          title: 'Dashboard',
          path: '/dashboard',
          icon: HomeIcon,
        },
        {
          title: 'Analytics',
          path: '/analytics',
          icon: ActivityIcon,
        },
        {
          title: 'Projects',
          path: '/projects',
          icon: CalendarIcon,
        },
      ],
    },
    {
      title: 'Account',
      items: [
        {
          title: 'Profile',
          path: '/profile',
          icon: UserIcon,
        },
        {
          title: 'Settings',
          path: '/settings',
          icon: SettingsIcon,
        },
        {
          title: 'Inbox',
          path: '/inbox',
          icon: InboxIcon,
        },
      ],
    },
  ],
  isActive: (path: string) => path === '/dashboard',
};

// // Mock the useNavigation hook
// vitest.mock('@/hooks/navigation', () => ({
//   useNavigation: () => mockNavigation,
// }));

const meta: Meta<typeof Sidebar> = {
  title: 'Components/Sidebar',
  component: Sidebar,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component:
          'A flexible sidebar component with support for desktop and mobile layouts, collapsible states, and customizable content.',
      },
    },
  },
  decorators: [
    (Story, context) => {
      return (
        <SidebarProvider>
          <div className="flex h-screen">
            <Story />
            <Sidebar.Inset className="flex-1 bg-gray-50 p-8">
              <h1 className="font-bold text-2xl">Main Content</h1>
              <p>This is the main content area next to the sidebar.</p>
            </Sidebar.Inset>
          </div>
        </SidebarProvider>
      );
    },
  ],
  argTypes: {
    side: {
      control: 'select',
      options: ['left', 'right'],
      description: 'Which side the sidebar appears on',
    },
    variant: {
      control: 'select',
      options: ['sidebar', 'floating', 'inset'],
      description: 'Visual style variant of the sidebar',
    },
    collapsible: {
      control: 'select',
      options: ['offcanvas', 'icon', 'none'],
      description: 'How the sidebar behaves when collapsed',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Sidebar>;

// // Custom Sidebar Stories (using the project's custom implementation)
// export const CustomDefault: Story = {
//   name: 'Custom Sidebar - Default',
//   render: () => {
//     // Import the custom sidebar component
//     const { Sidebar: CustomSidebar } = require('./sidebar');
//     return <CustomSidebar />;
//   },
//   parameters: {
//     docs: {
//       description: {
//         story: 'The default custom sidebar with navigation menu and logo.',
//       },
//     },
//   },
// };

// UI Component Sidebar Stories
export const Default: Story = {
  args: {
    side: 'left',
    variant: 'sidebar',
    collapsible: 'offcanvas',
  },
  render: (args) => (
    <Sidebar {...args} collapsible="icon">
      <Sidebar.Header>
        <div className="flex items-center gap-2 px-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
            <span className="font-bold text-primary-foreground text-sm">A</span>
          </div>
          <span className="font-semibold">Acme Inc</span>
        </div>
      </Sidebar.Header>

      <Sidebar.Content>
        <Sidebar.Group>
          <Sidebar.GroupLabel>Platform</Sidebar.GroupLabel>
          <Sidebar.GroupContent>
            <Sidebar.Menu>
              <Sidebar.MenuItem>
                <Sidebar.MenuButton>
                  <HomeIcon />
                  <span>Dashboard</span>
                </Sidebar.MenuButton>
              </Sidebar.MenuItem>
              <Sidebar.MenuItem>
                <Sidebar.MenuButton isActive>
                  <CalendarIcon />
                  <span>Projects</span>
                </Sidebar.MenuButton>
              </Sidebar.MenuItem>
              <Sidebar.MenuItem>
                <Sidebar.MenuButton>
                  <ActivityIcon />
                  <span>Analytics</span>
                </Sidebar.MenuButton>
              </Sidebar.MenuItem>
            </Sidebar.Menu>
          </Sidebar.GroupContent>
        </Sidebar.Group>

        <Sidebar.Group>
          <Sidebar.GroupLabel>Account</Sidebar.GroupLabel>
          <Sidebar.GroupContent>
            <Sidebar.Menu>
              <Sidebar.MenuItem>
                <Sidebar.MenuButton>
                  <UserIcon />
                  <span>Profile</span>
                </Sidebar.MenuButton>
              </Sidebar.MenuItem>
              <Sidebar.MenuItem>
                <Sidebar.MenuButton>
                  <SettingsIcon />
                  <span>Settings</span>
                </Sidebar.MenuButton>
              </Sidebar.MenuItem>
            </Sidebar.Menu>
          </Sidebar.GroupContent>
        </Sidebar.Group>
      </Sidebar.Content>

      <Sidebar.Footer>
        <Sidebar.Menu>
          <Sidebar.MenuItem>
            <Sidebar.MenuButton>
              <UserIcon />
              <span>John Doe</span>
            </Sidebar.MenuButton>
          </Sidebar.MenuItem>
        </Sidebar.Menu>
      </Sidebar.Footer>
      <Sidebar.Rail />
    </Sidebar>
  ),
};

// export const WithSearch: Story = {
//   args: {
//     side: 'left',
//     variant: 'sidebar',
//     collapsible: 'icon',
//   },
//   render: (args) => (
//     <Sidebar {...args}>
//       <Sidebar.Header>
//         <div className="flex items-center gap-2 px-2">
//           <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
//             <span className="font-bold text-primary-foreground text-sm">A</span>
//           </div>
//           <span className="font-semibold">Acme Inc</span>
//         </div>
//         <div className="relative">
//           <SearchIcon className="absolute top-2 left-2 h-4 w-4 text-muted-foreground" />
//           <Sidebar.Input placeholder="Search..." className="pl-8" />
//         </div>
//       </Sidebar.Header>
//
//       <Sidebar.Content>
//         <Sidebar.Group>
//           <Sidebar.GroupLabel>Platform</Sidebar.GroupLabel>
//           <Sidebar.GroupContent>
//             <Sidebar.Menu>
//               <Sidebar.MenuItem>
//                 <Sidebar.MenuButton tooltip="Dashboard">
//                   <HomeIcon />
//                   <span>Dashboard</span>
//                 </Sidebar.MenuButton>
//               </Sidebar.MenuItem>
//               <Sidebar.MenuItem>
//                 <Sidebar.MenuButton tooltip="Projects" isActive>
//                   <CalendarIcon />
//                   <span>Projects</span>
//                 </Sidebar.MenuButton>
//               </Sidebar.MenuItem>
//             </Sidebar.Menu>
//           </Sidebar.GroupContent>
//         </Sidebar.Group>
//       </Sidebar.Content>
//     </Sidebar>
//   ),
// };
//
// export const WithMenuActions: Story = {
//   args: {
//     side: 'left',
//     variant: 'sidebar',
//     collapsible: 'offcanvas',
//   },
//   render: (args) => (
//     <Sidebar {...args}>
//       <Sidebar.Header>
//         <div className="flex items-center gap-2 px-2">
//           <span className="font-semibold">Project Manager</span>
//         </div>
//       </Sidebar.Header>
//
//       <Sidebar.Content>
//         <Sidebar.Group>
//           <Sidebar.GroupLabel>Projects</Sidebar.GroupLabel>
//           <Sidebar.GroupAction>
//             <PlusIcon />
//           </Sidebar.GroupAction>
//           <Sidebar.GroupContent>
//             <Sidebar.Menu>
//               <Sidebar.MenuItem>
//                 <Sidebar.MenuButton>
//                   <CalendarIcon />
//                   <span>Project Alpha</span>
//                 </Sidebar.MenuButton>
//                 <Sidebar.MenuAction showOnHover>
//                   <PlusIcon />
//                 </Sidebar.MenuAction>
//               </Sidebar.MenuItem>
//               <Sidebar.MenuItem>
//                 <Sidebar.MenuButton>
//                   <CalendarIcon />
//                   <span>Project Beta</span>
//                 </Sidebar.MenuButton>
//                 <Sidebar.MenuBadge>3</Sidebar.MenuBadge>
//               </Sidebar.MenuItem>
//             </Sidebar.Menu>
//           </Sidebar.GroupContent>
//         </Sidebar.Group>
//       </Sidebar.Content>
//     </Sidebar>
//   ),
// };
//
// export const WithSubmenus: Story = {
//   args: {
//     side: 'left',
//     variant: 'sidebar',
//     collapsible: 'icon',
//   },
//   render: (args) => (
//     <Sidebar {...args}>
//       <Sidebar.Header>
//         <div className="flex items-center gap-2 px-2">
//           <span className="font-semibold">Navigation</span>
//         </div>
//       </Sidebar.Header>
//
//       <Sidebar.Content>
//         <Sidebar.Group>
//           <Sidebar.GroupContent>
//             <Sidebar.Menu>
//               <Sidebar.MenuItem>
//                 <Sidebar.MenuButton>
//                   <HomeIcon />
//                   <span>Dashboard</span>
//                 </Sidebar.MenuButton>
//               </Sidebar.MenuItem>
//               <Sidebar.MenuItem>
//                 <Sidebar.MenuButton>
//                   <CalendarIcon />
//                   <span>Projects</span>
//                 </Sidebar.MenuButton>
//                 <Sidebar.MenuSub>
//                   <Sidebar.MenuSubItem>
//                     <Sidebar.MenuSubButton href="#" isActive>
//                       <span>Project Alpha</span>
//                     </Sidebar.MenuSubButton>
//                   </Sidebar.MenuSubItem>
//                   <Sidebar.MenuSubItem>
//                     <Sidebar.MenuSubButton href="#">
//                       <span>Project Beta</span>
//                     </Sidebar.MenuSubButton>
//                   </Sidebar.MenuSubItem>
//                   <Sidebar.MenuSubItem>
//                     <Sidebar.MenuSubButton href="#">
//                       <span>Project Gamma</span>
//                     </Sidebar.MenuSubButton>
//                   </Sidebar.MenuSubItem>
//                 </Sidebar.MenuSub>
//               </Sidebar.MenuItem>
//             </Sidebar.Menu>
//           </Sidebar.GroupContent>
//         </Sidebar.Group>
//       </Sidebar.Content>
//     </Sidebar>
//   ),
// };
//
// export const FloatingVariant: Story = {
//   args: {
//     side: 'left',
//     variant: 'floating',
//     collapsible: 'icon',
//   },
//   render: (args) => (
//     <Sidebar {...args}>
//       <Sidebar.Header>
//         <div className="flex items-center gap-2 px-2">
//           <span className="font-semibold">Floating Sidebar</span>
//         </div>
//       </Sidebar.Header>
//
//       <Sidebar.Content>
//         <Sidebar.Group>
//           <Sidebar.GroupContent>
//             <Sidebar.Menu>
//               <Sidebar.MenuItem>
//                 <Sidebar.MenuButton tooltip="Dashboard">
//                   <HomeIcon />
//                   <span>Dashboard</span>
//                 </Sidebar.MenuButton>
//               </Sidebar.MenuItem>
//               <Sidebar.MenuItem>
//                 <Sidebar.MenuButton tooltip="Analytics">
//                   <ActivityIcon />
//                   <span>Analytics</span>
//                 </Sidebar.MenuButton>
//               </Sidebar.MenuItem>
//             </Sidebar.Menu>
//           </Sidebar.GroupContent>
//         </Sidebar.Group>
//       </Sidebar.Content>
//     </Sidebar>
//   ),
// };
//
// export const RightSide: Story = {
//   args: {
//     side: 'right',
//     variant: 'sidebar',
//     collapsible: 'offcanvas',
//   },
//   render: (args) => (
//     <Sidebar {...args}>
//       <Sidebar.Header>
//         <div className="flex items-center gap-2 px-2">
//           <span className="font-semibold">Right Sidebar</span>
//         </div>
//       </Sidebar.Header>
//
//       <Sidebar.Content>
//         <Sidebar.Group>
//           <Sidebar.GroupLabel>Actions</Sidebar.GroupLabel>
//           <Sidebar.GroupContent>
//             <Sidebar.Menu>
//               <Sidebar.MenuItem>
//                 <Sidebar.MenuButton>
//                   <SettingsIcon />
//                   <span>Settings</span>
//                 </Sidebar.MenuButton>
//               </Sidebar.MenuItem>
//               <Sidebar.MenuItem>
//                 <Sidebar.MenuButton>
//                   <UserIcon />
//                   <span>Profile</span>
//                 </Sidebar.MenuButton>
//               </Sidebar.MenuItem>
//             </Sidebar.Menu>
//           </Sidebar.GroupContent>
//         </Sidebar.Group>
//       </Sidebar.Content>
//     </Sidebar>
//   ),
// };
//
// export const LoadingState: Story = {
//   args: {
//     side: 'left',
//     variant: 'sidebar',
//     collapsible: 'offcanvas',
//   },
//   render: (args) => (
//     <Sidebar {...args}>
//       <Sidebar.Header>
//         <div className="flex items-center gap-2 px-2">
//           <span className="font-semibold">Loading...</span>
//         </div>
//       </Sidebar.Header>
//
//       <Sidebar.Content>
//         <Sidebar.Group>
//           <Sidebar.GroupLabel>Platform</Sidebar.GroupLabel>
//           <Sidebar.GroupContent>
//             <Sidebar.Menu>
//               <Sidebar.MenuItem>
//                 <Sidebar.MenuSkeleton showIcon />
//               </Sidebar.MenuItem>
//               <Sidebar.MenuItem>
//                 <Sidebar.MenuSkeleton showIcon />
//               </Sidebar.MenuItem>
//               <Sidebar.MenuItem>
//                 <Sidebar.MenuSkeleton />
//               </Sidebar.MenuItem>
//             </Sidebar.Menu>
//           </Sidebar.GroupContent>
//         </Sidebar.Group>
//       </Sidebar.Content>
//     </Sidebar>
//   ),
// };
//
// export const WithTrigger: Story = {
//   args: {
//     side: 'left',
//     variant: 'sidebar',
//     collapsible: 'offcanvas',
//   },
//   render: (args) => (
//     <>
//       <Sidebar {...args}>
//         <Sidebar.Header>
//           <div className="flex items-center gap-2 px-2">
//             <Sidebar.Trigger />
//             <span className="font-semibold">Collapsible</span>
//           </div>
//         </Sidebar.Header>
//
//         <Sidebar.Content>
//           <Sidebar.Group>
//             <Sidebar.GroupContent>
//               <Sidebar.Menu>
//                 <Sidebar.MenuItem>
//                   <Sidebar.MenuButton>
//                     <HomeIcon />
//                     <span>Dashboard</span>
//                   </Sidebar.MenuButton>
//                 </Sidebar.MenuItem>
//                 <Sidebar.MenuItem>
//                   <Sidebar.MenuButton>
//                     <CalendarIcon />
//                     <span>Projects</span>
//                   </Sidebar.MenuButton>
//                 </Sidebar.MenuItem>
//               </Sidebar.Menu>
//             </Sidebar.GroupContent>
//           </Sidebar.Group>
//         </Sidebar.Content>
//       </Sidebar>
//       <Sidebar.Rail />
//     </>
//   ),
//   parameters: {
//     docs: {
//       description: {
//         story:
//           'Sidebar with trigger button and rail for easy toggling. Try clicking the trigger button or hover over the rail when collapsed.',
//       },
//     },
//   },
// };
