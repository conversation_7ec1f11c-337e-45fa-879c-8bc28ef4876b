{"name": "@muraadso/icons", "description": "Icons library", "exports": {".": "./src/index.ts"}, "scripts": {"lint": "biome lint \"**/*.ts*\""}, "devDependencies": {"@muraadso/tsconfig": "workspace:*", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "rimraf": "^6.0.1", "rollup": "^4.34.5", "rollup-plugin-esbuild": "^6.2.0", "typescript": "^5", "vite": "^6.1.0"}, "peerDependencies": {"react": "^19"}}