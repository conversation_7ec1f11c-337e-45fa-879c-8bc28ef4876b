import { getRandomDate } from './utils';

export const customersData = [
  {
    name: 'Nu<PERSON> Daahir',
    phone: '+252634567890',
    email: '<EMAIL>',
    image: null,
  },
  {
    name: '<PERSON>aarax Cismaan',
    phone: '+252634567891',
    email: '<EMAIL>',
    image: null,
  },
  {
    name: '<PERSON><PERSON>',
    phone: '+252634567892',
    email: '<EMAIL>',
    image: null,
  },
  {
    name: '<PERSON>',
    phone: '+252634567893',
    email: '<EMAIL>',
    image: null,
  },
  {
    name: '<PERSON><PERSON>',
    phone: '+252634567894',
    email: '<EMAIL>',
    image: null,
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    phone: '+252634567895',
    email: 'abdira<PERSON>.<EMAIL>',
    image: null,
  },
  {
    name: '<PERSON><PERSON>',
    phone: '+252634567896',
    email: null,
    image: null,
  },
  {
    name: '<PERSON>',
    phone: '+252634567897',
    email: 'ahmed.<PERSON><PERSON><PERSON>@example.com',
    image: null,
  },
  {
    name: '<PERSON>tun <PERSON>i',
    phone: '+252634567898',
    email: '<EMAIL>',
    image: null,
  },
  {
    name: '<PERSON>i <PERSON>',
    phone: '+252634567899',
    email: null,
    image: null,
  },
  {
    name: 'Maryam <PERSON>a',
    phone: '+252634567800',
    email: '<EMAIL>',
    image: null,
  },
  {
    name: '<PERSON> Farah',
    phone: '+252634567801',
    email: null,
    image: null,
  },
  {
    name: 'Asha Osman',
    phone: '+252634567802',
    email: '<EMAIL>',
    image: null,
  },
];

export type CustomersData = {
  id: string;
  name: string;
  phone: string;
  email?: string | null;
  image?: string | null;
  userId?: string | null;
};

export const generateCustomersData = (users: { id: string }[]) => {
  return customersData.map((customer, index) => {
    const shouldHaveUser = customer.email && index < users.length;

    return {
      ...customer,
      userId: shouldHaveUser ? users[index]?.id || null : null,
      createdAt: getRandomDate(),
    };
  });
};
