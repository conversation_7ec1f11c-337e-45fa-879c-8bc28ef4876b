export const getRandomDate = (): string => {
  const now = new Date();
  const past = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate()); // Approximately one year ago
  const randomTime =
    past.getTime() + Math.random() * (now.getTime() - past.getTime());
  return new Date(randomTime).toISOString();
};

export const findBySlug = <T extends { slug: string }>(
  entities: T[],
  slug: string,
): T => {
  const entity = entities.find((e) => e.slug === slug);
  if (!entity) {
    throw new Error(`Entity with slug "${slug}" not found.`);
  }
  return entity;
};
