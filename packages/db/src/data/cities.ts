import type { CountriesData } from './countries';
import { getRandomDate } from './utils';

export interface CitiesData {
  id: string;
  name: string;
  countryId: string;
  createdAt: string;
}

export const sampleCitiesByCountry: Record<string, string[]> = {
  Somalia: ['Hargeisa', 'Garowe', 'Berbera', 'Burao'],
  Ethiopia: ['Dire Dawa'],
};

export const generateCitiesData = (countries: CountriesData[]) => {
  const cities = [];

  for (const country of countries) {
    const countryCities = sampleCitiesByCountry[country.name] || [];
    for (const cityName of countryCities) {
      cities.push({
        name: cityName,
        countryId: country.id,
        createdAt: getRandomDate(),
      });
    }
  }

  return cities;
};
