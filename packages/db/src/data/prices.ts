import type { VariantsData } from './variants';

export const generatePricesData = (
  variants: VariantsData[],
) => {
  return variants.map((variant) => {
    // Base cost in cents
    const cost = Math.floor(Math.random() * 45000) + 5000;

    // Calculate different price points with different markups
    const retailMarkup = 1 + (Math.random() * 0.3 + 0.2); // 20-50% markup
    const buybackMarkup = 0.6 + (Math.random() * 0.1); // 60-70% of cost
    const consignmentMarkup = 0.7 + (Math.random() * 0.1); // 70-80% of cost
    const tradeInMarkup = 0.5 + (Math.random() * 0.1); // 50-60% of cost

    // Calculate prices in cents
    const retailPrice = Math.floor(cost * retailMarkup);
    const buybackPrice = Math.floor(cost * buybackMarkup);
    const consignmentPrice = Math.floor(cost * consignmentMarkup);
    const tradeInPrice = Math.floor(cost * tradeInMarkup);

    return {
      variantId: variant.id,
      tradeInPrice: tradeInPrice / 100, // Convert cents to dollars
      consignmentPrice: consignmentPrice / 100,
      buybackPrice: buybackPrice / 100,
      retailPrice: retailPrice / 100,
    };
  });
};
