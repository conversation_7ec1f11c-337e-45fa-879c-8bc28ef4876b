import type { BrandsData } from './brands';
import type { CategoriesData } from './categories';

export const generateBrandCategoriesData = (
  brands: BrandsData[],
  categories: CategoriesData[],
) => {
  const findBySlug = <T extends { slug: string }>(
    entities: T[],
    slug: string,
  ): T => {
    const entity = entities.find((e) => e.slug === slug);
    if (!entity) {
      throw new Error(`Entity with slug "${slug}" not found.`);
    }
    return entity;
  };

  // Define the relationships between brands and categories
  const relationships = [
    { brandSlug: 'apple', categorySlug: 'mobile-phones' },
    { brandSlug: 'apple', categorySlug: 'watches' },
    { brandSlug: 'apple', categorySlug: 'laptops' },
    { brandSlug: 'google', categorySlug: 'mobile-phones' },
    { brandSlug: 'google', categorySlug: 'watches' },
  ];

  // Generate the data for insertion
  return relationships.map((rel) => ({
    brandId: findBySlug(brands, rel.brandSlug).id,
    categoryId: findBySlug(categories, rel.categorySlug).id,
  }));
};
