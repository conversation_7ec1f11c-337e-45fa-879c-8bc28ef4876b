import type { OptionValuesData } from './options';
import type { ProductsData } from './products';

export const generateVariantsData = (
  products: ProductsData[],
  optionValues: OptionValuesData[],
) => {
  const findProductBySlug = (slug: string): ProductsData => {
    const product = products.find((p) => p.slug === slug);
    if (!product) {
      throw new Error(`Product with slug "${slug}" not found.`);
    }
    return product;
  };

  const findOptionValueByKey = (key: string): OptionValuesData => {
    const optionValue = optionValues.find((ov) => ov._key === key);
    if (!optionValue) {
      throw new Error(`Option value with key "${key}" not found.`);
    }
    return optionValue;
  };

  // Define variant data
  const variantsData = [
    // Original Products
    {
      productSlug: 'iphone-13',
      variants: [
        { storage: '128GB', color: 'Midnight', sku: 'IP13-128-MID' },
        {
          storage: '128GB',
          color: 'Starlight',
          sku: 'IP13-128-STL',
        },
        { storage: '256GB', color: 'Blue', sku: 'IP13-256-BLU' },
        { storage: '256GB', color: 'Pink', sku: 'IP13-256-PNK' },
        { storage: '512GB', color: 'Red', sku: 'IP13-512-RED' },
      ],
    },
    {
      productSlug: 'macbook-air-m2',
      variants: [
        {
          storage: '256GB SSD',
          ram: '8GB unified memory',
          color: 'Space Gray',
          sku: 'MBA-M2-256-8-SG',
        },
        {
          storage: '512GB SSD',
          ram: '8GB unified memory',
          color: 'Starlight',
          sku: 'MBA-M2-512-8-STL',
        },
        {
          storage: '512GB SSD',
          ram: '16GB unified memory',
          color: 'Midnight',
          sku: 'MBA-M2-512-16-MID',
        },
        {
          storage: '1TB SSD',
          ram: '16GB unified memory',
          color: 'Silver',
          sku: 'MBA-M2-1T-16-SIL',
        },
      ],
    },
    // New Products with variable variants
    {
      productSlug: 'samsung-galaxy-s23-ultra',
      variants: [
        {
          storage: '256GB',
          color: 'Phantom Black',
          sku: 'S23U-256-PBK',
        },
        { storage: '256GB', color: 'Green', sku: 'S23U-256-GRN' },
        { storage: '512GB', color: 'Cream', sku: 'S23U-512-CRM' },
        {
          storage: '1TB',
          color: 'Phantom Black',
          sku: 'S23U-1T-PBK',
        },
      ],
    },
    {
      productSlug: 'dell-xps-15',
      variants: [
        {
          processor: 'Intel Core i7',
          ram: '16GB DDR5',
          storage: '512GB SSD',
          sku: 'XPS15-I7-16-512',
        },
        {
          processor: 'Intel Core i7',
          ram: '32GB DDR5',
          storage: '1TB SSD',
          sku: 'XPS15-I7-32-1T',
        },
        {
          processor: 'Intel Core i9',
          ram: '16GB DDR5',
          storage: '1TB SSD',
          sku: 'XPS15-I9-16-1T',
        },
        {
          processor: 'Intel Core i9',
          ram: '32GB DDR5',
          storage: '2TB SSD',
          sku: 'XPS15-I9-32-2T',
        },
        {
          processor: 'Intel Core i9',
          ram: '64GB DDR5',
          storage: '2TB SSD',
          sku: 'XPS15-I9-64-2T',
        },
      ],
    },
    {
      productSlug: 'sony-wh-1000xm5',
      variants: [
        { color: 'Black', sku: 'WHXM5-BLK' },
        { color: 'Silver', sku: 'WHXM5-SIL' },
        { color: 'Midnight Blue', sku: 'WHXM5-BLU' },
      ],
    },
    {
      productSlug: 'apple-watch-series-8',
      variants: [
        {
          caseSize: '41mm',
          caseMaterial: 'Aluminum',
          color: 'Midnight',
          sku: 'AW8-41-AL-MID',
        },
        {
          caseSize: '45mm',
          caseMaterial: 'Aluminum',
          color: 'Starlight',
          sku: 'AW8-45-AL-STL',
        },
        {
          caseSize: '41mm',
          caseMaterial: 'Stainless Steel',
          color: 'Silver',
          sku: 'AW8-41-SS-SIL',
        },
        {
          caseSize: '45mm',
          caseMaterial: 'Stainless Steel',
          color: 'Graphite',
          sku: 'AW8-45-SS-GRA',
        },
      ],
    },
    {
      productSlug: 'google-pixel-7-pro',
      variants: [
        { storage: '128GB', color: 'Obsidian', sku: 'P7P-128-OBS' },
        { storage: '256GB', color: 'Snow', sku: 'P7P-256-SNO' },
        { storage: '512GB', color: 'Hazel', sku: 'P7P-512-HAZ' },
      ],
    },
    {
      productSlug: 'lg-c2-oled-tv',
      variants: [
        { size: '42-inch', sku: 'LGC2-42' },
        { size: '55-inch', sku: 'LGC2-55' },
        { size: '65-inch', sku: 'LGC2-65' },
        { size: '77-inch', sku: 'LGC2-77' },
      ],
    },
    {
      productSlug: 'bose-qc-earbuds-ii',
      variants: [
        { color: 'Triple Black', sku: 'QCE2-TBK' },
        { color: 'Soapstone', sku: 'QCE2-SST' },
      ],
    },
    {
      productSlug: 'gopro-hero11-black',
      variants: [
        { bundle: 'Standard', sku: 'H11B-STD' },
        { bundle: 'Creator Edition', sku: 'H11B-CRE' },
      ],
    },
    {
      productSlug: 'canon-eos-r6-mark-ii',
      variants: [{ kit: 'Body Only', sku: 'EOSR6M2-BODY' }],
    },
    {
      productSlug: 'microsoft-surface-laptop-5',
      variants: [
        {
          size: '13.5-inch',
          color: 'Platinum',
          ram: '8GB',
          storage: '256GB SSD',
          sku: 'SL5-13-PL-8-256',
        },
        {
          size: '13.5-inch',
          color: 'Matte Black',
          ram: '16GB',
          storage: '512GB SSD',
          sku: 'SL5-13-MB-16-512',
        },
        {
          size: '15-inch',
          color: 'Platinum',
          ram: '16GB',
          storage: '512GB SSD',
          sku: 'SL5-15-PL-16-512',
        },
        {
          size: '15-inch',
          color: 'Matte Black',
          ram: '32GB',
          storage: '1TB SSD',
          sku: 'SL5-15-MB-32-1T',
        },
      ],
    },
    {
      productSlug: 'ipad-pro',
      variants: [
        {
          size: '11-inch',
          storage: '128GB',
          connectivity: 'Wi-Fi',
          color: 'Space Gray',
          sku: 'IPADPRO-11-128-W-SG',
        },
        {
          size: '11-inch',
          storage: '256GB',
          connectivity: 'Wi-Fi + Cellular',
          color: 'Silver',
          sku: 'IPADPRO-11-256-C-SIL',
        },
        {
          size: '12.9-inch',
          storage: '256GB',
          connectivity: 'Wi-Fi',
          color: 'Silver',
          sku: 'IPADPRO-129-256-W-SIL',
        },
        {
          size: '12.9-inch',
          storage: '512GB',
          connectivity: 'Wi-Fi + Cellular',
          color: 'Space Gray',
          sku: 'IPADPRO-129-512-C-SG',
        },
        {
          size: '12.9-inch',
          storage: '1TB',
          connectivity: 'Wi-Fi + Cellular',
          color: 'Silver',
          sku: 'IPADPRO-129-1T-C-SIL',
        },
      ],
    },
    {
      productSlug: 'samsung-odyssey-g9',
      variants: [
        { model: 'G9', sku: 'ODYS-G9' },
        { model: 'Neo G9', sku: 'ODYS-NEOG9' },
      ],
    },
    {
      productSlug: 'playstation-5',
      variants: [
        { edition: 'Standard', sku: 'PS5-STD' },
        { edition: 'Digital', sku: 'PS5-DIG' },
      ],
    },
  ];

  // Prepare data for insertion
  const variantsToInsert = [];
  const variantOptionValuesMap = new Map<string, string[]>();

  // Process each product's variants
  for (const productVariants of variantsData) {
    const productId = findProductBySlug(productVariants.productSlug).id;
    const productSlug = productVariants.productSlug;

    for (const variant of productVariants.variants) {
      // Create a unique name for the variant
      let variantName = '';
      const optionValueKeys = [];

      // Add storage to name and collect option value keys
      if ('storage' in variant) {
        variantName += `${variant.storage} / `;
        optionValueKeys.push(`${productSlug}:Storage:${variant.storage}`);
      }

      // Add ram to name and collect option value keys
      if ('ram' in variant) {
        variantName += `${variant.ram} / `;
        optionValueKeys.push(`${productSlug}:RAM:${variant.ram}`);
      }

      // Add color to name and collect option value keys
      if ('color' in variant) {
        variantName += `${variant.color} / `;
        optionValueKeys.push(`${productSlug}:Color:${variant.color}`);
      }

      // Add connectivity to name and collect option value keys
      if ('connectivity' in variant) {
        variantName += `${variant.connectivity} / `;
        optionValueKeys.push(
          `${productSlug}:Connectivity:${variant.connectivity}`,
        );
      }
      // Add size to name and collect option value keys
      if ('size' in variant) {
        variantName += `${variant.size} / `;
        optionValueKeys.push(`${productSlug}:Size:${variant.size}`);
      }

      // Add processor to name and collect option value keys
      if ('processor' in variant) {
        variantName += `${variant.processor} / `;
        optionValueKeys.push(`${productSlug}:Processor:${variant.processor}`);
      }

      // Add caseSize to name and collect option value keys
      if ('caseSize' in variant) {
        variantName += `${variant.caseSize} / `;
        optionValueKeys.push(`${productSlug}:Case Size:${variant.caseSize}`);
      }

      // Add caseMaterial to name and collect option value keys
      if ('caseMaterial' in variant) {
        variantName += `${variant.caseMaterial} / `;
        optionValueKeys.push(
          `${productSlug}:Case Material:${variant.caseMaterial}`,
        );
      }

      // Add bundle to name and collect option value keys
      if ('bundle' in variant) {
        variantName += `${variant.bundle} / `;
        optionValueKeys.push(`${productSlug}:Bundle:${variant.bundle}`);
      }

      // Add kit to name and collect option value keys
      if ('kit' in variant) {
        variantName += `${variant.kit} / `;
        optionValueKeys.push(`${productSlug}:Kit:${variant.kit}`);
      }

      // Add model to name and collect option value keys
      if ('model' in variant) {
        variantName += `${variant.model} / `;
        optionValueKeys.push(`${productSlug}:Model:${variant.model}`);
      }

      // Add edition to name and collect option value keys
      if ('edition' in variant) {
        variantName += `${variant.edition} / `;
        optionValueKeys.push(`${productSlug}:Edition:${variant.edition}`);
      }

      // Remove trailing slash and space
      variantName = variantName.slice(0, -3);

      // Add variant to insert list
      variantsToInsert.push({
        name: variantName,
        sku: variant.sku,
        productId: productId,
        _key: variant.sku, // Use SKU as a unique key
      });

      // Store option value keys for this variant
      variantOptionValuesMap.set(variant.sku, optionValueKeys);
    }
  }

  return {
    variants: variantsToInsert,
    getVariantOptionValuesData: (insertedVariants: VariantsData[]) => {
      // Generate variant option values data based on the inserted variants
      const variantOptionValuesToInsert = [];

      for (const variant of insertedVariants) {
        const optionValueKeys = variantOptionValuesMap.get(variant._key);

        if (!optionValueKeys) {
          throw new Error(
            `Option value keys for variant with SKU "${variant._key}" not found.`,
          );
        }

        for (const key of optionValueKeys) {
          variantOptionValuesToInsert.push({
            variantId: variant.id,
            optionValueId: findOptionValueByKey(key).id,
          });
        }
      }

      return variantOptionValuesToInsert;
    },
  };
};

export type VariantsData = {
  id: string;
  name: string;
  sku: string;
  productId: string;
  _key: string; // Used to find the variant later
};
