import type { CitiesData } from './cities';
import { getRandomDate } from './utils';

export interface DistrictsData {
  id: string;
  name: string;
  cityId: string;
  createdAt: string;
}

export const sampleDistricts: Record<string, string[]> = {
  // Somalia
  Hargeisa: [
    '26 June',
    '<PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    'October',
    "Sha'ab",
  ],
  Garowe: ['Garowe Central', 'Garowe East', 'Garowe West'],
  Berbera: ['Berbera Central', 'Berbera Port', 'Berbera East'],
  Burao: ['Burao Central', 'Burao East', 'Burao West'],

  // Ethiopia
  'Dire Dawa': ['Dire Dawa Central', 'Dire Dawa East', 'Dire Dawa West'],
};

export const generateDistrictsData = (cities: CitiesData[]) => {
  const districts = [];
  for (const city of cities) {
    const cityDistricts = sampleDistricts[city.name] || [];

    for (const districtName of cityDistricts) {
      districts.push({
        name: districtName,
        cityId: city.id,
        createdAt: getRandomDate(),
      });
    }
  }

  return districts;
};
