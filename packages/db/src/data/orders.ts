import type { CustomersData } from './customers';
import type { LocationsData } from './locations';
import { getRandomDate } from './utils';

// Sample addresses in Somalia/Somaliland
const sampleAddresses = [
  'Jidka Xoriyada, Hargeisa, Somaliland',
  'Suuqa Weyn, Hargeisa, Somaliland',
  'Jidka Silanyo, Hargeisa, Somaliland',
  'Wadada Berbera, Hargeisa, Somaliland',
  'Suuqa Burco, Burco, Somaliland',
  'Jidka Wadada Weyn, Burco, Somaliland',
  'Suuqa Borama, Borama, Somaliland',
  'Jidka Dilla, Borama, Somaliland',
  'Suuqa Jigjiga, Jigjiga, Ethiopia',
  'Wadada Harar, Jigjiga, Ethiopia',
];

// Sample notes for different order types
const sampleNotes = [
  'Customer requested express delivery',
  'Item condition: Excellent, original packaging included',
  'Customer preferred pickup over delivery',
  'Special handling required for fragile items',
  'Customer trade-in approved after quality check',
  'Consignment terms: 30-day listing period',
  'Buyback evaluation completed - Grade A condition',
  null, // Some orders won't have notes
  null,
  null,
];

export const generateOrdersData = (
  customers: CustomersData[],
  locations: LocationsData[],
) => {
  const orders = [];

  // Create diverse orders for different customers
  const orderScenarios = [
    // Retail Orders
    {
      serviceType: 'retail' as const,
      status: 'completed' as const,
      qchStatus: 'none' as const,
      totalAmount: 88.35,
      note: 'Customer requested express delivery',
      isPaid: null,
      paidAt: null,
      commissionRate: 0,
    },
    {
      serviceType: 'retail' as const,
      status: 'pending' as const,
      qchStatus: 'none' as const,
      totalAmount: 12.99,
      note: null,
      isPaid: null,
      paidAt: null,
      commissionRate: 0,
    },
    {
      serviceType: 'retail' as const,
      status: 'pending' as const,
      qchStatus: 'none' as const,
      totalAmount: 749.99,
      note: 'Customer preferred pickup over delivery',
      isPaid: null,
      paidAt: null,
      commissionRate: 0,
    },

    // Consignment Orders
    {
      serviceType: 'consignment' as const,
      status: 'canceled' as const,
      qchStatus: 'failed' as const,
      totalAmount: 29.99,
      note: 'Consignment terms: 30-day listing period',
      isPaid: null,
      paidAt: null,
      commissionRate: 15.0,
    },
    {
      serviceType: 'consignment' as const,
      status: 'completed' as const,
      qchStatus: 'passed' as const,
      totalAmount: 259.99,
      note: 'Item condition: Excellent, original packaging included',
      isPaid: null,
      paidAt: null,
      commissionRate: 12.5,
    },

    // Buyback Orders
    {
      serviceType: 'buyback' as const,
      status: 'confirmed' as const,
      qchStatus: 'processing' as const,
      totalAmount: 39.0,
      note: 'Buyback evaluation completed - Grade A condition',
      isPaid: true,
      paidAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
      commissionRate: 0,
    },
    {
      serviceType: 'buyback' as const,
      status: 'completed' as const,
      qchStatus: 'passed' as const,
      totalAmount: 450.0,
      note: 'Quality check in progress',
      isPaid: false,
      paidAt: null,
      commissionRate: 0,
    },

    // Trade-in Orders
    {
      serviceType: 'trade-in' as const,
      status: 'confirmed' as const,
      qchStatus: 'processing' as const,
      totalAmount: 299.99, // Net amount after trade-in credit
      note: 'Customer trade-in approved after quality check',
      isPaid: null,
      paidAt: null,
      commissionRate: 0,
    },
    {
      serviceType: 'trade-in' as const,
      status: 'canceled' as const,
      qchStatus: 'failed' as const,
      totalAmount: 199.99,
      note: 'Trade-in evaluation in progress',
      isPaid: null,
      paidAt: null,
      commissionRate: 0,
    },

    // Additional retail orders for variety
    {
      serviceType: 'retail' as const,
      status: 'returned' as const,
      qchStatus: 'none' as const,
      totalAmount: 399.99,
      note: 'Customer returned item - defective unit',
      isPaid: null,
      paidAt: null,
      commissionRate: 0,
    },
    {
      serviceType: 'retail' as const,
      status: 'canceled' as const,
      qchStatus: 'none' as const,
      totalAmount: 99.99,
      note: 'Payment failed - order canceled',
      isPaid: null,
      paidAt: null,
      commissionRate: 0,
    },
    {
      serviceType: 'retail' as const,
      status: 'completed' as const,
      qchStatus: 'none' as const,
      totalAmount: 149.99,
      note: 'Special handling required for fragile items',
      isPaid: null,
      paidAt: null,
      commissionRate: 0,
    },
  ];

  // Generate orders by cycling through customers and scenarios
  for (let i = 0; i < orderScenarios.length && i < customers.length; i++) {
    const customer = customers[i];
    const scenario = orderScenarios[i];
    const address = sampleAddresses[i % sampleAddresses.length];

    const locationId = locations[i % locations.length]?.id || null;

    orders.push({
      customerId: customer!.id,
      locationId,
      customerSnapshot: {},
      address,
      serviceType: scenario!.serviceType,
      status: scenario!.status,
      qchStatus: scenario!.qchStatus,
      totalAmount: scenario!.totalAmount,
      note: scenario!.note,
      isPaid: scenario!.isPaid,
      paidAt: scenario!.paidAt,
      commissionRate: scenario!.commissionRate,
      createdAt: getRandomDate(),
    });
  }

  return orders;
};

export type OrdersData = {
  id: string;
  customerId: string;
  serviceType: string;
  status: string;
  qchStatus: string;
  totalAmount: number;
  address: string;
  trackingId: string;
  createdAt: string;
};
