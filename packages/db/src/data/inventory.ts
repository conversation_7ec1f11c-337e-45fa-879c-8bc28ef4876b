import type { LocationsData } from './locations';
import type { VariantsData } from './variants';

export const generateInventoryData = (
  variants: VariantsData[],
  locations: LocationsData[],
) => {
  const inventoryData = [];

  // For each variant, create inventory entries for random locations
  for (const variant of variants) {
    // Randomly decide how many locations will have this variant (1 to all locations)
    const numLocations = Math.floor(Math.random() * locations.length) + 1;

    // Randomly select locations
    const selectedLocationIndices = new Set<number>();
    while (selectedLocationIndices.size < numLocations) {
      const randomIndex = Math.floor(Math.random() * locations.length);
      selectedLocationIndices.add(randomIndex);
    }

    // Convert to array for easier manipulation
    const selectedLocations = Array.from(selectedLocationIndices).map(
      (index) => locations[index],
    );

    // Create inventory entries for each selected location
    for (const location of selectedLocations) {
      // Generate a random quantity between 0 and 100
      const quantity = Math.floor(Math.random() * 101);

      // Only create inventory entry if quantity > 0
      if (quantity > 0) {
        inventoryData.push({
          variantId: variant.id,
          locationId: location!.id,
          quantity: quantity,
        });
      }
    }
  }

  return inventoryData;
};

export type InventoryData = {
  id: string;
  variantId: string;
  locationId: string;
  quantity: number;
  createdAt: string;
  updatedAt: string;
};
