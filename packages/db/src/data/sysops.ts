export const sysopsData = [
  {
    name: 'Super Admin',
    image: null,
    locationId: null, // Superadmin not bound to location
    isSuperAdmin: true,
  },
  {
    name: '<PERSON><PERSON><PERSON> Manager',
    image: null,
    locationId: 'hargeisa-1', // Will be replaced with actual location ID
    isSuperAdmin: false,
  },
  {
    name: 'Burco Manager',
    image: null,
    locationId: 'burco-1', // Will be replaced with actual location ID
    isSuperAdmin: false,
  },
  {
    name: 'Hargeisa Staff',
    image: null,
    locationId: 'hargeisa-2', // Will be replaced with actual location ID
    isSuperAdmin: false,
  },
];

export type SysopsData = {
  id: string;
  name: string;
  image?: string | null;
  userId: string;
  locationId?: string | null;
};

// Function to generate sysop data with user IDs and location IDs
export const generateSysopsData = (
  users: { id: string; isSysop: boolean }[],
  locations: { id: string; name: string }[],
) => {
  // Filter sysops that are marked as sysops
  const sysopUsers = users.filter((user) => user.isSysop);

  // If no sysop sysops found, return empty array
  if (sysopUsers.length === 0) {
    return [];
  }

  return sysopsData.map((sysop, index) => {
    const user = sysopUsers[index % sysopUsers.length]!;
    let locationId = null;

    // Only assign location if user is not a superadmin
    if (!sysop.isSuperAdmin && sysop.locationId) {
      // Map location names to actual location IDs
      if (sysop.locationId === 'hargeisa-1') {
        locationId = locations.find((l) => l.name === 'Hargesia 1')?.id || null;
      } else if (sysop.locationId === 'hargeisa-2') {
        locationId = locations.find((l) => l.name === 'Hargeisa 2')?.id || null;
      } else if (sysop.locationId === 'burco-1') {
        locationId = locations.find((l) => l.name === 'Burco 1')?.id || null;
      }
    }

    return {
      ...sysop,
      userId: user.id,
      locationId,
    };
  });
};
