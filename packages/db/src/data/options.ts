import type { ProductsData } from './products';

export const generateOptionsData = (products: ProductsData[]) => {
  const findProductBySlug = (slug: string): ProductsData => {
    const product = products.find((p) => p.slug === slug);
    if (!product) {
      throw new Error(`Product with slug "${slug}" not found.`);
    }
    return product;
  };

  const productOptionsData = [
    // iPhone 13
    {
      productSlug: 'iphone-13',
      name: 'Storage',
      values: ['128GB', '256GB', '512GB'],
    },
    {
      productSlug: 'iphone-13',
      name: 'Color',
      values: ['Midnight', 'Starlight', 'Blue', 'Pink', 'Red'],
    },

    // MacBook Air M2
    {
      productSlug: 'macbook-air-m2',
      name: 'Storage',
      values: ['256GB SSD', '512GB SSD', '1TB SSD'],
    },
    {
      productSlug: 'macbook-air-m2',
      name: 'RAM',
      values: [
        '8GB unified memory',
        '16GB unified memory',
        '24GB unified memory',
      ],
    },
    {
      productSlug: 'macbook-air-m2',
      name: 'Color',
      values: ['Midnight', 'Starlight', 'Space Gray', 'Silver'],
    },

    // Samsung Galaxy S23 Ultra
    {
      productSlug: 'samsung-galaxy-s23-ultra',
      name: 'Storage',
      values: ['256GB', '512GB', '1TB'],
    },
    {
      productSlug: 'samsung-galaxy-s23-ultra',
      name: 'Color',
      values: ['Phantom Black', 'Green', 'Cream', 'Lavender'],
    },

    // Dell XPS 15
    {
      productSlug: 'dell-xps-15',
      name: 'Processor',
      values: ['Intel Core i7', 'Intel Core i9'],
    },
    {
      productSlug: 'dell-xps-15',
      name: 'RAM',
      values: ['16GB DDR5', '32GB DDR5', '64GB DDR5'],
    },
    {
      productSlug: 'dell-xps-15',
      name: 'Storage',
      values: ['512GB SSD', '1TB SSD', '2TB SSD'],
    },

    // Sony WH-1000XM5
    {
      productSlug: 'sony-wh-1000xm5',
      name: 'Color',
      values: ['Black', 'Silver', 'Midnight Blue'],
    },

    // Apple Watch Series 8
    {
      productSlug: 'apple-watch-series-8',
      name: 'Case Size',
      values: ['41mm', '45mm'],
    },
    {
      productSlug: 'apple-watch-series-8',
      name: 'Case Material',
      values: ['Aluminum', 'Stainless Steel'],
    },
    {
      productSlug: 'apple-watch-series-8',
      name: 'Color',
      values: ['Midnight', 'Starlight', 'Silver', 'Graphite'],
    },

    // Google Pixel 7 Pro
    {
      productSlug: 'google-pixel-7-pro',
      name: 'Storage',
      values: ['128GB', '256GB', '512GB'],
    },
    {
      productSlug: 'google-pixel-7-pro',
      name: 'Color',
      values: ['Obsidian', 'Snow', 'Hazel'],
    },

    // LG C2 OLED TV
    {
      productSlug: 'lg-c2-oled-tv',
      name: 'Size',
      values: ['42-inch', '55-inch', '65-inch', '77-inch'],
    },

    // Bose QuietComfort Earbuds II
    {
      productSlug: 'bose-qc-earbuds-ii',
      name: 'Color',
      values: ['Triple Black', 'Soapstone'],
    },

    // GoPro HERO11 Black
    {
      productSlug: 'gopro-hero11-black',
      name: 'Bundle',
      values: ['Standard', 'Creator Edition'],
    },

    // Canon EOS R6 Mark II
    {
      productSlug: 'canon-eos-r6-mark-ii',
      name: 'Kit',
      values: ['Body Only', 'with RF 24-105mm f/4L Lens'],
    },

    // Microsoft Surface Laptop 5
    {
      productSlug: 'microsoft-surface-laptop-5',
      name: 'Size',
      values: ['13.5-inch', '15-inch'],
    },
    {
      productSlug: 'microsoft-surface-laptop-5',
      name: 'Color',
      values: ['Platinum', 'Matte Black', 'Sandstone'],
    },
    {
      productSlug: 'microsoft-surface-laptop-5',
      name: 'RAM',
      values: ['8GB', '16GB', '32GB'],
    },
    {
      productSlug: 'microsoft-surface-laptop-5',
      name: 'Storage',
      values: ['256GB SSD', '512GB SSD', '1TB SSD'],
    },

    // iPad Pro
    {
      productSlug: 'ipad-pro',
      name: 'Size',
      values: ['11-inch', '12.9-inch'],
    },
    {
      productSlug: 'ipad-pro',
      name: 'Storage',
      values: ['128GB', '256GB', '512GB', '1TB', '2TB'],
    },
    {
      productSlug: 'ipad-pro',
      name: 'Connectivity',
      values: ['Wi-Fi', 'Wi-Fi + Cellular'],
    },
    {
      productSlug: 'ipad-pro',
      name: 'Color',
      values: ['Space Gray', 'Silver'],
    },

    // Samsung Odyssey G9
    {
      productSlug: 'samsung-odyssey-g9',
      name: 'Model',
      values: ['G9', 'Neo G9'],
    },

    // PlayStation 5
    {
      productSlug: 'playstation-5',
      name: 'Edition',
      values: ['Standard', 'Digital'],
    },
  ];

  const optionsToInsert = [];
  const optionValuesMap = new Map<
    string,
    { optionId: string; value: string }[]
  >();

  for (const prodOpt of productOptionsData) {
    const productId = findProductBySlug(prodOpt.productSlug).id;

    optionsToInsert.push({
      name: prodOpt.name,
      productId: productId,
      // Store the option data with a key that can be used to find it later
      _key: `${prodOpt.productSlug}:${prodOpt.name}`,
    });
  }

  return {
    options: optionsToInsert,
    getOptionValuesData: (insertedOptions: OptionsData[]) => {
      // Generate option values data based on the inserted options
      const optionValuesToInsert = [];

      for (const prodOpt of productOptionsData) {
        const option = insertedOptions.find(
          (o) => o._key === `${prodOpt.productSlug}:${prodOpt.name}`,
        );

        if (!option) {
          throw new Error(
            `Option "${prodOpt.name}" for product "${prodOpt.productSlug}" not found after insertion.`,
          );
        }

        for (const value of prodOpt.values) {
          optionValuesToInsert.push({
            value: value,
            optionId: option.id,
            // Store a key that can be used to find this option value later
            _key: `${prodOpt.productSlug}:${prodOpt.name}:${value}`,
          });
        }
      }

      return optionValuesToInsert;
    },
  };
};

export type OptionsData = {
  id: string;
  name: string;
  productId: string;
  _key: string;
};

export type OptionValuesData = {
  id: string;
  value: string;
  optionId: string;
  _key: string;
};
