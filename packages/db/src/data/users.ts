import { getRandomDate } from './utils';

export const usersData = [
  {
    email: '<EMAIL>',
    emailVerifiedAt: getRandomDate(),
    phone: '+252 63 4103005',
    phoneVerifiedAt: getRandomDate(),
    password: '$2b$10$AvHQ6cdPHA8ri/XEXVPen.btKvECMq3mexi63nsLvxuJNbrhHdYVO', //Admin@123
    isSysop: true,
  },
  {
    email: '<EMAIL>',
    emailVerifiedAt: getRandomDate(),
    phone: '+252634567890',
    phoneVerifiedAt: getRandomDate(),
    password: '$2b$10$sqYSBcpjufJlOq/8eK3KqeNGQuFSVcKHQVSDZ.Z.1/Cl5.HVbOIsu', // password123
    isSysop: false,
  },
  {
    email: '<EMAIL>',
    password: '$2b$10$sqYSBcpjufJlOq/8eK3KqeNGQuFSVcKHQVSDZ.Z.1/Cl5.HVbOIsu', // password123
    phone: '+252634567891',
    isSysop: false,
  },
  {
    email: '<EMAIL>',
    password: '$2b$10$sqYSBcpjufJlOq/8eK3KqeNGQuFSVcKHQVSDZ.Z.1/Cl5.HVbOIsu', // password123
    phone: '+252634567892',
    isSysop: false,
  },
  {
    email: '<EMAIL>',
    password: '$2b$10$sqYSBcpjufJlOq/8eK3KqeNGQuFSVcKHQVSDZ.Z.1/Cl5.HVbOIsu', // password123
    phone: '+252634567893',
    isSysop: false,
  },
  {
    email: '<EMAIL>',
    password: '$2b$10$sqYSBcpjufJlOq/8eK3KqeNGQuFSVcKHQVSDZ.Z.1/Cl5.HVbOIsu', // password123
    phone: '+252634567894',
    isSysop: false,
  },
  {
    email: '<EMAIL>',
    password: '$2b$10$sqYSBcpjufJlOq/8eK3KqeNGQuFSVcKHQVSDZ.Z.1/Cl5.HVbOIsu', // password123
    phone: '+252634567895',
    isSysop: false,
  },
  {
    email: '<EMAIL>',
    password: '$2b$10$sqYSBcpjufJlOq/8eK3KqeNGQuFSVcKHQVSDZ.Z.1/Cl5.HVbOIsu', // password123
    phone: '+252634567805', // Different from customer phone since customer has no phone
    isSysop: false,
  },
  {
    email: '<EMAIL>',
    password: '$2b$10$sqYSBcpjufJlOq/8eK3KqeNGQuFSVcKHQVSDZ.Z.1/Cl5.HVbOIsu', // password123
    phone: '+252634567897',
    isSysop: false,
  },
];

export type UsersData = {
  id: string;
  email: string;
  emailVerified: boolean;
  phone?: string;
  phoneVerified: boolean;
  isSysop: boolean;
};
