import type { BrandsData } from './brands';
import type { CategoriesData } from './categories';
import { findBySlug } from './utils';

export const generateProductsData = (
  brands: BrandsData[],
  categories: CategoriesData[],
) => {
  const productsData = [
    {
      name: 'MacBook Air M2',
      slug: 'macbook-air-m2',
      description:
        'Supercharged by the M2 chip, this redesigned MacBook Air is more portable and powerful than ever.',
      brandSlug: 'apple',
      categorySlug: 'laptops',
    },
    {
      name: 'iPhone 13',
      slug: 'iphone-13',
      description:
        'A powerful smartphone with a dual-camera system and A15 Bionic chip.',
      brandSlug: 'apple',
      categorySlug: 'mobile-phones',
    },
    // Add 20 new products
    {
      name: 'Samsung Galaxy S23 Ultra',
      slug: 'samsung-galaxy-s23-ultra',
      description:
        'The ultimate smartphone for productivity and creativity, featuring an embedded S Pen and a pro-grade camera.',
      brandSlug: 'samsung',
      categorySlug: 'mobile-phones',
    },
    {
      name: 'Dell XPS 15',
      slug: 'dell-xps-15',
      description:
        'A premium laptop with a stunning OLED display and powerful performance for creators.',
      brandSlug: 'dell',
      categorySlug: 'laptops',
    },
    {
      name: 'Sony WH-1000XM5',
      slug: 'sony-wh-1000xm5',
      description:
        'Industry-leading noise-canceling headphones with exceptional sound quality.',
      brandSlug: 'sony',
      categorySlug: 'headphones',
    },
    {
      name: 'Apple Watch Series 8',
      slug: 'apple-watch-series-8',
      description:
        'A powerful and stylish smartwatch with advanced health and safety features.',
      brandSlug: 'apple',
      categorySlug: 'watches',
    },
    {
      name: 'Google Pixel 7 Pro',
      slug: 'google-pixel-7-pro',
      description:
        'The smartest Google phone yet, with a powerful camera and the new Google Tensor G2 chip.',
      brandSlug: 'google',
      categorySlug: 'mobile-phones',
    },
    {
      name: 'LG C2 OLED TV',
      slug: 'lg-c2-oled-tv',
      description:
        'A stunning OLED TV with a bright, detailed picture and a sleek design.',
      brandSlug: 'lg',
      categorySlug: 'tv',
    },
    {
      name: 'Bose QuietComfort Earbuds II',
      slug: 'bose-qc-earbuds-ii',
      description:
        "The world's best noise cancellation in a compact, truly wireless earbud.",
      brandSlug: 'bose',
      categorySlug: 'headphones',
    },
    {
      name: 'GoPro HERO11 Black',
      slug: 'gopro-hero11-black',
      description:
        'The most powerful GoPro yet, featuring a new, larger image sensor.',
      brandSlug: 'gopro',
      categorySlug: 'cameras',
    },
    {
      name: 'Canon EOS R6 Mark II',
      slug: 'canon-eos-r6-mark-ii',
      description:
        'A full-frame mirrorless camera for hybrid shooters who want to master both stills and video.',
      brandSlug: 'canon',
      categorySlug: 'cameras',
    },
    {
      name: 'Microsoft Surface Laptop 5',
      slug: 'microsoft-surface-laptop-5',
      description:
        'Blazing-fast performance and sophisticated style in a thin and light laptop.',
      brandSlug: 'microsoft',
      categorySlug: 'laptops',
    },
    {
      name: 'iPad Pro',
      slug: 'ipad-pro',
      description:
        'The ultimate iPad experience with the powerful Apple M2 chip, a stunning Liquid Retina XDR display, and superfast wireless connectivity.',
      brandSlug: 'apple',
      categorySlug: 'tablets',
    },
    {
      name: 'Samsung Odyssey G9',
      slug: 'samsung-odyssey-g9',
      description:
        'A super ultrawide gaming monitor with a 1000R curved screen for immersive gameplay.',
      brandSlug: 'samsung',
      categorySlug: 'monitors',
    },
    {
      name: 'PlayStation 5',
      slug: 'playstation-5',
      description:
        'Experience lightning-fast loading with an ultra-high-speed SSD, deeper immersion with support for haptic feedback, adaptive triggers, and 3D Audio.',
      brandSlug: 'sony',
      categorySlug: 'gaming',
    },
  ];

  // Generate the data for insertion by mapping slugs to IDs
  return productsData.map((product) => ({
    name: product.name,
    slug: product.slug,
    description: product.description,
    brandId: findBySlug(brands, product.brandSlug).id,
    categoryId: findBySlug(categories, product.categorySlug).id,
  }));
};

// Type for the returned data from the seeder
export type ProductsData = {
  id: string;
  name: string;
  slug: string;
  brandId: string;
  categoryId: string;
};
