import { getRandomDate } from './utils';

export interface CountriesData {
  id: string;
  name: string;
  code: string;
  emoji: string;
  createdAt: string;
}

export const sampleCountries: Omit<CountriesData, 'id' | 'createdAt'>[] = [
  {
    name: 'Somalia',
    code: '+252',
    emoji: '🇸🇴',
  },
  {
    name: 'Ethiopia',
    code: '+251',
    emoji: '🇪🇹',
  },
];

export const generateCountriesData = () => {
  return sampleCountries.map((country) => ({
    ...country,
    createdAt: getRandomDate(),
  }));
};
