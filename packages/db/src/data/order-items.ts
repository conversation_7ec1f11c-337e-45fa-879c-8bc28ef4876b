import type { OrdersData } from './orders';
import type { ProductsData } from './products';
import type { VariantsData } from './variants';

// Helper function to create product snapshot
const createProductSnapshot = (product: ProductsData) => ({
  id: product.id,
  name: product.name,
  slug: product.slug,
  brandId: product.brandId,
  categoryId: product.categoryId,
  snapshotAt: new Date().toISOString(),
});

// Helper function to create variant snapshot
const createVariantSnapshot = (variant: VariantsData, productName: string) => ({
  id: variant.id,
  sku: variant.sku,
  productId: variant.productId,
  productName: productName,
  snapshotAt: new Date().toISOString(),
});

// Helper function to get realistic price based on product name
const getRealisticPrice = (productName: string, variantSku: string): number => {
  const name = productName.toLowerCase();
  const sku = variantSku.toLowerCase();

  // iPhone pricing
  if (name.includes('iphone 13')) {
    if (sku.includes('128')) return 699.99;
    if (sku.includes('256')) return 799.99;
    if (sku.includes('512')) return 999.99;
  }

  // MacBook pricing
  if (name.includes('macbook air m2')) {
    if (sku.includes('256')) return 1199.99;
    if (sku.includes('512')) return 1499.99;
    if (sku.includes('1t')) return 1899.99;
  }

  // Samsung Galaxy pricing
  if (name.includes('samsung galaxy s23')) {
    if (sku.includes('256')) return 899.99;
    if (sku.includes('512')) return 1099.99;
    if (sku.includes('1t')) return 1299.99;
  }

  // Dell XPS pricing
  if (name.includes('dell xps')) {
    if (sku.includes('i7-16-512')) return 1599.99;
    if (sku.includes('i7-32-1t')) return 1999.99;
    if (sku.includes('i9')) return 2499.99;
  }

  // Sony headphones
  if (name.includes('sony wh-1000xm5')) return 399.99;

  // Apple Watch
  if (name.includes('apple watch')) {
    if (sku.includes('41-al')) return 399.99;
    if (sku.includes('45-al')) return 429.99;
    if (sku.includes('ss')) return 699.99;
  }

  // Google Pixel
  if (name.includes('google pixel')) {
    if (sku.includes('128')) return 599.99;
    if (sku.includes('256')) return 699.99;
    if (sku.includes('512')) return 899.99;
  }

  // PlayStation
  if (name.includes('playstation 5')) return 499.99;

  // iPad Pro
  if (name.includes('ipad pro')) {
    if (sku.includes('128')) return 799.99;
    if (sku.includes('256')) return 899.99;
    if (sku.includes('129')) return 1099.99; // 12.9-inch model
  }

  // Microsoft Surface
  if (name.includes('microsoft surface')) {
    if (sku.includes('13')) return 999.99;
    if (sku.includes('15')) return 1299.99;
  }

  // Bose earbuds
  if (name.includes('bose quietcomfort')) return 279.99;

  // Default fallback pricing
  return 199.99;
};

export const generateOrderItemsData = (
  orders: OrdersData[],
  products: ProductsData[],
  variants: VariantsData[],
) => {
  const orderItems: OrderItemsData[] = [];

  const productMap = new Map(products.map((p) => [p.id, p]));

  // Define order item scenarios - each order gets specific items
  const orderItemScenarios = [
    // Order 1: iPhone 13 (retail)
    [{ productSlug: 'iphone-13', variantSku: 'IP13-256-BLU', quantity: 1 }],

    // Order 2: MacBook Air M2 (retail)
    [
      {
        productSlug: 'macbook-air-m2',
        variantSku: 'MBA-M2-512-8-STL',
        quantity: 1,
      },
    ],

    // Order 3: Dell XPS 15 (retail)
    [{ productSlug: 'dell-xps-15', variantSku: 'XPS15-I9-32-2T', quantity: 1 }],

    // Order 4: Samsung Galaxy S23 Ultra (consignment)
    [
      {
        productSlug: 'samsung-galaxy-s23-ultra',
        variantSku: 'S23U-512-CRM',
        quantity: 1,
      },
    ],

    // Order 5: Sony WH-1000XM5 (consignment)
    [{ productSlug: 'sony-wh-1000xm5', variantSku: 'WHXM5-BLK', quantity: 1 }],

    // Order 6: iPhone 13 (buyback)
    [{ productSlug: 'iphone-13', variantSku: 'IP13-128-MID', quantity: 1 }],

    // Order 7: Google Pixel 7 Pro (buyback)
    [
      {
        productSlug: 'google-pixel-7-pro',
        variantSku: 'P7P-256-SNO',
        quantity: 1,
      },
    ],

    // Order 8: PlayStation 5 (trade-in)
    [{ productSlug: 'playstation-5', variantSku: 'PS5-STD', quantity: 1 }],

    // Order 9: iPad Pro (trade-in)
    [
      {
        productSlug: 'ipad-pro',
        variantSku: 'IPADPRO-11-128-W-SG',
        quantity: 1,
      },
    ],

    // Order 10: Apple Watch Series 8 (returned)
    [
      {
        productSlug: 'apple-watch-series-8',
        variantSku: 'AW8-41-AL-MID',
        quantity: 1,
      },
    ],

    // Order 11: Microsoft Surface Laptop 5 (canceled)
    [
      {
        productSlug: 'microsoft-surface-laptop-5',
        variantSku: 'SL5-13-PL-8-256',
        quantity: 1,
      },
    ],

    // Order 12: Bose QuietComfort Earbuds (completed)
    [
      {
        productSlug: 'bose-qc-earbuds-ii',
        variantSku: 'QCE2-TBK',
        quantity: 1,
      },
    ],
  ];

  // Generate order items for each order
  orders.forEach((order, orderIndex) => {
    const scenario = orderItemScenarios[orderIndex % orderItemScenarios.length];

    for (const item of scenario!) {
      // Find the product by slug
      const product = products.find((p) => p.slug === item.productSlug);
      if (!product) {
        console.warn(`Product not found for slug: ${item.productSlug}`);
        return;
      }

      // Find the variant by SKU
      const variant = variants.find(
        (v) => v.sku === item.variantSku && v.productId === product.id,
      );
      if (!variant) {
        console.warn(
          `Variant not found for SKU: ${item.variantSku} and product: ${product.name}`,
        );
        return;
      }

      const unitPrice = getRealisticPrice(product.name, variant.sku);

      orderItems.push({
        orderId: order.id,
        productId: product.id,
        variantId: variant.id,
        productSnapshot: createProductSnapshot(product),
        variantSnapshot: createVariantSnapshot(variant, product.name),
        unitPrice,
        quantity: item.quantity,
      });
    }
  });

  return orderItems;
};

export type OrderItemsData = {
  orderId: string;
  productId: string;
  variantId: string;
  productSnapshot: Record<string, unknown>;
  variantSnapshot: Record<string, unknown>;
  unitPrice: number;
  quantity: number;
};
