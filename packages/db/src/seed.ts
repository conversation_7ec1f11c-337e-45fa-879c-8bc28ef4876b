import { sql } from 'drizzle-orm';
import {
  PgTable,
  type PgTransaction,
  getTableConfig,
} from 'drizzle-orm/pg-core';
import * as d from './data';
import { createDb } from './db';
import { env } from './env';
import * as s from './schema';

// @ts-ignore
type Transaction = PgTransaction;

const seedCountries = (tx: Transaction): Promise<d.CountriesData[]> => {
  return tx.insert(s.countries).values(d.generateCountriesData()).returning({
    id: s.countries.id,
    name: s.countries.name,
  });
};

const seedCities = (
  tx: Transaction,
  countries: d.CountriesData[],
): Promise<d.CitiesData[]> => {
  return tx.insert(s.cities).values(d.generateCitiesData(countries)).returning({
    id: s.locations.id,
    name: s.locations.name,
  });
};

const seedDistricts = (
  tx: Transaction,
  cities: d.CitiesData[],
): Promise<d.DistrictsData[]> => {
  return tx
    .insert(s.districts)
    .values(d.generateDistrictsData(cities))
    .returning({
      id: s.districts.id,
      name: s.districts.name,
    });
};

const seedLocations = (tx: Transaction): Promise<d.LocationsData[]> => {
  return tx.insert(s.locations).values(d.locationsData).returning({
    id: s.locations.id,
    name: s.locations.name,
    description: s.locations.description,
    address: s.locations.address,
    city: s.locations.city,
    contactNumber: s.locations.contactNumber,
  });
};

const seedUsers = (tx: Transaction): Promise<d.UsersData[]> => {
  return tx.insert(s.users).values(d.usersData).returning({
    id: s.users.id,
    email: s.users.email,
    phone: s.users.phone,
    isSysop: s.users.isSysop,
  });
};

const seedSysops = (
  tx: Transaction,
  users: d.UsersData[],
  locations: d.LocationsData[],
): Promise<d.SysopsData[]> => {
  return tx
    .insert(s.sysops)
    .values(d.generateSysopsData(users, locations))
    .returning({
      id: s.sysops.id,
      name: s.sysops.name,
      image: s.sysops.image,
      userId: s.sysops.userId,
      locationId: s.sysops.locationId,
    });
};

const seedCustomers = (
  tx: Transaction,
  users: d.UsersData[],
): Promise<d.CustomersData[]> => {
  return tx
    .insert(s.customers)
    .values(d.generateCustomersData(users))
    .returning({
      id: s.customers.id,
      name: s.customers.name,
      phone: s.customers.phone,
      email: s.customers.email,
      image: s.customers.image,
      userId: s.customers.userId,
    });
};

const seedCategories = (tx: Transaction): Promise<d.CategoriesData[]> => {
  return tx.insert(s.categories).values(d.categoriesData).returning({
    id: s.categories.id,
    name: s.categories.name,
    slug: s.categories.slug,
  });
};

const seedBrands = (tx: Transaction): Promise<d.BrandsData[]> => {
  return tx.insert(s.brands).values(d.brandsData).returning({
    id: s.brands.id,
    name: s.brands.name,
    slug: s.brands.slug,
  });
};

const seedBrandCategories = async (
  tx: Transaction,
  brands: d.BrandsData[],
  categories: d.CategoriesData[],
): Promise<void> => {
  await tx
    .insert(s.brandCategories)
    .values(d.generateBrandCategoriesData(brands, categories))
    .returning();
};

const seedProducts = (
  tx: Transaction,
  brands: d.BrandsData[],
  categories: d.CategoriesData[],
): Promise<d.ProductsData[]> => {
  return tx
    .insert(s.products)
    .values(d.generateProductsData(brands, categories))
    .returning({
      id: s.products.id,
      name: s.products.name,
      slug: s.products.slug,
      brandId: s.products.brandId,
      categoryId: s.products.categoryId,
    });
};

const seedOptions = async (
  tx: Transaction,
  products: d.ProductsData[],
): Promise<{
  options: d.OptionsData[];
  optionValues: d.OptionValuesData[];
}> => {
  const { options: optionsData, getOptionValuesData } =
    d.generateOptionsData(products);

  const insertedOptions = [];
  for (const optionData of optionsData) {
    const [inserted] = await tx
      .insert(s.options)
      .values({
        name: optionData.name,
        productId: optionData.productId,
      })
      .returning({
        id: s.options.id,
        name: s.options.name,
        productId: s.options.productId,
      });

    // Add the _key field manually
    insertedOptions.push({
      ...inserted,
      _key: optionData._key,
    });
  }

  const optionValuesData = getOptionValuesData(insertedOptions);

  // Insert option values with _key for reference
  const insertedOptionValues = [];
  for (const optionValueData of optionValuesData) {
    const [inserted] = await tx
      .insert(s.optionValues)
      .values({
        value: optionValueData.value,
        optionId: optionValueData.optionId,
      })
      .returning({
        id: s.optionValues.id,
        value: s.optionValues.value,
        optionId: s.optionValues.optionId,
      });

    insertedOptionValues.push({
      ...inserted,
      _key: optionValueData._key,
    });
  }

  return {
    options: insertedOptions,
    optionValues: insertedOptionValues,
  };
};

const seedVariants = async (
  tx: Transaction,
  products: d.ProductsData[],
  optionValues: d.OptionValuesData[],
): Promise<d.VariantsData[]> => {
  const { variants: variantsData, getVariantOptionValuesData } =
    d.generateVariantsData(products, optionValues);

  const insertedVariants = [];
  for (const variantData of variantsData) {
    const [inserted] = await tx
      .insert(s.variants)
      .values({
        name: variantData.name,
        sku: variantData.sku,
        productId: variantData.productId,
        rank: 0, // Default rank
      })
      .returning({
        id: s.variants.id,
        name: s.variants.name,
        sku: s.variants.sku,
        productId: s.variants.productId,
      });

    // Add the _key field manually
    insertedVariants.push({
      ...inserted,
      _key: variantData._key,
    });
  }

  await tx
    .insert(s.variantOptionValues)
    .values(getVariantOptionValuesData(insertedVariants))
    .returning();

  return insertedVariants;
};

const seedPrices = async (
  tx: Transaction,
  variants: d.VariantsData[],
): Promise<void> => {
  await tx.insert(s.prices).values(d.generatePricesData(variants)).returning();
};

const seedInventory = async (
  tx: Transaction,
  variants: d.VariantsData[],
  locations: d.LocationsData[],
): Promise<void> => {
  await tx
    .insert(s.inventory)
    .values(d.generateInventoryData(variants, locations))
    .returning();
};

const seedOrders = async (
  tx: Transaction,
  customers: d.CustomersData[],
  locations: d.LocationsData[],
): Promise<d.OrdersData[]> => {
  return tx
    .insert(s.orders)
    .values(d.generateOrdersData(customers, locations))
    .returning({
      id: s.orders.id,
      customerId: s.orders.customerId,
      trackingId: s.orders.trackingId,
      serviceType: s.orders.serviceType,
      status: s.orders.status,
      qchStatus: s.orders.qchStatus,
      totalAmount: s.orders.totalAmount,
      address: s.orders.address,
    });
};

const seedOrderItems = async (
  tx: Transaction,
  orders: d.OrdersData[],
  products: d.ProductsData[],
  variants: d.VariantsData[],
): Promise<d.OrderItemsData[]> => {
  return tx
    .insert(s.orderItems)
    .values(d.generateOrderItemsData(orders, products, variants))
    .returning({
      id: s.orderItems.id,
      orderId: s.orderItems.orderId,
      productId: s.orderItems.productId,
      variantId: s.orderItems.variantId,
      unitPrice: s.orderItems.unitPrice,
      quantity: s.orderItems.quantity,
    });
};

(async () => {
  console.log('Starting database seeding...');
  const db = createDb(env.DATABASE_URL);

  // Clean db first
  console.log('Truncating existing tables...');
  for (const table of Object.values(s)) {
    if (table instanceof PgTable) {
      const config = getTableConfig(table);
      config.schema = config.schema === undefined ? 'public' : config.schema;
      const tablesToTruncate = [`"${config.schema}"."${config.name}"`];
      await db.execute(
        sql.raw(`truncate ${tablesToTruncate.join(',')} cascade;`),
      );
    }
  }
  console.log('All tables truncated successfully.');

  await db.transaction(async (tx) => {
    const countries = await seedCountries(tx);
    const cities = await seedCities(tx, countries);
    const districts = await seedDistricts(tx, cities);
    const locations = await seedLocations(tx);
    const users = await seedUsers(tx);
    const [customers, sysops] = await Promise.all([
      seedCustomers(tx, users),
      seedSysops(tx, users, locations),
    ]);

    const [categories, brands] = await Promise.all([
      seedCategories(tx),
      seedBrands(tx),
    ]);
    await seedBrandCategories(tx, brands, categories);
    const products = await seedProducts(tx, brands, categories);
    const { optionValues } = await seedOptions(tx, products);
    const variants = await seedVariants(tx, products, optionValues);
    await seedPrices(tx, variants);
    // await seedInventory(tx, variants, locations);

    const orders = await seedOrders(tx, customers, locations);
    await seedOrderItems(tx, orders, products, variants);
  });

  console.log('Seeding completed successfully ✅');
  process.exit(0);
})().catch((err) => {
  console.error(err);
  process.exit(1);
});
