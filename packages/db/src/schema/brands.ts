import { relations } from 'drizzle-orm';
import {
  index,
  pgTable,
  primaryKey,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';
import { brandCategories } from './brand-categories';

export const brands = pgTable(
  'brands',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    name: t.text().notNull(),
    slug: t.text().notNull(),
    description: t.text(),
    image: t.text(),
    rank: t.smallint().notNull(),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    unique().on(t.name),
    unique().on(t.slug),
    index().on(t.rank),
  ],
);

export const brandsRelations = relations(brands, ({ many }) => ({
  brandCategories: many(brandCategories),
}));
