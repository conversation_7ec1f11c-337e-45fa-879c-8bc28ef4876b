import { relations } from 'drizzle-orm';
import {
  foreignKey,
  pgTable,
  primaryKey,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';
import { products } from './products';
import { variantImages } from './variant-images';

export const images = pgTable(
  'images',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    productId: t.uuid().notNull(),
    path: t.text().notNull(),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    foreignKey({
      columns: [t.productId],
      foreignColumns: [products.id],
    }).onDelete('cascade'),
    unique().on(t.path),
  ],
);

export const imagesRelations = relations(images, ({ one, many }) => ({
  product: one(products, {
    fields: [images.productId],
    references: [products.id],
  }),
  variantImages: many(variantImages),
}));
