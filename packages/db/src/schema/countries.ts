import { relations } from 'drizzle-orm';
import {
  index,
  pgTable,
  primaryKey,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';
import { cities } from './cities';

export const countries = pgTable(
  'countries',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    name: t.text().notNull(),
    code: t.text().notNull(),
    emoji: t.text().notNull(),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    unique().on(t.name),
    unique().on(t.code),
    index().on(t.name),
  ],
);

export const countriesRelations = relations(countries, ({ many }) => ({
  cities: many(cities),
}));
