import { relations } from 'drizzle-orm';
import {
  foreignKey,
  index,
  pgEnum,
  pgTable,
  primaryKey,
  unique,
} from 'drizzle-orm/pg-core';
import { generateTrackingId } from '../utils';
import { customers } from './customers';
import { locations } from './locations';
import { products } from './products';
import { sysops } from './sysops';
import { variants } from './variants';

export const orderServiceTypeEnum = pgEnum('order_service_type', [
  'retail',
  'consignment',
  'buyback',
  'trade-in',
]);

export const orderChangeTypeEnum = pgEnum('order_change_type', [
  'status_update',
  'payment_update',
  'fulfillment_update',
  'amount_update',
  'item_added',
  'item_removed',
  'item_updated',
  'note_update',
  'customer_update',
  'other',
]);

export const orderStatusEnum = pgEnum('order_status', [
  'pending',
  'confirmed',
  'completed',
  'returned',
  'canceled',
]);

export const orderQchStatusEnum = pgEnum('order_qch_status', [
  'none',
  'processing',
  'passed',
  'failed',
]);

export const orders = pgTable(
  'orders',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: t
      .timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: t
      .timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    trackingId: t
      .text()
      .notNull()
      .$defaultFn(() => generateTrackingId()),
    customerId: t.uuid().notNull(),
    locationId: t.uuid(),
    guarantor: t.jsonb(),
    address: t.text().notNull(),
    serviceType: orderServiceTypeEnum().notNull(),
    totalAmount: t
      .numeric({ mode: 'number', precision: 10, scale: 2 })
      .notNull(),
    commissionRate: t
      .numeric({ mode: 'number', precision: 5, scale: 2 })
      .notNull()
      .default(0),

    // where the customer is paid for buyback/trade-in/consignment service
    isPaid: t.boolean(),
    paidAt: t.timestamp({ withTimezone: true, mode: 'string' }),

    status: orderStatusEnum().notNull().default('pending'),
    qchStatus: orderQchStatusEnum().notNull().default('none'),
    note: t.text(),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    foreignKey({
      columns: [t.customerId],
      foreignColumns: [customers.id],
    }).onDelete('restrict'),
    foreignKey({
      columns: [t.locationId],
      foreignColumns: [locations.id],
    }).onDelete('set null'),
    unique().on(t.trackingId),
    // Existing indexes
    index().on(t.customerId),
    index().on(t.serviceType),
    index().on(t.status),
    // New critical indexes for performance optimization
    index().on(t.createdAt),
    index().on(t.updatedAt),
    index().on(t.totalAmount),
    index().on(t.locationId),
    index().on(t.paidAt),
    // Composite indexes for common query patterns
    index().on(t.customerId, t.createdAt),
    index().on(t.serviceType, t.status),
    index().on(t.locationId, t.createdAt),
    index().on(t.status, t.createdAt),
    index().on(t.createdAt, t.totalAmount),
  ],
);

export const ordersRelations = relations(orders, ({ one, many }) => ({
  customer: one(customers, {
    fields: [orders.customerId],
    references: [customers.id],
  }),
  location: one(locations, {
    fields: [orders.locationId],
    references: [locations.id],
  }),
  items: many(orderItems),
}));

export const orderItems = pgTable(
  'order_items',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    orderId: t.uuid().notNull(),
    productId: t.uuid(),
    variantId: t.uuid(),
    productSnapshot: t.jsonb().notNull(),
    variantSnapshot: t.jsonb().notNull(),
    unitPrice: t.numeric({ mode: 'number', precision: 10, scale: 2 }).notNull(),
    quantity: t.smallint().notNull(),
    metadata: t.jsonb(),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    foreignKey({
      columns: [t.orderId],
      foreignColumns: [orders.id],
    }).onDelete('cascade'),
    foreignKey({
      columns: [t.productId],
      foreignColumns: [products.id],
    }).onDelete('set null'),
    foreignKey({
      columns: [t.variantId],
      foreignColumns: [variants.id],
    }).onDelete('set null'),
    // Existing indexes
    index().on(t.orderId),
    index().on(t.productId),
    index().on(t.variantId),
    // New composite indexes for sales analysis
    index().on(t.variantId, t.orderId),
  ],
);

export const orderItemsRelations = relations(orderItems, ({ one }) => ({
  order: one(orders, {
    fields: [orderItems.orderId],
    references: [orders.id],
  }),
  product: one(products, {
    fields: [orderItems.productId],
    references: [products.id],
  }),
  variant: one(variants, {
    fields: [orderItems.variantId],
    references: [variants.id],
  }),
}));

export const orderChanges = pgTable(
  'order_changes',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: t
      .timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    orderId: t.uuid().notNull(),
    sysopId: t.uuid(),
    changeType: orderChangeTypeEnum().notNull(),
    previousValues: t.jsonb('previous_values'),
    newValues: t.jsonb('new_values'),
    comment: t.text('comment'),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    foreignKey({
      columns: [t.orderId],
      foreignColumns: [orders.id],
    }).onDelete('cascade'),
    foreignKey({
      columns: [t.sysopId],
      foreignColumns: [sysops.id],
    }).onDelete('set null'),
    index().on(t.orderId),
    index().on(t.sysopId),
    index().on(t.changeType),
    index().on(t.createdAt),
  ],
);

export const orderChangesRelations = relations(orderChanges, ({ one }) => ({
  order: one(orders, {
    fields: [orderChanges.orderId],
    references: [orders.id],
  }),
  sysop: one(sysops, {
    fields: [orderChanges.sysopId],
    references: [sysops.id],
  }),
}));
