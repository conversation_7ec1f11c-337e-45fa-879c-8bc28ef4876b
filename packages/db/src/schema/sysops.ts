import { relations } from 'drizzle-orm';
import {
  foreignKey,
  index,
  pgTable,
  primaryKey,
  timestamp,
} from 'drizzle-orm/pg-core';
import { locations } from './locations';
import { users } from './users';

export const sysops = pgTable(
  'sysops',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    userId: t.uuid().notNull(),
    locationId: t.uuid(),
    name: t.text().notNull(),
    image: t.text(),
    isSuperAdmin: t.boolean().notNull().default(false),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    foreignKey({
      columns: [t.userId],
      foreignColumns: [users.id],
    }),
    foreignKey({
      columns: [t.locationId],
      foreignColumns: [locations.id],
    }),
    // New critical indexes for performance optimization
    index().on(t.userId),
    index().on(t.locationId),
    index().on(t.isSuperAdmin),
  ],
);

export const sysopsRelations = relations(sysops, ({ one }) => ({
  user: one(users, {
    fields: [sysops.userId],
    references: [users.id],
  }),
  location: one(locations, {
    fields: [sysops.locationId],
    references: [locations.id],
  }),
}));
