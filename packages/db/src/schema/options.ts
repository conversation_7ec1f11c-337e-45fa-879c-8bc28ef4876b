import { relations } from 'drizzle-orm';
import {
  foreignKey,
  pgTable,
  primaryKey,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';
import { optionValues } from './option-values';
import { products } from './products';

export const options = pgTable(
  'options',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    productId: t.uuid().notNull(),
    name: t.text().notNull(),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    foreignKey({
      columns: [t.productId],
      foreignColumns: [products.id],
    }).onDelete('cascade'),
    unique().on(t.name, t.productId),
  ],
);

export const optionsRelations = relations(options, ({ one, many }) => ({
  product: one(products, {
    fields: [options.productId],
    references: [products.id],
  }),
  optionValues: many(optionValues),
}));
