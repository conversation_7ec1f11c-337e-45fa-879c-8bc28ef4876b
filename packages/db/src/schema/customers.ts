import { relations } from 'drizzle-orm';
import {
  foreignKey,
  index,
  pgTable,
  primaryKey,
  timestamp,
} from 'drizzle-orm/pg-core';
import { users } from './users';

export const customers = pgTable(
  'customers',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    userId: t.uuid(),
    name: t.text().notNull(),
    phone: t.text().notNull(),
    email: t.text(),
    nationalId: t.text(),
    image: t.text(),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    foreignKey({
      columns: [t.userId],
      foreignColumns: [users.id],
    }).onDelete('restrict'),
    // Existing indexes
    index().on(t.name),
    // New critical indexes for performance optimization
    index().on(t.createdAt),
    index().on(t.updatedAt),
    index().on(t.userId),
    index().on(t.phone),
    index().on(t.email),
    // Composite indexes for common query patterns
    index().on(t.createdAt, t.name),
  ],
);

export const customersRelations = relations(customers, ({ one }) => ({
  user: one(users, {
    fields: [customers.userId],
    references: [users.id],
  }),
}));
