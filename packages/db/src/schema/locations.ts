import { index, pgTable, primaryKey, timestamp } from 'drizzle-orm/pg-core';

export const locations = pgTable(
  'locations',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    name: t.text().notNull(),
    description: t.text(),
    address: t.text().notNull(),
    city: t.text().notNull(),
    contactNumber: t.text().notNull(),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    index().on(t.name),
    index().on(t.city),
  ],
);
