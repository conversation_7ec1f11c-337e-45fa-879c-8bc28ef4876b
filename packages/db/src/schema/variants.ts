import { relations, sql } from 'drizzle-orm';
import {
  foreignKey,
  index,
  pgTable,
  primaryKey,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';
import { inventory } from './inventory';
import { prices } from './prices';
import { products } from './products';
import { variantImages } from './variant-images';
import { variantOptionValues } from './variant-option-values';

export const variants = pgTable(
  'variants',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    productId: t.uuid().notNull(),
    name: t.text().notNull(),
    sku: t.text(),
    rank: t.smallint().notNull(),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    foreignKey({
      columns: [t.productId],
      foreignColumns: [products.id],
    }).onDelete('cascade'),
    unique().on(t.sku),
    // New critical indexes for performance optimization
    index().on(t.productId),
    index().on(t.createdAt),
    index().on(t.updatedAt),
    // Composite indexes for common query patterns
    index().on(t.productId, t.rank),
  ],
);

export const variantsRelations = relations(variants, ({ one, many }) => ({
  product: one(products, {
    fields: [variants.productId],
    references: [products.id],
  }),
  prices: one(prices, {
    fields: [variants.id],
    references: [prices.variantId],
  }),
  variantImages: many(variantImages),
  variantOptionValue: many(variantOptionValues),
  inventory: many(inventory),
}));
