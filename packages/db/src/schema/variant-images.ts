import { relations } from 'drizzle-orm';
import { foreignKey, pgTable, primaryKey } from 'drizzle-orm/pg-core';
import { images } from './images';
import { variants } from './variants';

export const variantImages = pgTable(
  'variant_images',
  (t) => ({
    variantId: t.uuid().notNull(),
    imageId: t.uuid().notNull(),
  }),
  (t) => [
    primaryKey({
      columns: [t.variantId, t.imageId],
    }),
    foreignKey({
      columns: [t.variantId],
      foreignColumns: [variants.id],
    }).onDelete('cascade'),
    foreignKey({
      columns: [t.imageId],
      foreignColumns: [images.id],
    }).onDelete('cascade'),
  ],
);

export const variantImagesRelations = relations(variantImages, ({ one }) => ({
  variant: one(variants, {
    fields: [variantImages.variantId],
    references: [variants.id],
  }),
  image: one(images, {
    fields: [variantImages.imageId],
    references: [images.id],
  }),
}));
