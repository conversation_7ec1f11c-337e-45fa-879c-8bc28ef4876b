import { relations } from 'drizzle-orm';
import {
  foreignKey,
  index,
  pgTable,
  primaryKey,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';
import { brandCategories } from './brand-categories';

export const categories = pgTable(
  'categories',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    parentId: t.uuid(),
    name: t.text().notNull(),
    slug: t.text().notNull(),
    description: t.text(),
    image: t.text(),
    rank: t.smallint().notNull(),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    foreignKey({
      columns: [t.parentId],
      foreignColumns: [t.id],
    }).onDelete('restrict'),
    unique().on(t.name),
    unique().on(t.slug),
    index().on(t.rank),
  ],
);

export const categoriesRelations = relations(categories, ({ many }) => ({
  brandCategories: many(brandCategories),
}));
