import { relations } from 'drizzle-orm';
import {
  foreignKey,
  index,
  pgTable,
  primaryKey,
  timestamp,
} from 'drizzle-orm/pg-core';
import { variants } from './variants';

export const prices = pgTable(
  'prices',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    variantId: t.uuid().notNull(),
    tradeInPrice: t.numeric({ mode: 'number' }).notNull(),
    consignmentPrice: t.numeric({ mode: 'number' }).notNull(),
    buybackPrice: t.numeric({ mode: 'number' }).notNull(),
    retailPrice: t.numeric({ mode: 'number' }).notNull(),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    foreignKey({
      columns: [t.variantId],
      foreignColumns: [variants.id],
    }).onDelete('cascade'),
    // New critical indexes for performance optimization
    index().on(t.variantId),
  ],
);

export const pricesRelations = relations(prices, ({ one }) => ({
  variant: one(variants, {
    fields: [prices.variantId],
    references: [variants.id],
  }),
}));
