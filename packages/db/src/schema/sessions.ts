import { relations, sql } from 'drizzle-orm';
import {
  foreignKey,
  index,
  pgTable,
  primaryKey,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';
import { users } from './users';

export const sessions = pgTable(
  'sessions',
  (t) => ({
    id: t.uuid().defaultRandom(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    userId: t.uuid().notNull(),
    token: t.text().notNull(),
    isRevoked: t.boolean().notNull().default(sql`FALSE`),
    expiresAt: t.timestamp({ withTimezone: true }).notNull(),
    ipAddress: t.inet(),
    userAgent: t.text(),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    foreignKey({
      columns: [t.userId],
      foreignColumns: [users.id],
    }).onDelete('cascade'),
    unique().on(t.token),
    // New critical indexes for performance optimization
    index().on(t.userId),
    index().on(t.expiresAt),
    index().on(t.isRevoked),
    // Composite indexes for common query patterns
    index().on(t.userId, t.expiresAt),
    index().on(t.userId, t.isRevoked),
  ],
);

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.userId],
    references: [users.id],
  }),
}));
