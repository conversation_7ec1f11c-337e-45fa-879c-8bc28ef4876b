import { sql } from 'drizzle-orm';
import {
  index,
  pgTable,
  primaryKey,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';

export const users = pgTable(
  'users',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    email: t.text(),
    emailVerifiedAt: t.timestamp({ withTimezone: true, mode: 'string' }),
    password: t.text(),
    phone: t.text(),
    phoneVerifiedAt: t.timestamp({ withTimezone: true, mode: 'string' }),
    verificationToken: t.text(),
    verificationSentAt: t.timestamp({ withTimezone: true }),
    recoveryToken: t.text(),
    recoverySentAt: t.timestamp({ withTimezone: true }),
    invitedAt: t.timestamp({ withTimezone: true }),
    lastSignInAt: t.timestamp({ withTimezone: true, mode: 'string' }),
    isBanned: t.boolean().notNull().default(sql`FALSE`),
    isAnonymous: t.boolean().notNull().default(sql`FALSE`),
    isSysop: t.boolean().notNull().default(sql`FALSE`),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    unique().on(t.email),
    unique().on(t.phone),
    index().on(t.verificationToken),
    index().on(t.recoveryToken),
    index().on(t.isSysop),
  ],
);
