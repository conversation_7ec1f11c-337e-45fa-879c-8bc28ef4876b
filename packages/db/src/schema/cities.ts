import { relations } from 'drizzle-orm';
import {
  foreignKey,
  index,
  pgTable,
  primaryKey,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';
import { countries } from './countries';
import { districts } from './districts';

export const cities = pgTable(
  'cities',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    countryId: t.uuid().notNull(),
    name: t.text().notNull(),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    foreignKey({
      columns: [t.countryId],
      foreignColumns: [countries.id],
    }).onDelete('restrict'),
    unique().on(t.name, t.countryId),
    index().on(t.name),
    index().on(t.countryId),
  ],
);

export const citiesRelations = relations(cities, ({ one, many }) => ({
  country: one(countries, {
    fields: [cities.countryId],
    references: [countries.id],
  }),
  districts: many(districts),
}));
