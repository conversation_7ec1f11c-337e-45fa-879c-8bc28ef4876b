import { relations } from 'drizzle-orm';
import { foreignKey, pgTable, primaryKey } from 'drizzle-orm/pg-core';
import { optionValues } from './option-values';
import { variants } from './variants';

export const variantOptionValues = pgTable(
  'variant_option_values',
  (t) => ({
    variantId: t.uuid().notNull(),
    optionValueId: t.uuid().notNull(),
  }),
  (t) => [
    primaryKey({
      columns: [t.variantId, t.optionValueId],
    }),
    foreignKey({
      columns: [t.variantId],
      foreignColumns: [variants.id],
    }).onDelete('cascade'),
    foreignKey({
      columns: [t.optionValueId],
      foreignColumns: [optionValues.id],
    }).onDelete('cascade'),
  ],
);

export const variantOptionValuesRelations = relations(
  variantOptionValues,
  ({ one }) => ({
    variant: one(variants, {
      fields: [variantOptionValues.variantId],
      references: [variants.id],
    }),
    optionValue: one(optionValues, {
      fields: [variantOptionValues.optionValueId],
      references: [optionValues.id],
    }),
  }),
);
