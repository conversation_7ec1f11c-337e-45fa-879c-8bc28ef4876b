import { relations } from 'drizzle-orm';
import {
  foreignKey,
  index,
  pgTable,
  primaryKey,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';
import { locations } from './locations';
import { variants } from './variants';

export const inventory = pgTable(
  'inventory',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    variantId: t.uuid().notNull(),
    locationId: t.uuid().notNull(),
    quantity: t.smallint().notNull().default(0),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    foreignKey({
      columns: [t.variantId],
      foreignColumns: [variants.id],
    }).onDelete('cascade'),
    foreignKey({
      columns: [t.locationId],
      foreignColumns: [locations.id],
    }).onDelete('cascade'),
    unique().on(t.variantId, t.locationId),
    // New critical indexes for performance optimization
    index().on(t.quantity),
    index().on(t.createdAt),
    index().on(t.updatedAt),
    // Composite indexes for common query patterns
    index().on(t.variantId, t.quantity),
  ],
);

export const variantInventoryRelations = relations(inventory, ({ one }) => ({
  variant: one(variants, {
    fields: [inventory.variantId],
    references: [variants.id],
  }),
  location: one(locations, {
    fields: [inventory.locationId],
    references: [locations.id],
  }),
}));
