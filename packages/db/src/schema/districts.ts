import { relations } from 'drizzle-orm';
import {
  foreignKey,
  index,
  pgTable,
  primaryKey,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';
import { cities } from './cities';

export const districts = pgTable(
  'districts',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    cityId: t.uuid().notNull(),
    name: t.text().notNull(),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    foreignKey({
      columns: [t.cityId],
      foreignColumns: [cities.id],
    }).onDelete('restrict'),
    unique().on(t.name, t.cityId),
    index().on(t.name),
    index().on(t.cityId),
  ],
);

export const districtsRelations = relations(districts, ({ one }) => ({
  city: one(cities, {
    fields: [districts.cityId],
    references: [cities.id],
  }),
}));
