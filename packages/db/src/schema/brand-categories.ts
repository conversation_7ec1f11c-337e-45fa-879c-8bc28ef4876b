import { relations } from 'drizzle-orm';
import { foreignKey, pgTable, primaryKey } from 'drizzle-orm/pg-core';
import { brands } from './brands';
import { categories } from './categories';

export const brandCategories = pgTable(
  'brand_categories',
  (t) => ({
    brandId: t.uuid().notNull(),
    categoryId: t.uuid().notNull(),
  }),
  (t) => [
    primaryKey({
      columns: [t.brandId, t.categoryId],
    }),
    foreignKey({
      columns: [t.brandId],
      foreignColumns: [brands.id],
    }).onDelete('cascade'),
    foreignKey({
      columns: [t.categoryId],
      foreignColumns: [categories.id],
    }).onDelete('restrict'),
  ],
);

export const brandCategoriesRelations = relations(brandCategories, ({ one }) => ({
  brand: one(brands, {
    fields: [brandCategories.brandId],
    references: [brands.id],
  }),
  category: one(categories, {
    fields: [brandCategories.categoryId],
    references: [categories.id],
  }),
}));
