import { relations } from 'drizzle-orm';
import {
  foreignKey,
  pgTable,
  primaryKey,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';
import { options } from './options';
import { variantOptionValues } from './variant-option-values';

export const optionValues = pgTable(
  'option_values',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    optionId: t.uuid().notNull(),
    value: t.text().notNull(),
    metadata: t.jsonb(),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    foreignKey({
      columns: [t.optionId],
      foreignColumns: [options.id],
    }).onDelete('cascade'),
    unique().on(t.optionId, t.value),
  ],
);

export const optionValuesRelations = relations(
  optionValues,
  ({ one, many }) => ({
    option: one(options, {
      fields: [optionValues.optionId],
      references: [options.id],
    }),
    variantOptionValues: many(variantOptionValues),
  }),
);
