import { relations, sql } from 'drizzle-orm';
import {
  foreignKey,
  index,
  pgTable,
  primaryKey,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';
import { brands } from './brands';
import { categories } from './categories';
import { images } from './images';
import { options } from './options';
import { variants } from './variants';

export const products = pgTable(
  'products',
  (t) => ({
    id: t.uuid().notNull().defaultRandom(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    categoryId: t.uuid().notNull(),
    brandId: t.uuid().notNull(),
    name: t.text().notNull(),
    slug: t.text().notNull(),
    description: t.text(),
    isPublished: t.boolean().notNull().default(sql`FALSE`),
  }),
  (t) => [
    primaryKey({
      columns: [t.id],
    }),
    foreignKey({
      columns: [t.categoryId],
      foreignColumns: [categories.id],
    }).onDelete('restrict'),
    foreignKey({
      columns: [t.brandId],
      foreignColumns: [brands.id],
    }).onDelete('restrict'),
    unique().on(t.name),
    unique().on(t.slug),
    // Existing indexes
    index().on(t.isPublished),
    index().on(t.brandId),
    // New critical indexes for performance optimization
    index().on(t.categoryId),
    index().on(t.createdAt),
    index().on(t.updatedAt),
    // Composite indexes for common query patterns
    index().on(t.categoryId, t.isPublished),
    index().on(t.brandId, t.isPublished),
    index().on(t.isPublished, t.updatedAt),
  ],
);

export const productsRelations = relations(products, ({ one, many }) => ({
  category: one(categories, {
    fields: [products.categoryId],
    references: [categories.id],
  }),
  brand: one(brands, {
    fields: [products.brandId],
    references: [brands.id],
  }),
  prices: one(images),
  images: many(images),
  options: many(options),
  variants: many(variants),
}));
