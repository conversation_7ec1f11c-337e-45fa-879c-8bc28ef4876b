ALTER TABLE "order_changes" DROP CONSTRAINT "order_changes_user_id_users_id_fk";
--> statement-breakpoint
DROP INDEX "order_changes_user_id_index";--> statement-breakpoint
ALTER TABLE "order_changes" ADD COLUMN "sysop_id" uuid;--> statement-breakpoint
ALTER TABLE "order_changes" ADD CONSTRAINT "order_changes_sysop_id_sysops_id_fk" FOREIGN KEY ("sysop_id") REFERENCES "public"."sysops"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "order_changes_sysop_id_index" ON "order_changes" USING btree ("sysop_id");--> statement-breakpoint
ALTER TABLE "order_changes" DROP COLUMN "user_id";