{"id": "c8a554fd-619e-452f-8197-fbf4fead006f", "prevId": "6e91339c-d343-4f46-8f1e-7bfaa3f407d7", "version": "7", "dialect": "postgresql", "tables": {"public.brand_categories": {"name": "brand_categories", "schema": "", "columns": {"brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"brand_categories_brand_id_brands_id_fk": {"name": "brand_categories_brand_id_brands_id_fk", "tableFrom": "brand_categories", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "brand_categories_category_id_categories_id_fk": {"name": "brand_categories_category_id_categories_id_fk", "tableFrom": "brand_categories", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}}, "compositePrimaryKeys": {"brand_categories_brand_id_category_id_pk": {"name": "brand_categories_brand_id_category_id_pk", "columns": ["brand_id", "category_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.brands": {"name": "brands", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "rank": {"name": "rank", "type": "smallint", "primaryKey": false, "notNull": true}}, "indexes": {"brands_rank_index": {"name": "brands_rank_index", "columns": [{"expression": "rank", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {"brands_id_pk": {"name": "brands_id_pk", "columns": ["id"]}}, "uniqueConstraints": {"brands_name_unique": {"name": "brands_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "brands_slug_unique": {"name": "brands_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.categories": {"name": "categories", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "rank": {"name": "rank", "type": "smallint", "primaryKey": false, "notNull": true}}, "indexes": {"categories_rank_index": {"name": "categories_rank_index", "columns": [{"expression": "rank", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"categories_parent_id_categories_id_fk": {"name": "categories_parent_id_categories_id_fk", "tableFrom": "categories", "tableTo": "categories", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}}, "compositePrimaryKeys": {"categories_id_pk": {"name": "categories_id_pk", "columns": ["id"]}}, "uniqueConstraints": {"categories_name_unique": {"name": "categories_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "categories_slug_unique": {"name": "categories_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.cities": {"name": "cities", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "country_id": {"name": "country_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"cities_name_index": {"name": "cities_name_index", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cities_country_id_index": {"name": "cities_country_id_index", "columns": [{"expression": "country_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"cities_country_id_countries_id_fk": {"name": "cities_country_id_countries_id_fk", "tableFrom": "cities", "tableTo": "countries", "columnsFrom": ["country_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}}, "compositePrimaryKeys": {"cities_id_pk": {"name": "cities_id_pk", "columns": ["id"]}}, "uniqueConstraints": {"cities_name_countryId_unique": {"name": "cities_name_countryId_unique", "nullsNotDistinct": false, "columns": ["name", "country_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.countries": {"name": "countries", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "emoji": {"name": "emoji", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"countries_name_index": {"name": "countries_name_index", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {"countries_id_pk": {"name": "countries_id_pk", "columns": ["id"]}}, "uniqueConstraints": {"countries_name_unique": {"name": "countries_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "countries_code_unique": {"name": "countries_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customers": {"name": "customers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "national_id": {"name": "national_id", "type": "text", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"customers_name_index": {"name": "customers_name_index", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customers_created_at_index": {"name": "customers_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customers_updated_at_index": {"name": "customers_updated_at_index", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customers_user_id_index": {"name": "customers_user_id_index", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customers_phone_index": {"name": "customers_phone_index", "columns": [{"expression": "phone", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customers_email_index": {"name": "customers_email_index", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customers_created_at_name_index": {"name": "customers_created_at_name_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"customers_user_id_users_id_fk": {"name": "customers_user_id_users_id_fk", "tableFrom": "customers", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}}, "compositePrimaryKeys": {"customers_id_pk": {"name": "customers_id_pk", "columns": ["id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.districts": {"name": "districts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "city_id": {"name": "city_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"districts_name_index": {"name": "districts_name_index", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "districts_city_id_index": {"name": "districts_city_id_index", "columns": [{"expression": "city_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"districts_city_id_cities_id_fk": {"name": "districts_city_id_cities_id_fk", "tableFrom": "districts", "tableTo": "cities", "columnsFrom": ["city_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}}, "compositePrimaryKeys": {"districts_id_pk": {"name": "districts_id_pk", "columns": ["id"]}}, "uniqueConstraints": {"districts_name_cityId_unique": {"name": "districts_name_cityId_unique", "nullsNotDistinct": false, "columns": ["name", "city_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.images": {"name": "images", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "product_id": {"name": "product_id", "type": "uuid", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"images_product_id_products_id_fk": {"name": "images_product_id_products_id_fk", "tableFrom": "images", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"images_id_pk": {"name": "images_id_pk", "columns": ["id"]}}, "uniqueConstraints": {"images_path_unique": {"name": "images_path_unique", "nullsNotDistinct": false, "columns": ["path"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.inventory": {"name": "inventory", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "variant_id": {"name": "variant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "location_id": {"name": "location_id", "type": "uuid", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "smallint", "primaryKey": false, "notNull": true, "default": 0}}, "indexes": {"inventory_quantity_index": {"name": "inventory_quantity_index", "columns": [{"expression": "quantity", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "inventory_created_at_index": {"name": "inventory_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "inventory_updated_at_index": {"name": "inventory_updated_at_index", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "inventory_variant_id_quantity_index": {"name": "inventory_variant_id_quantity_index", "columns": [{"expression": "variant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "quantity", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"inventory_variant_id_variants_id_fk": {"name": "inventory_variant_id_variants_id_fk", "tableFrom": "inventory", "tableTo": "variants", "columnsFrom": ["variant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "inventory_location_id_locations_id_fk": {"name": "inventory_location_id_locations_id_fk", "tableFrom": "inventory", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"inventory_id_pk": {"name": "inventory_id_pk", "columns": ["id"]}}, "uniqueConstraints": {"inventory_variantId_locationId_unique": {"name": "inventory_variantId_locationId_unique", "nullsNotDistinct": false, "columns": ["variant_id", "location_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.locations": {"name": "locations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": true}, "contact_number": {"name": "contact_number", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"locations_name_index": {"name": "locations_name_index", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "locations_city_index": {"name": "locations_city_index", "columns": [{"expression": "city", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {"locations_id_pk": {"name": "locations_id_pk", "columns": ["id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.option_values": {"name": "option_values", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "option_id": {"name": "option_id", "type": "uuid", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"option_values_option_id_options_id_fk": {"name": "option_values_option_id_options_id_fk", "tableFrom": "option_values", "tableTo": "options", "columnsFrom": ["option_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"option_values_id_pk": {"name": "option_values_id_pk", "columns": ["id"]}}, "uniqueConstraints": {"option_values_optionId_value_unique": {"name": "option_values_optionId_value_unique", "nullsNotDistinct": false, "columns": ["option_id", "value"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.options": {"name": "options", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "product_id": {"name": "product_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"options_product_id_products_id_fk": {"name": "options_product_id_products_id_fk", "tableFrom": "options", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"options_id_pk": {"name": "options_id_pk", "columns": ["id"]}}, "uniqueConstraints": {"options_name_productId_unique": {"name": "options_name_productId_unique", "nullsNotDistinct": false, "columns": ["name", "product_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.order_changes": {"name": "order_changes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "order_id": {"name": "order_id", "type": "uuid", "primaryKey": false, "notNull": true}, "sysop_id": {"name": "sysop_id", "type": "uuid", "primaryKey": false, "notNull": false}, "change_type": {"name": "change_type", "type": "order_change_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "previous_values": {"name": "previous_values", "type": "jsonb", "primaryKey": false, "notNull": false}, "new_values": {"name": "new_values", "type": "jsonb", "primaryKey": false, "notNull": false}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"order_changes_order_id_index": {"name": "order_changes_order_id_index", "columns": [{"expression": "order_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "order_changes_sysop_id_index": {"name": "order_changes_sysop_id_index", "columns": [{"expression": "sysop_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "order_changes_change_type_index": {"name": "order_changes_change_type_index", "columns": [{"expression": "change_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "order_changes_created_at_index": {"name": "order_changes_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"order_changes_order_id_orders_id_fk": {"name": "order_changes_order_id_orders_id_fk", "tableFrom": "order_changes", "tableTo": "orders", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "order_changes_sysop_id_sysops_id_fk": {"name": "order_changes_sysop_id_sysops_id_fk", "tableFrom": "order_changes", "tableTo": "sysops", "columnsFrom": ["sysop_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {"order_changes_id_pk": {"name": "order_changes_id_pk", "columns": ["id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.order_items": {"name": "order_items", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "order_id": {"name": "order_id", "type": "uuid", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "uuid", "primaryKey": false, "notNull": false}, "variant_id": {"name": "variant_id", "type": "uuid", "primaryKey": false, "notNull": false}, "product_snapshot": {"name": "product_snapshot", "type": "jsonb", "primaryKey": false, "notNull": true}, "variant_snapshot": {"name": "variant_snapshot", "type": "jsonb", "primaryKey": false, "notNull": true}, "unit_price": {"name": "unit_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "smallint", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"order_items_order_id_index": {"name": "order_items_order_id_index", "columns": [{"expression": "order_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "order_items_product_id_index": {"name": "order_items_product_id_index", "columns": [{"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "order_items_variant_id_index": {"name": "order_items_variant_id_index", "columns": [{"expression": "variant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "order_items_variant_id_order_id_index": {"name": "order_items_variant_id_order_id_index", "columns": [{"expression": "variant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "order_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"order_items_order_id_orders_id_fk": {"name": "order_items_order_id_orders_id_fk", "tableFrom": "order_items", "tableTo": "orders", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "order_items_product_id_products_id_fk": {"name": "order_items_product_id_products_id_fk", "tableFrom": "order_items", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "order_items_variant_id_variants_id_fk": {"name": "order_items_variant_id_variants_id_fk", "tableFrom": "order_items", "tableTo": "variants", "columnsFrom": ["variant_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {"order_items_id_pk": {"name": "order_items_id_pk", "columns": ["id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.orders": {"name": "orders", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "tracking_id": {"name": "tracking_id", "type": "text", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "location_id": {"name": "location_id", "type": "uuid", "primaryKey": false, "notNull": false}, "guarantor": {"name": "guarantor", "type": "jsonb", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "service_type": {"name": "service_type", "type": "order_service_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "total_amount": {"name": "total_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "commission_rate": {"name": "commission_rate", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true, "default": 0}, "is_paid": {"name": "is_paid", "type": "boolean", "primaryKey": false, "notNull": false}, "paid_at": {"name": "paid_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "order_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "qch_status": {"name": "qch_status", "type": "order_qch_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'none'"}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"orders_customer_id_index": {"name": "orders_customer_id_index", "columns": [{"expression": "customer_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_service_type_index": {"name": "orders_service_type_index", "columns": [{"expression": "service_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_status_index": {"name": "orders_status_index", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_created_at_index": {"name": "orders_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_updated_at_index": {"name": "orders_updated_at_index", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_total_amount_index": {"name": "orders_total_amount_index", "columns": [{"expression": "total_amount", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_location_id_index": {"name": "orders_location_id_index", "columns": [{"expression": "location_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_paid_at_index": {"name": "orders_paid_at_index", "columns": [{"expression": "paid_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_customer_id_created_at_index": {"name": "orders_customer_id_created_at_index", "columns": [{"expression": "customer_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_service_type_status_index": {"name": "orders_service_type_status_index", "columns": [{"expression": "service_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_location_id_created_at_index": {"name": "orders_location_id_created_at_index", "columns": [{"expression": "location_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_status_created_at_index": {"name": "orders_status_created_at_index", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_created_at_total_amount_index": {"name": "orders_created_at_total_amount_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "total_amount", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"orders_customer_id_customers_id_fk": {"name": "orders_customer_id_customers_id_fk", "tableFrom": "orders", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}, "orders_location_id_locations_id_fk": {"name": "orders_location_id_locations_id_fk", "tableFrom": "orders", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {"orders_id_pk": {"name": "orders_id_pk", "columns": ["id"]}}, "uniqueConstraints": {"orders_trackingId_unique": {"name": "orders_trackingId_unique", "nullsNotDistinct": false, "columns": ["tracking_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.prices": {"name": "prices", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "variant_id": {"name": "variant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "trade_in_price": {"name": "trade_in_price", "type": "numeric", "primaryKey": false, "notNull": true}, "consignment_price": {"name": "consignment_price", "type": "numeric", "primaryKey": false, "notNull": true}, "buyback_price": {"name": "buyback_price", "type": "numeric", "primaryKey": false, "notNull": true}, "retail_price": {"name": "retail_price", "type": "numeric", "primaryKey": false, "notNull": true}}, "indexes": {"prices_variant_id_index": {"name": "prices_variant_id_index", "columns": [{"expression": "variant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"prices_variant_id_variants_id_fk": {"name": "prices_variant_id_variants_id_fk", "tableFrom": "prices", "tableTo": "variants", "columnsFrom": ["variant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"prices_id_pk": {"name": "prices_id_pk", "columns": ["id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.products": {"name": "products", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": true}, "brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_published": {"name": "is_published", "type": "boolean", "primaryKey": false, "notNull": true, "default": "FALSE"}}, "indexes": {"products_is_published_index": {"name": "products_is_published_index", "columns": [{"expression": "is_published", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "products_brand_id_index": {"name": "products_brand_id_index", "columns": [{"expression": "brand_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "products_category_id_index": {"name": "products_category_id_index", "columns": [{"expression": "category_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "products_created_at_index": {"name": "products_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "products_updated_at_index": {"name": "products_updated_at_index", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "products_category_id_is_published_index": {"name": "products_category_id_is_published_index", "columns": [{"expression": "category_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_published", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "products_brand_id_is_published_index": {"name": "products_brand_id_is_published_index", "columns": [{"expression": "brand_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_published", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "products_is_published_updated_at_index": {"name": "products_is_published_updated_at_index", "columns": [{"expression": "is_published", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"products_category_id_categories_id_fk": {"name": "products_category_id_categories_id_fk", "tableFrom": "products", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}, "products_brand_id_brands_id_fk": {"name": "products_brand_id_brands_id_fk", "tableFrom": "products", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}}, "compositePrimaryKeys": {"products_id_pk": {"name": "products_id_pk", "columns": ["id"]}}, "uniqueConstraints": {"products_name_unique": {"name": "products_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "products_slug_unique": {"name": "products_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": false, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "is_revoked": {"name": "is_revoked", "type": "boolean", "primaryKey": false, "notNull": true, "default": "FALSE"}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "inet", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"sessions_user_id_index": {"name": "sessions_user_id_index", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sessions_expires_at_index": {"name": "sessions_expires_at_index", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sessions_is_revoked_index": {"name": "sessions_is_revoked_index", "columns": [{"expression": "is_revoked", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sessions_user_id_expires_at_index": {"name": "sessions_user_id_expires_at_index", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sessions_user_id_is_revoked_index": {"name": "sessions_user_id_is_revoked_index", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_revoked", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"sessions_id_pk": {"name": "sessions_id_pk", "columns": ["id"]}}, "uniqueConstraints": {"sessions_token_unique": {"name": "sessions_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sysops": {"name": "sysops", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "location_id": {"name": "location_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "is_super_admin": {"name": "is_super_admin", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"sysops_user_id_index": {"name": "sysops_user_id_index", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sysops_location_id_index": {"name": "sysops_location_id_index", "columns": [{"expression": "location_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sysops_is_super_admin_index": {"name": "sysops_is_super_admin_index", "columns": [{"expression": "is_super_admin", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"sysops_user_id_users_id_fk": {"name": "sysops_user_id_users_id_fk", "tableFrom": "sysops", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "sysops_location_id_locations_id_fk": {"name": "sysops_location_id_locations_id_fk", "tableFrom": "sysops", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"sysops_id_pk": {"name": "sysops_id_pk", "columns": ["id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "email_verified_at": {"name": "email_verified_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "phone_verified_at": {"name": "phone_verified_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "verification_token": {"name": "verification_token", "type": "text", "primaryKey": false, "notNull": false}, "verification_sent_at": {"name": "verification_sent_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "recovery_token": {"name": "recovery_token", "type": "text", "primaryKey": false, "notNull": false}, "recovery_sent_at": {"name": "recovery_sent_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "invited_at": {"name": "invited_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "last_sign_in_at": {"name": "last_sign_in_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "is_banned": {"name": "is_banned", "type": "boolean", "primaryKey": false, "notNull": true, "default": "FALSE"}, "is_anonymous": {"name": "is_anonymous", "type": "boolean", "primaryKey": false, "notNull": true, "default": "FALSE"}, "is_sysop": {"name": "is_sysop", "type": "boolean", "primaryKey": false, "notNull": true, "default": "FALSE"}}, "indexes": {"users_verification_token_index": {"name": "users_verification_token_index", "columns": [{"expression": "verification_token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_recovery_token_index": {"name": "users_recovery_token_index", "columns": [{"expression": "recovery_token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_is_sysop_index": {"name": "users_is_sysop_index", "columns": [{"expression": "is_sysop", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {"users_id_pk": {"name": "users_id_pk", "columns": ["id"]}}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "users_phone_unique": {"name": "users_phone_unique", "nullsNotDistinct": false, "columns": ["phone"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.variant_images": {"name": "variant_images", "schema": "", "columns": {"variant_id": {"name": "variant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "image_id": {"name": "image_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"variant_images_variant_id_variants_id_fk": {"name": "variant_images_variant_id_variants_id_fk", "tableFrom": "variant_images", "tableTo": "variants", "columnsFrom": ["variant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "variant_images_image_id_images_id_fk": {"name": "variant_images_image_id_images_id_fk", "tableFrom": "variant_images", "tableTo": "images", "columnsFrom": ["image_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"variant_images_variant_id_image_id_pk": {"name": "variant_images_variant_id_image_id_pk", "columns": ["variant_id", "image_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.variant_option_values": {"name": "variant_option_values", "schema": "", "columns": {"variant_id": {"name": "variant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "option_value_id": {"name": "option_value_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"variant_option_values_variant_id_variants_id_fk": {"name": "variant_option_values_variant_id_variants_id_fk", "tableFrom": "variant_option_values", "tableTo": "variants", "columnsFrom": ["variant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "variant_option_values_option_value_id_option_values_id_fk": {"name": "variant_option_values_option_value_id_option_values_id_fk", "tableFrom": "variant_option_values", "tableTo": "option_values", "columnsFrom": ["option_value_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"variant_option_values_variant_id_option_value_id_pk": {"name": "variant_option_values_variant_id_option_value_id_pk", "columns": ["variant_id", "option_value_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.variants": {"name": "variants", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "product_id": {"name": "product_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "sku": {"name": "sku", "type": "text", "primaryKey": false, "notNull": false}, "rank": {"name": "rank", "type": "smallint", "primaryKey": false, "notNull": true}}, "indexes": {"variants_product_id_index": {"name": "variants_product_id_index", "columns": [{"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "variants_created_at_index": {"name": "variants_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "variants_updated_at_index": {"name": "variants_updated_at_index", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "variants_product_id_rank_index": {"name": "variants_product_id_rank_index", "columns": [{"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "rank", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"variants_product_id_products_id_fk": {"name": "variants_product_id_products_id_fk", "tableFrom": "variants", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"variants_id_pk": {"name": "variants_id_pk", "columns": ["id"]}}, "uniqueConstraints": {"variants_sku_unique": {"name": "variants_sku_unique", "nullsNotDistinct": false, "columns": ["sku"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.order_change_type": {"name": "order_change_type", "schema": "public", "values": ["status_update", "payment_update", "fulfillment_update", "amount_update", "item_added", "item_removed", "item_updated", "note_update", "customer_update", "other"]}, "public.order_qch_status": {"name": "order_qch_status", "schema": "public", "values": ["none", "processing", "passed", "failed"]}, "public.order_service_type": {"name": "order_service_type", "schema": "public", "values": ["retail", "consignment", "buyback", "trade-in"]}, "public.order_status": {"name": "order_status", "schema": "public", "values": ["pending", "confirmed", "completed", "returned", "canceled"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}