ALTER TABLE "sysops" ALTER COLUMN "id" SET NOT NULL;--> statement-breakpoint
CREATE INDEX "customers_created_at_index" ON "customers" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "customers_updated_at_index" ON "customers" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX "customers_user_id_index" ON "customers" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "customers_phone_index" ON "customers" USING btree ("phone");--> statement-breakpoint
CREATE INDEX "customers_email_index" ON "customers" USING btree ("email");--> statement-breakpoint
CREATE INDEX "customers_created_at_name_index" ON "customers" USING btree ("created_at","name");--> statement-breakpoint
CREATE INDEX "inventory_quantity_index" ON "inventory" USING btree ("quantity");--> statement-breakpoint
CREATE INDEX "inventory_created_at_index" ON "inventory" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "inventory_updated_at_index" ON "inventory" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX "inventory_variant_id_quantity_index" ON "inventory" USING btree ("variant_id","quantity");--> statement-breakpoint
CREATE INDEX "order_items_variant_id_order_id_index" ON "order_items" USING btree ("variant_id","order_id");--> statement-breakpoint
CREATE INDEX "orders_created_at_index" ON "orders" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "orders_updated_at_index" ON "orders" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX "orders_total_amount_index" ON "orders" USING btree ("total_amount");--> statement-breakpoint
CREATE INDEX "orders_location_id_index" ON "orders" USING btree ("location_id");--> statement-breakpoint
CREATE INDEX "orders_paid_at_index" ON "orders" USING btree ("paid_at");--> statement-breakpoint
CREATE INDEX "orders_customer_id_created_at_index" ON "orders" USING btree ("customer_id","created_at");--> statement-breakpoint
CREATE INDEX "orders_service_type_status_index" ON "orders" USING btree ("service_type","status");--> statement-breakpoint
CREATE INDEX "orders_location_id_created_at_index" ON "orders" USING btree ("location_id","created_at");--> statement-breakpoint
CREATE INDEX "orders_status_created_at_index" ON "orders" USING btree ("status","created_at");--> statement-breakpoint
CREATE INDEX "orders_created_at_total_amount_index" ON "orders" USING btree ("created_at","total_amount");--> statement-breakpoint
CREATE INDEX "prices_variant_id_index" ON "prices" USING btree ("variant_id");--> statement-breakpoint
CREATE INDEX "products_category_id_index" ON "products" USING btree ("category_id");--> statement-breakpoint
CREATE INDEX "products_created_at_index" ON "products" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "products_updated_at_index" ON "products" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX "products_category_id_is_published_index" ON "products" USING btree ("category_id","is_published");--> statement-breakpoint
CREATE INDEX "products_brand_id_is_published_index" ON "products" USING btree ("brand_id","is_published");--> statement-breakpoint
CREATE INDEX "products_is_published_updated_at_index" ON "products" USING btree ("is_published","updated_at");--> statement-breakpoint
CREATE INDEX "sessions_user_id_index" ON "sessions" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "sessions_expires_at_index" ON "sessions" USING btree ("expires_at");--> statement-breakpoint
CREATE INDEX "sessions_is_revoked_index" ON "sessions" USING btree ("is_revoked");--> statement-breakpoint
CREATE INDEX "sessions_user_id_expires_at_index" ON "sessions" USING btree ("user_id","expires_at");--> statement-breakpoint
CREATE INDEX "sessions_user_id_is_revoked_index" ON "sessions" USING btree ("user_id","is_revoked");--> statement-breakpoint
CREATE INDEX "sysops_user_id_index" ON "sysops" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "sysops_location_id_index" ON "sysops" USING btree ("location_id");--> statement-breakpoint
CREATE INDEX "sysops_is_super_admin_index" ON "sysops" USING btree ("is_super_admin");--> statement-breakpoint
CREATE INDEX "variants_product_id_index" ON "variants" USING btree ("product_id");--> statement-breakpoint
CREATE INDEX "variants_created_at_index" ON "variants" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "variants_updated_at_index" ON "variants" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX "variants_product_id_rank_index" ON "variants" USING btree ("product_id","rank");