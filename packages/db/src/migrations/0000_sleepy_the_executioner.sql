CREATE TYPE "public"."order_change_type" AS ENUM('status_update', 'payment_update', 'fulfillment_update', 'amount_update', 'item_added', 'item_removed', 'item_updated', 'note_update', 'customer_update', 'other');--> statement-breakpoint
CREATE TYPE "public"."order_qch_status" AS ENUM('none', 'processing', 'passed', 'failed');--> statement-breakpoint
CREATE TYPE "public"."order_service_type" AS ENUM('retail', 'consignment', 'buyback', 'trade-in');--> statement-breakpoint
CREATE TYPE "public"."order_status" AS ENUM('pending', 'confirmed', 'completed', 'returned', 'canceled');--> statement-breakpoint
CREATE TABLE "brand_categories" (
	"brand_id" uuid NOT NULL,
	"category_id" uuid NOT NULL,
	CONSTRAINT "brand_categories_brand_id_category_id_pk" PRIMARY KEY("brand_id","category_id")
);
--> statement-breakpoint
CREATE TABLE "brands" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"name" text NOT NULL,
	"slug" text NOT NULL,
	"description" text,
	"image" text,
	"rank" smallint NOT NULL,
	CONSTRAINT "brands_id_pk" PRIMARY KEY("id"),
	CONSTRAINT "brands_name_unique" UNIQUE("name"),
	CONSTRAINT "brands_slug_unique" UNIQUE("slug")
);
--> statement-breakpoint
CREATE TABLE "categories" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"parent_id" uuid,
	"name" text NOT NULL,
	"slug" text NOT NULL,
	"description" text,
	"image" text,
	"rank" smallint NOT NULL,
	CONSTRAINT "categories_id_pk" PRIMARY KEY("id"),
	CONSTRAINT "categories_name_unique" UNIQUE("name"),
	CONSTRAINT "categories_slug_unique" UNIQUE("slug")
);
--> statement-breakpoint
CREATE TABLE "cities" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"country_id" uuid NOT NULL,
	"name" text NOT NULL,
	CONSTRAINT "cities_id_pk" PRIMARY KEY("id"),
	CONSTRAINT "cities_name_countryId_unique" UNIQUE("name","country_id")
);
--> statement-breakpoint
CREATE TABLE "countries" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"name" text NOT NULL,
	"code" text NOT NULL,
	"emoji" text NOT NULL,
	CONSTRAINT "countries_id_pk" PRIMARY KEY("id"),
	CONSTRAINT "countries_name_unique" UNIQUE("name"),
	CONSTRAINT "countries_code_unique" UNIQUE("code")
);
--> statement-breakpoint
CREATE TABLE "customers" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"user_id" uuid,
	"name" text NOT NULL,
	"phone" text NOT NULL,
	"email" text,
	"national_id" text,
	"image" text,
	CONSTRAINT "customers_id_pk" PRIMARY KEY("id")
);
--> statement-breakpoint
CREATE TABLE "districts" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"city_id" uuid NOT NULL,
	"name" text NOT NULL,
	CONSTRAINT "districts_id_pk" PRIMARY KEY("id"),
	CONSTRAINT "districts_name_cityId_unique" UNIQUE("name","city_id")
);
--> statement-breakpoint
CREATE TABLE "images" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"product_id" uuid NOT NULL,
	"path" text NOT NULL,
	CONSTRAINT "images_id_pk" PRIMARY KEY("id"),
	CONSTRAINT "images_path_unique" UNIQUE("path")
);
--> statement-breakpoint
CREATE TABLE "inventory" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"variant_id" uuid NOT NULL,
	"location_id" uuid NOT NULL,
	"quantity" smallint DEFAULT 0 NOT NULL,
	CONSTRAINT "inventory_id_pk" PRIMARY KEY("id"),
	CONSTRAINT "inventory_variantId_locationId_unique" UNIQUE("variant_id","location_id")
);
--> statement-breakpoint
CREATE TABLE "locations" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"address" text NOT NULL,
	"city" text NOT NULL,
	"contact_number" text NOT NULL,
	CONSTRAINT "locations_id_pk" PRIMARY KEY("id")
);
--> statement-breakpoint
CREATE TABLE "option_values" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"option_id" uuid NOT NULL,
	"value" text NOT NULL,
	"metadata" jsonb,
	CONSTRAINT "option_values_id_pk" PRIMARY KEY("id"),
	CONSTRAINT "option_values_optionId_value_unique" UNIQUE("option_id","value")
);
--> statement-breakpoint
CREATE TABLE "options" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"product_id" uuid NOT NULL,
	"name" text NOT NULL,
	CONSTRAINT "options_id_pk" PRIMARY KEY("id"),
	CONSTRAINT "options_name_productId_unique" UNIQUE("name","product_id")
);
--> statement-breakpoint
CREATE TABLE "order_changes" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"order_id" uuid NOT NULL,
	"user_id" uuid,
	"change_type" "order_change_type" NOT NULL,
	"previous_values" jsonb,
	"new_values" jsonb,
	"comment" text,
	CONSTRAINT "order_changes_id_pk" PRIMARY KEY("id")
);
--> statement-breakpoint
CREATE TABLE "order_items" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"order_id" uuid NOT NULL,
	"product_id" uuid,
	"variant_id" uuid,
	"product_snapshot" jsonb NOT NULL,
	"variant_snapshot" jsonb NOT NULL,
	"unit_price" numeric(10, 2) NOT NULL,
	"quantity" smallint NOT NULL,
	"metadata" jsonb,
	CONSTRAINT "order_items_id_pk" PRIMARY KEY("id")
);
--> statement-breakpoint
CREATE TABLE "orders" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"tracking_id" text NOT NULL,
	"customer_id" uuid NOT NULL,
	"location_id" uuid,
	"guarantor" jsonb,
	"address" text NOT NULL,
	"service_type" "order_service_type" NOT NULL,
	"total_amount" numeric(10, 2) NOT NULL,
	"commission_rate" numeric(5, 2) DEFAULT 0 NOT NULL,
	"is_paid" boolean,
	"paid_at" timestamp with time zone,
	"status" "order_status" DEFAULT 'pending' NOT NULL,
	"qch_status" "order_qch_status" DEFAULT 'none' NOT NULL,
	"note" text,
	CONSTRAINT "orders_id_pk" PRIMARY KEY("id"),
	CONSTRAINT "orders_trackingId_unique" UNIQUE("tracking_id")
);
--> statement-breakpoint
CREATE TABLE "prices" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"variant_id" uuid NOT NULL,
	"trade_in_price" numeric NOT NULL,
	"consignment_price" numeric NOT NULL,
	"buyback_price" numeric NOT NULL,
	"retail_price" numeric NOT NULL,
	CONSTRAINT "prices_id_pk" PRIMARY KEY("id")
);
--> statement-breakpoint
CREATE TABLE "products" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"category_id" uuid NOT NULL,
	"brand_id" uuid NOT NULL,
	"name" text NOT NULL,
	"slug" text NOT NULL,
	"description" text,
	"is_published" boolean DEFAULT FALSE NOT NULL,
	CONSTRAINT "products_id_pk" PRIMARY KEY("id"),
	CONSTRAINT "products_name_unique" UNIQUE("name"),
	CONSTRAINT "products_slug_unique" UNIQUE("slug")
);
--> statement-breakpoint
CREATE TABLE "sessions" (
	"id" uuid DEFAULT gen_random_uuid(),
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"user_id" uuid NOT NULL,
	"token" text NOT NULL,
	"is_revoked" boolean DEFAULT FALSE NOT NULL,
	"expires_at" timestamp with time zone NOT NULL,
	"ip_address" "inet",
	"user_agent" text,
	CONSTRAINT "sessions_id_pk" PRIMARY KEY("id"),
	CONSTRAINT "sessions_token_unique" UNIQUE("token")
);
--> statement-breakpoint
CREATE TABLE "sysops" (
	"id" uuid DEFAULT gen_random_uuid(),
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"user_id" uuid NOT NULL,
	"location_id" uuid,
	"name" text NOT NULL,
	"image" text,
	"is_super_admin" boolean DEFAULT false NOT NULL,
	CONSTRAINT "sysops_id_pk" PRIMARY KEY("id")
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"email" text,
	"email_verified_at" timestamp with time zone,
	"password" text,
	"phone" text,
	"phone_verified_at" timestamp with time zone,
	"verification_token" text,
	"verification_sent_at" timestamp with time zone,
	"recovery_token" text,
	"recovery_sent_at" timestamp with time zone,
	"invited_at" timestamp with time zone,
	"last_sign_in_at" timestamp with time zone,
	"is_banned" boolean DEFAULT FALSE NOT NULL,
	"is_anonymous" boolean DEFAULT FALSE NOT NULL,
	"is_sysop" boolean DEFAULT FALSE NOT NULL,
	CONSTRAINT "users_id_pk" PRIMARY KEY("id"),
	CONSTRAINT "users_email_unique" UNIQUE("email"),
	CONSTRAINT "users_phone_unique" UNIQUE("phone")
);
--> statement-breakpoint
CREATE TABLE "variant_images" (
	"variant_id" uuid NOT NULL,
	"image_id" uuid NOT NULL,
	CONSTRAINT "variant_images_variant_id_image_id_pk" PRIMARY KEY("variant_id","image_id")
);
--> statement-breakpoint
CREATE TABLE "variant_option_values" (
	"variant_id" uuid NOT NULL,
	"option_value_id" uuid NOT NULL,
	CONSTRAINT "variant_option_values_variant_id_option_value_id_pk" PRIMARY KEY("variant_id","option_value_id")
);
--> statement-breakpoint
CREATE TABLE "variants" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"product_id" uuid NOT NULL,
	"name" text NOT NULL,
	"sku" text,
	"rank" smallint NOT NULL,
	CONSTRAINT "variants_id_pk" PRIMARY KEY("id"),
	CONSTRAINT "variants_sku_unique" UNIQUE("sku")
);
--> statement-breakpoint
ALTER TABLE "brand_categories" ADD CONSTRAINT "brand_categories_brand_id_brands_id_fk" FOREIGN KEY ("brand_id") REFERENCES "public"."brands"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "brand_categories" ADD CONSTRAINT "brand_categories_category_id_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE restrict ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "categories" ADD CONSTRAINT "categories_parent_id_categories_id_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."categories"("id") ON DELETE restrict ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "cities" ADD CONSTRAINT "cities_country_id_countries_id_fk" FOREIGN KEY ("country_id") REFERENCES "public"."countries"("id") ON DELETE restrict ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "customers" ADD CONSTRAINT "customers_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE restrict ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "districts" ADD CONSTRAINT "districts_city_id_cities_id_fk" FOREIGN KEY ("city_id") REFERENCES "public"."cities"("id") ON DELETE restrict ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "images" ADD CONSTRAINT "images_product_id_products_id_fk" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "inventory" ADD CONSTRAINT "inventory_variant_id_variants_id_fk" FOREIGN KEY ("variant_id") REFERENCES "public"."variants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "inventory" ADD CONSTRAINT "inventory_location_id_locations_id_fk" FOREIGN KEY ("location_id") REFERENCES "public"."locations"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "option_values" ADD CONSTRAINT "option_values_option_id_options_id_fk" FOREIGN KEY ("option_id") REFERENCES "public"."options"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "options" ADD CONSTRAINT "options_product_id_products_id_fk" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "order_changes" ADD CONSTRAINT "order_changes_order_id_orders_id_fk" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "order_changes" ADD CONSTRAINT "order_changes_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "order_items" ADD CONSTRAINT "order_items_order_id_orders_id_fk" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "order_items" ADD CONSTRAINT "order_items_product_id_products_id_fk" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "order_items" ADD CONSTRAINT "order_items_variant_id_variants_id_fk" FOREIGN KEY ("variant_id") REFERENCES "public"."variants"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "orders" ADD CONSTRAINT "orders_customer_id_customers_id_fk" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE restrict ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "orders" ADD CONSTRAINT "orders_location_id_locations_id_fk" FOREIGN KEY ("location_id") REFERENCES "public"."locations"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "prices" ADD CONSTRAINT "prices_variant_id_variants_id_fk" FOREIGN KEY ("variant_id") REFERENCES "public"."variants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "products" ADD CONSTRAINT "products_category_id_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE restrict ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "products" ADD CONSTRAINT "products_brand_id_brands_id_fk" FOREIGN KEY ("brand_id") REFERENCES "public"."brands"("id") ON DELETE restrict ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sessions" ADD CONSTRAINT "sessions_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sysops" ADD CONSTRAINT "sysops_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sysops" ADD CONSTRAINT "sysops_location_id_locations_id_fk" FOREIGN KEY ("location_id") REFERENCES "public"."locations"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "variant_images" ADD CONSTRAINT "variant_images_variant_id_variants_id_fk" FOREIGN KEY ("variant_id") REFERENCES "public"."variants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "variant_images" ADD CONSTRAINT "variant_images_image_id_images_id_fk" FOREIGN KEY ("image_id") REFERENCES "public"."images"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "variant_option_values" ADD CONSTRAINT "variant_option_values_variant_id_variants_id_fk" FOREIGN KEY ("variant_id") REFERENCES "public"."variants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "variant_option_values" ADD CONSTRAINT "variant_option_values_option_value_id_option_values_id_fk" FOREIGN KEY ("option_value_id") REFERENCES "public"."option_values"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "variants" ADD CONSTRAINT "variants_product_id_products_id_fk" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "brands_rank_index" ON "brands" USING btree ("rank");--> statement-breakpoint
CREATE INDEX "categories_rank_index" ON "categories" USING btree ("rank");--> statement-breakpoint
CREATE INDEX "cities_name_index" ON "cities" USING btree ("name");--> statement-breakpoint
CREATE INDEX "cities_country_id_index" ON "cities" USING btree ("country_id");--> statement-breakpoint
CREATE INDEX "countries_name_index" ON "countries" USING btree ("name");--> statement-breakpoint
CREATE INDEX "customers_name_index" ON "customers" USING btree ("name");--> statement-breakpoint
CREATE INDEX "districts_name_index" ON "districts" USING btree ("name");--> statement-breakpoint
CREATE INDEX "districts_city_id_index" ON "districts" USING btree ("city_id");--> statement-breakpoint
CREATE INDEX "locations_name_index" ON "locations" USING btree ("name");--> statement-breakpoint
CREATE INDEX "locations_city_index" ON "locations" USING btree ("city");--> statement-breakpoint
CREATE INDEX "order_changes_order_id_index" ON "order_changes" USING btree ("order_id");--> statement-breakpoint
CREATE INDEX "order_changes_user_id_index" ON "order_changes" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "order_changes_change_type_index" ON "order_changes" USING btree ("change_type");--> statement-breakpoint
CREATE INDEX "order_changes_created_at_index" ON "order_changes" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "order_items_order_id_index" ON "order_items" USING btree ("order_id");--> statement-breakpoint
CREATE INDEX "order_items_product_id_index" ON "order_items" USING btree ("product_id");--> statement-breakpoint
CREATE INDEX "order_items_variant_id_index" ON "order_items" USING btree ("variant_id");--> statement-breakpoint
CREATE INDEX "orders_customer_id_index" ON "orders" USING btree ("customer_id");--> statement-breakpoint
CREATE INDEX "orders_service_type_index" ON "orders" USING btree ("service_type");--> statement-breakpoint
CREATE INDEX "orders_status_index" ON "orders" USING btree ("status");--> statement-breakpoint
CREATE INDEX "products_is_published_index" ON "products" USING btree ("is_published");--> statement-breakpoint
CREATE INDEX "products_brand_id_index" ON "products" USING btree ("brand_id");--> statement-breakpoint
CREATE INDEX "users_verification_token_index" ON "users" USING btree ("verification_token");--> statement-breakpoint
CREATE INDEX "users_recovery_token_index" ON "users" USING btree ("recovery_token");--> statement-breakpoint
CREATE INDEX "users_is_sysop_index" ON "users" USING btree ("is_sysop");