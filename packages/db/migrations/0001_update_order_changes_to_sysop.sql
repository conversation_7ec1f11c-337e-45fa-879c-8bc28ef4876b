-- Migration: Update order_changes table to use sysop_id instead of user_id
-- This migration updates the order_changes table to reference sysops instead of users

-- Step 1: Add the new sysop_id column
ALTER TABLE order_changes ADD COLUMN sysop_id UUID;

-- Step 2: Map existing user_id values to corresponding sysop_id values
-- This joins with the sysops table to get the correct sysop_id for each user_id
UPDATE order_changes
SET sysop_id = s.id
FROM sysops s
WHERE order_changes.user_id = s.user_id
AND order_changes.user_id IS NOT NULL;

-- Step 3: Add foreign key constraint for sysop_id
ALTER TABLE order_changes 
ADD CONSTRAINT order_changes_sysop_id_fkey 
FOREIGN KEY (sysop_id) REFERENCES sysops(id) ON DELETE SET NULL;

-- Step 4: Create index on sysop_id
CREATE INDEX idx_order_changes_sysop_id ON order_changes(sysop_id);

-- Step 5: Drop the old foreign key constraint and index for user_id
ALTER TABLE order_changes DROP CONSTRAINT IF EXISTS order_changes_user_id_fkey;
DROP INDEX IF EXISTS idx_order_changes_user_id;

-- Step 6: Drop the user_id column
ALTER TABLE order_changes DROP COLUMN user_id;

-- Note: This migration maps existing user_id values to their corresponding sysop records.
-- If a user_id doesn't have a corresponding sysop record, the sysop_id will remain NULL,
-- which is appropriate for customer actions or system-generated changes.
