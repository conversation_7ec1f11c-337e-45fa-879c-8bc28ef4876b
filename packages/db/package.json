{"name": "@muraadso/db", "description": "", "type": "module", "exports": {".": "./src/index.ts", "./schema/*": "./src/schema/*.ts", "./errors": "./src/errors.ts", "./utils": "./src/utils.ts"}, "scripts": {"generate": "drizzle-kit generate", "migrate:local": "tsx ./src/migrate.ts", "migrate:staging": "tsx --env-file=.env.staging ./src/migrate.ts", "seed:local": "tsx ./src/seed.ts", "seed:staging": "tsx --env-file=.env.staging ./src/seed.ts"}, "devDependencies": {"@muraadso/tsconfig": "workspace:*", "@types/node": "^20", "drizzle-kit": "^0.31.1", "tsx": "^4.19.4", "typescript": "^5"}, "dependencies": {"dotenv": "^16.5.0", "drizzle-orm": "^0.44.0", "postgres": "^3.4.7", "uuid": "^11.1.0", "zod": "catalog:"}}