{"name": "@muraadso/api", "version": "0.1.0", "private": true, "type": "module", "exports": {".": "./src/index.ts", "./types": "./src/types.ts", "./hooks": "./src/hooks/index.ts", "./utils": "./src/utils/index.ts"}, "dependencies": {"@tanstack/react-query": "^5.83.0", "axios": "^1.10.0", "js-cookie": "^3.0.5", "nuqs": "^2.4.3", "zod": "catalog:"}, "devDependencies": {"@muraadso/tsconfig": "workspace:*", "@tanstack/react-query-devtools": "^5.80.2", "@types/js-cookie": "^3.0.6", "@types/react": "^19", "react": "^19.0.0", "typescript": "^5"}, "peerDependencies": {"@muraadso/models": "workspace:*", "@muraadso/ui": "workspace:*", "react": "^19.0.0"}}