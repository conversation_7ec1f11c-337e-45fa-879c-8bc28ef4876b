import type { City, CreateCity, UpdateCity } from '@muraadso/models/cities';
import { toast } from '@muraadso/ui';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getHttpClient } from '../lib/http';
import { queryKeys } from '../utils/query-keys';

export const useGetCities = () => {
  const { isPending, error, data } = useQuery({
    queryKey: queryKeys.cities.all,
    queryFn: () => getHttpClient().get<City[]>('/cities'),
  });

  return {
    isPending,
    error,
    cities: data?.payload || [],
  };
};

export const useCreateCity = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: CreateCity) => {
      const http = getHttpClient();
      const res = await http.post<City>('/cities', payload);
      return res.payload;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.cities.all,
      });
      toast.success('City created successfully!');
    },
  });
};

export const useUpdateCity = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: UpdateCity) => {
      const http = getHttpClient();
      const res = await http.put<City>(`/cities/${payload.id}`, payload);
      return res.payload;
    },
    onSuccess: async (data) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.cities.all,
      });
      toast.success('City updated successfully!');
    },
  });
};

export const useDeleteCity = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      const http = getHttpClient();
      await http.delete(`/cities/${id}`);
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.cities.all,
      });
      toast.success('City deleted successfully!');
    },
  });
};
