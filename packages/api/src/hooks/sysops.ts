import type { InviteUser } from '@muraadso/models/auth';
import type { Sysop } from '@muraadso/models/sysops';
import type { User } from '@muraadso/models/users';
import { toast } from '@muraadso/ui';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getHttpClient } from '../lib/http';

export const useGetSysops = () => {
  const { isPending, error, data } = useQuery({
    queryKey: ['sysops'],
    queryFn: async () => {
      const result = await getHttpClient().get<Sysop[]>('/sysops');
      return result.payload;
    },
  });

  return {
    isPending,
    error,
    sysops: data,
  };
};

export const useGetCurrentSysop = () => {
  const { isPending, error, data } = useQuery({
    queryKey: ['sysops'],
    queryFn: async () => {
      const result = await getHttpClient().get<Sysop>('/sysops/me');
      return result.payload;
    },
    staleTime: Number.POSITIVE_INFINITY,
    gcTime: Number.POSITIVE_INFINITY,
  });
  return {
    isPending,
    error,
    currentSysop: data,
  };
};

export const useInviteSysop = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: InviteUser) => {
      const result = await getHttpClient().post<{
        user: User;
        sysop: Sysop;
      }>('/sysops/invite', payload);
      return result.payload;
    },
    mutationKey: ['invite-user'],
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: ['sysops'],
      });
    },
  });
};

export const useDeleteSysop = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ['delete-user'],
    mutationFn: async (sysopId: string) =>
      await getHttpClient().delete('/sysops/' + sysopId),
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: ['sysops'] });
      toast.success('User deleted successfully!');
    },
  });
};
