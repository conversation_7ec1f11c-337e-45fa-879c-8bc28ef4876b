import type { Brand, CreateBrand, UpdateBrand } from '@muraadso/models/brands';
import { toast } from '@muraadso/ui';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getHttpClient } from '../lib/http';
import { queryKeys } from '../utils/query-keys';

export const useGetBrands = () => {
  const {
    isPending,
    error,
    data: brands,
  } = useQuery({
    queryKey: queryKeys.brands.lists(),
    queryFn: async () => {
      const result = await getHttpClient().get<Brand[]>('/brands');
      return result.payload;
    },
  });

  return {
    isPending,
    error,
    brands,
  };
};

export const useGetBrand = (id: string) => {
  return useQuery({
    queryKey: queryKeys.brands.detail(id),
    queryFn: async () => getHttpClient().get<Brand>('/brands/' + id),
    enabled: !!id,
  });
};

export const useGetBrandStats = () => {
  const { data, isPending, isError, error } = useQuery({
    queryKey: queryKeys.brands.stats(),
    queryFn: async () => {
      const result = await getHttpClient().get<{
        totalBrands: number;
        activeBrandsCount: number;
        emptyBrandsCount: number;
        avgProductsPerBrand: number;
        topBrandsByProducts: Array<{ name: string; productCount: number }>;
        topBrandsByPrice: Array<{
          name: string;
          avgPrice: number;
          priceRange: number;
          publishedProducts: number;
        }>;
        brandsWithBestPublishRate: Array<{
          name: string;
          publishRate: number;
          totalProducts: number;
          avgPrice: number;
        }>;
        avgPricePerBrand: number;
      }>('/brands/stats');
      return result.payload;
    },
  });

  return {
    stats: data || {
      totalBrands: 0,
      activeBrandsCount: 0,
      emptyBrandsCount: 0,
      avgProductsPerBrand: 0,
      topBrandsByProducts: [],
      topBrandsByPrice: [],
      brandsWithBestPublishRate: [],
      avgPricePerBrand: 0,
    },
    isPending,
    isError,
    error,
  };
};

export const useCreateBrand = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: CreateBrand) =>
      getHttpClient().post<Brand>('/brands', payload),
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.brands.all,
      });
      toast.success('Brand created successfully!');
    },
  });
};

export const useUpdateBrand = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: UpdateBrand) =>
      getHttpClient().put<Brand>('/brands/' + payload.id, payload),
    onSuccess: async (data) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.brands.all,
      });
      await queryClient.invalidateQueries({
        queryKey: queryKeys.brands.detail(data.payload.id),
      });
      toast.success('Brand updated successfully!');
    },
  });
};

export const useDeleteBrand = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) =>
      getHttpClient().delete<void>('/brands/' + id),
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.brands.all,
      });
      toast.success('Brand deleted successfully!');
    },
  });
};

export const useUploadBrandImage = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ id, image }: { id: string; image: File }) => {
      const formData = new FormData();
      formData.append('image', image);

      return getHttpClient().post<Brand>(`/brands/${id}/image`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.brands.all,
      });
      toast.success('Brand image uploaded successfully!');
    },
  });
};

export const useDeleteBrandImage = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) =>
      getHttpClient().delete<Brand>(`/brands/${id}/image`),
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.brands.all,
      });
      toast.success('Brand image deleted successfully!');
    },
  });
};
