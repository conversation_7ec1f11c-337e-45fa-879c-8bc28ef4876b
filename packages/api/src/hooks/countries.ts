import type {
  Country,
  CreateCountry,
  UpdateCountry,
} from '@muraadso/models/countries';
import { toast } from '@muraadso/ui';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getHttpClient } from '../lib/http';
import { queryKeys } from '../utils/query-keys';

export const useGetCountries = () => {
  const { isPending, error, data } = useQuery({
    queryKey: queryKeys.countries.all,
    queryFn: async () => getHttpClient().get<Country[]>('/countries'),
  });

  return {
    isPending,
    error,
    countries: data?.payload || [],
  };
};

export const useCreateCountry = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: CreateCountry) => {
      const http = getHttpClient();
      const res = await http.post<Country>('/countries', payload);
      return res.payload;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.countries.all,
      });
      toast.success('Country created successfully!');
    },
    onError: () => {
      toast.error('Failed to create country');
    },
  });
};

export const useUpdateCountry = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: UpdateCountry) => {
      const http = getHttpClient();
      const res = await http.put<Country>(`/countries/${payload.id}`, payload);
      return res.payload;
    },
    onSuccess: async (data) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.countries.all,
      });
      toast.success('Country updated successfully!');
    },
    onError: () => {
      toast.error('Failed to update country');
    },
  });
};

export const useDeleteCountry = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      const http = getHttpClient();
      await http.delete(`/countries/${id}`);
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.countries.all,
      });
      toast.success('Country deleted successfully!');
    },
    onError: () => {
      toast.error('Failed to delete country');
    },
  });
};
