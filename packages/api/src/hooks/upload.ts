import { toast } from '@muraadso/ui';
import { useMutation } from '@tanstack/react-query';
import { getHttpClient } from '../lib/http';

export const useUploadFile = () => {
  return useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('file', file);

      const res = await getHttpClient().post<{ key: string }>(
        '/upload',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );
      return res.payload;
    },
    onError: () => {
      toast.error('Failed to upload file');
    },
  });
};
