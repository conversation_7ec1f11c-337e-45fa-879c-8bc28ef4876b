import type {
  CreateLocation,
  Location,
  UpdateLocation,
} from '@muraadso/models/locations';
import { toast } from '@muraadso/ui';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getHttpClient } from '../lib/http';
import type { ApiResponse } from '../types';
import { queryKeys } from '../utils/query-keys';

export const useGetLocations = () => {
  const { isPending, error, data } = useQuery({
    queryKey: queryKeys.locations.lists(),
    queryFn: async () => {
      const response = await getHttpClient().get<Location[]>('/locations');
      return response.payload;
    },
  });

  return {
    isPending,
    error,
    locations: data,
  };
};

export const useGetLocation = (id: string) => {
  return useQuery({
    queryKey: [...queryKeys.locations.all, 'detail', id],
    queryFn: async () => {
      const response = await getHttpClient().get<Location>(`/locations/${id}`);
      return response.payload;
    },
    enabled: !!id,
  });
};

export const useCreateLocation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: CreateLocation) => {
      const response = await getHttpClient().post<Location>(
        '/locations',
        payload,
      );
      return response.payload;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.locations.all,
      });
      toast.success('Location created successfully!');
    },
  });
};

export const useUpdateLocation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: UpdateLocation) => {
      const response = await getHttpClient().put<Location>(
        `/locations/${payload.id}`,
        payload,
      );
      return response.payload;
    },
    onSuccess: async (data) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.locations.all,
      });
      toast.success('Location updated successfully!');
    },
  });
};

export const useDeleteLocation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      const response = await getHttpClient().delete<void>(`/locations/${id}`);
      return response.payload;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.locations.all,
      });
      toast.success('Location deleted successfully!');
    },
  });
};
