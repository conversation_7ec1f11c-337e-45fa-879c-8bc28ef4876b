import type { CreateOrderChange, OrderChange } from '@muraadso/models/orderChanges';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getHttpClient } from '../lib/http';
import { queryKeys } from '../utils/query-keys';
import { buildQueryString } from '../utils/query-params';

export interface OrderChangesQueryParams {
  page?: number;
  pageSize?: number;
}

export interface OrderChangesResponse {
  changes: OrderChange[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

export const useGetOrderChanges = (
  orderId: string,
  params: OrderChangesQueryParams = {}
) => {
  const {
    isPending,
    isError,
    error,
    data,
  } = useQuery({
    queryKey: queryKeys.orders.changes(orderId, params),
    queryFn: async () => {
      const queryString = buildQueryString(params);
      const response = await getHttpClient().get<OrderChangesResponse>(
        `/orders/${orderId}/changes${queryString}`
      );
      return response.payload;
    },
    enabled: !!orderId,
  });

  return {
    isPending,
    isError,
    error,
    changes: data?.changes || [],
    pagination: data?.pagination || {
      page: 1,
      pageSize: 20,
      total: 0,
      totalPages: 0,
    },
  };
};

export const useCreateOrderChange = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      orderId,
      ...changeData
    }: CreateOrderChange & { orderId: string }) => {
      const response = await getHttpClient().post<OrderChange>(
        `/orders/${orderId}/changes`,
        changeData
      );
      return response.payload;
    },
    onSuccess: async (data, variables) => {
      // Invalidate order changes queries
      await queryClient.invalidateQueries({
        queryKey: queryKeys.orders.changes(variables.orderId),
      });

      // Also invalidate the order detail to refresh any cached data
      await queryClient.invalidateQueries({
        queryKey: queryKeys.orders.detail(variables.orderId),
      });
    },
  });
};
