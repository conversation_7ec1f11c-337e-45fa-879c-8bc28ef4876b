import type {
  ChangePassword,
  SignIn,
  VerifyToken,
} from '@muraadso/models/auth';
import { toast } from '@muraadso/ui';
import { useMutation } from '@tanstack/react-query';
import { getHttpClient } from '../lib/http';
import { createSession } from '../lib/sessions';
import type { ApiResponse } from '../types';

const signIn = async ({ email, password }: SignIn) => {
  const result = await getHttpClient().post<{ session: string }>(
    '/auth/sign-in',
    {
      email,
      password,
    },
  );

  return result.payload;
};

export const verify = async ({ type, token }: VerifyToken) => {
  const res = await getHttpClient().post<{ session: string }>('/auth/verify', {
    type,
    token,
  });
  return res.payload;
};

export const changePassword = async (payload: ChangePassword) => {
  const res = await getHttpClient().post<ApiResponse<{ session: string }>>(
    '/auth/change-password',
    payload,
  );
  return res.payload;
};

export const useAuth = () => {
  return {
    signIn: useMutation({
      mutationKey: ['sign-in'],
      mutationFn: signIn,
      onSuccess: ({ session }) => {
        createSession(session);
        toast.success('Signed in successfully!');
      },
    }),
    verify: useMutation({
      mutationFn: (payload: VerifyToken) => verify(payload),
      mutationKey: ['verify'],
      onSuccess: ({ session }) => {
        createSession(session);
        toast.success('Successfully verified!');
      },
    }),
    changePassword: useMutation({
      mutationKey: ['change-password'],
      mutationFn: changePassword,
      onSuccess: () => {
        toast.success('Password changed successfully!');
      },
    }),
  };
};
