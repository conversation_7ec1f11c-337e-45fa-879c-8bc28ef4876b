import type {
  CreateOrder,
  Order,
  OrderStatus,
  QchStatus,
  UpdateOrder,
} from '@muraadso/models/orders';
import { toast } from '@muraadso/ui';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getHttpClient } from '../lib/http';
import type { OrdersQueryParams } from '../types';
import { queryKeys } from '../utils/query-keys';
import { buildQueryString } from '../utils/query-params';
import { useUpdateInventory } from './inventory';

const validateAndPrepareOrderUpdate = (payload: UpdateOrder) => {
  return Object.fromEntries(
    Object.entries(payload).filter(([_, value]) => value !== undefined),
  );
};

export const useGetOrders = (params: OrdersQueryParams = {}) => {
  const { isPending, error, data } = useQuery({
    queryKey: queryKeys.orders.list(params),
    queryFn: async () => {
      const queryString = buildQueryString(params);
      const url = '/orders' + queryString;
      const result = await getHttpClient().get<{
        orders: Order[];
        pagination: {
          page: number;
          pageSize: number;
          totalCount: number;
          totalPages: number;
          hasNextPage: boolean;
          hasPreviousPage: boolean;
        };
      }>(url);

      return result.payload;
    },
  });
  return {
    isPending,
    error,
    orders: data?.orders || [],
    pagination: data?.pagination,
  };
};

export const useGetOrderStats = (params: OrdersQueryParams = {}) => {
  const { data, isPending, isError, error } = useQuery({
    queryKey: queryKeys.orders.stats(params.statsType || 'monetary'),
    queryFn: async () => {
      const queryString = buildQueryString(params);
      const url = '/orders/stats' + queryString;
      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      const result = await getHttpClient().get<any>(url);
      return result.payload;
    },
  });

  return {
    stats: data || {},
    isPending,
    isError,
    error,
  };
};

export const useGetOrder = (id: string) => {
  const {
    isPending,
    isError,
    error,
    data: order,
  } = useQuery({
    queryKey: queryKeys.orders.detail(id!),
    queryFn: async () => {
      const response = await getHttpClient().get<Order>(`/orders/${id}`);
      return response.payload;
    },
    enabled: !!id,
  });

  return {
    isPending,
    isError,
    error,
    order,
  };
};

export const useCreateOrder = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: CreateOrder) => {
      const response = await getHttpClient().post<Order>('/orders', payload);
      return response.payload;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.orders.all,
      });
      toast.success('Order created successfully!');
    },
  });
};

export const useUpdateOrder = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: UpdateOrder) => {
      const validatedPayload = validateAndPrepareOrderUpdate(payload);
      const response = await getHttpClient().put<Order>(
        `/orders/${payload.id}`,
        validatedPayload,
      );
      return response.payload;
    },
    onSuccess: async (data) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.orders.all,
      });
      await queryClient.invalidateQueries({
        queryKey: queryKeys.orders.detail(data.id),
      });
      toast.success('Order updated successfully!');
    },
  });
};

export const useDeleteOrder = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      const response = await getHttpClient().delete<void>(`/orders/${id}`);
      return response.payload;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.orders.all,
      });
      toast.success('Order deleted successfully!');
    },
  });
};

export const useUpdateOrderStatus = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      orderId,
      status,
      comment,
    }: { orderId: string; status: OrderStatus; comment: string }) => {
      const response = await getHttpClient().put<Order>(
        `/orders/${orderId}/status`,
        {
          status,
          comment,
        },
      );
      return response.payload;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.orders.all,
      });
    },
  });
};

export const useUpdateOrderQchStatus = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      orderId,
      qchStatus,
    }: { orderId: string; qchStatus: QchStatus }) => {
      const response = await getHttpClient().put<Order>(
        `/orders/${orderId}/qch-status`,
        { qchStatus },
      );
      return response.payload;
    },
    onSuccess: async (data, variables) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.orders.all,
      });
      await queryClient.invalidateQueries({
        queryKey: queryKeys.orders.detail(variables.orderId),
      });
      await queryClient.invalidateQueries({
        queryKey: queryKeys.orders.changes(variables.orderId),
      });
    },
  });
};

type OrderStatusUpdateData = {
  orderIds: string[];
  status: OrderStatus;
  comment?: string;
};

export const useBulkUpdateOrderStatus = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: OrderStatusUpdateData) => {
      const response = await getHttpClient().patch<{ updatedCount: number }>(
        '/orders/bulk-status',
        payload,
      );
      return response.payload;
    },
    onSuccess: async (data, variables) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.orders.all,
      });
      // Invalidate individual order details and changes for all affected orders
      for (const orderId of variables.orderIds) {
        await queryClient.invalidateQueries({
          queryKey: queryKeys.orders.detail(orderId),
        });
        await queryClient.invalidateQueries({
          queryKey: queryKeys.orders.changes(orderId),
        });
      }
      toast.success(`${data.updatedCount} orders updated successfully!`);
    },
  });
};

export const useCancelOrder = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      orderId,
      reason,
    }: {
      orderId: string;
      reason?: string;
    }) => {
      const response = await getHttpClient().put<Order>(
        `/orders/${orderId}/cancel`,
        { reason },
      );
      return response.payload;
    },
    onSuccess: async (data, variables) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.orders.all,
      });
      await queryClient.invalidateQueries({
        queryKey: queryKeys.orders.detail(variables.orderId),
      });
      await queryClient.invalidateQueries({
        queryKey: queryKeys.orders.changes(variables.orderId),
      });
      toast.success('Order canceled successfully!');
    },
  });
};

export type FulfillmentItem = {
  orderItemId: string;
  variantId: string;
  fulfillQuantity: number;
  locationId: string;
};

export type FulfillOrderPayload = {
  orderId: string;
  items: FulfillmentItem[];
};

const shouldDecrementInventory = (serviceType: string): boolean => {
  return serviceType === 'retail' || serviceType === 'trade-in';
};

export const useFulfillOrder = () => {
  const queryClient = useQueryClient();
  const { mutateAsync: updateInventory } = useUpdateInventory();

  return useMutation({
    mutationFn: async (payload: FulfillOrderPayload) => {
      const { orderId, items } = payload;

      // First, fetch the order details to get the service type
      const orderResponse = await getHttpClient().get<Order>(
        `/orders/${orderId}`,
      );
      const order = orderResponse.payload;
      const shouldDecrement = shouldDecrementInventory(order.serviceType);

      // Process each fulfillment item
      const fulfillmentPromises = items.map(async (item) => {
        // Get current inventory for this variant and location
        const inventoryResponse = await getHttpClient().get<{
          inventory: Array<{
            inventoryId?: string;
            quantity: number;
            variantId: string;
            locationId: string;
          }>;
        }>(
          `/inventory?variantId=${item.variantId}&locationId=${item.locationId}`,
        );

        const inventoryItem = inventoryResponse.payload.inventory.find(
          (inv) =>
            inv.variantId === item.variantId &&
            inv.locationId === item.locationId,
        );

        let currentQuantity = 0;
        let inventoryId: string;

        if (!inventoryItem?.inventoryId) {
          // If inventory doesn't exist, handle based on service type
          if (shouldDecrement) {
            // For retail/trade-in, we need existing inventory
            throw new Error(
              `No inventory found for variant ${item.variantId} at location ${item.locationId}`,
            );
          }
          // For consignment/buyback, create new inventory record
          const createResponse = await getHttpClient().post<{
            id: string;
            variantId: string;
            locationId: string;
            quantity: number;
          }>('/inventory', {
            variantId: item.variantId,
            locationId: item.locationId,
            quantity: 0,
          });

          inventoryId = createResponse.payload.id;
          currentQuantity = 0;
        } else {
          inventoryId = inventoryItem.inventoryId;
          currentQuantity = inventoryItem.quantity;
        }

        // Only check for sufficient inventory if we're decrementing (retail/trade-in)
        if (shouldDecrement && currentQuantity < item.fulfillQuantity) {
          throw new Error(
            `Insufficient inventory. Available: ${currentQuantity}, Requested: ${item.fulfillQuantity}`,
          );
        }

        // Calculate new quantity based on service type
        const newQuantity = shouldDecrement
          ? currentQuantity - item.fulfillQuantity // Decrement for retail/trade-in
          : currentQuantity + item.fulfillQuantity; // Increment for consignment/buyback

        await updateInventory({
          id: inventoryId,
          variantId: item.variantId,
          locationId: item.locationId,
          quantity: newQuantity,
        });

        return {
          orderItemId: item.orderItemId,
          variantId: item.variantId,
          locationId: item.locationId,
          fulfilledQuantity: item.fulfillQuantity,
          remainingInventory: newQuantity,
        };
      });

      const fulfillmentResults = await Promise.all(fulfillmentPromises);

      return {
        orderId,
        fulfilledItems: fulfillmentResults.length,
        results: fulfillmentResults,
      };
    },
    onSuccess: async (data) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.orders.all,
      });
      await queryClient.invalidateQueries({
        queryKey: queryKeys.orders.detail(data.orderId),
      });
      await queryClient.invalidateQueries({
        queryKey: queryKeys.orders.changes(data.orderId),
      });
      await queryClient.invalidateQueries({
        queryKey: queryKeys.inventory.all,
      });
      toast.success(
        `Order fulfilled successfully! ${data.fulfilledItems} items processed.`,
      );
    },
    onError: (error: { message: string }) => {
      toast.error(`Fulfillment failed: ${error.message}`);
    },
  });
};
