import type {
  CreateDistrict,
  District,
  UpdateDistrict,
} from '@muraadso/models/districts';
import { toast } from '@muraadso/ui';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getHttpClient } from '../lib/http';
import { queryKeys } from '../utils/query-keys';

export const useGetDistricts = () => {
  const { isPending, error, data } = useQuery({
    queryKey: queryKeys.districts.all,
    queryFn: () => getHttpClient().get<District[]>('/districts'),
  });

  return {
    isPending,
    error,
    districts: data?.payload || [],
  };
};

export const useCreateDistrict = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: CreateDistrict) => {
      const http = getHttpClient();
      const res = await http.post<District>('/districts', payload);
      return res.payload;
    },
    onSuccess: async (data) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.districts.all,
      });
      toast.success('District created successfully!');
    },
    onError: () => {
      toast.error('Failed to create district');
    },
  });
};

export const useUpdateDistrict = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: UpdateDistrict) => {
      const http = getHttpClient();
      const res = await http.put<District>(`/districts/${payload.id}`, payload);
      return res.payload;
    },
    onSuccess: async (data) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.districts.all,
      });
      toast.success('District updated successfully!');
    },
    onError: () => {
      toast.error('Failed to update district');
    },
  });
};

export const useDeleteDistrict = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      const http = getHttpClient();
      await http.delete(`/districts/${id}`);
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.districts.all,
      });
      toast.success('District deleted successfully!');
    },
    onError: () => {
      toast.error('Failed to delete district');
    },
  });
};
