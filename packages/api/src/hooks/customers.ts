import type {
  <PERSON>reate<PERSON><PERSON><PERSON>,
  Customer,
  UpdateCustomer,
} from '@muraadso/models/customers';
import type { CreateUser } from '@muraadso/models/users';
import { toast } from '@muraadso/ui';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { z } from 'zod/v4';
import { getHttpClient } from '../lib/http';
import type { ApiResponse, CustomersQueryParams } from '../types';
import { queryKeys } from '../utils/query-keys';
import { buildQueryString } from '../utils/query-params';

export const useGetCustomers = (params: CustomersQueryParams = {}) => {
  const { isPending, error, data } = useQuery({
    queryKey: queryKeys.customers.list(params),
    queryFn: async () => {
      const queryString = buildQueryString(params);
      const url = '/customers' + queryString;

      return await getHttpClient().get<{
        customers: Customer[];
        pagination: {
          page: number;
          pageSize: number;
          totalCount: number;
          totalPages: number;
          hasNextPage: boolean;
          hasPreviousPage: boolean;
        };
      }>(url);
    },
  });

  return {
    isPending,
    error,
    customers: data?.payload?.customers || [],
    pagination: data?.payload?.pagination,
  };
};

// Keep the old hook for backward compatibility
export const useGetCustomersLegacy = (query: string) => {
  return useQuery({
    queryKey: queryKeys.customers.list({ q: query }),
    queryFn: async () => {
      const http = getHttpClient();
      const res = await http.get<Customer[]>('/customers?q=' + query);
      await new Promise((resolve) => setTimeout(resolve, 500));
      return res.payload;
    },
  });
};

export const useGetCustomer = (id: string) => {
  return useQuery({
    queryKey: queryKeys.customers.detail(id),
    queryFn: async () => {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      const http = getHttpClient();
      const res = await http.get<Customer>('/customers/' + id);
      return res.payload;
    },
    enabled: !!id,
  });
};

export const useGetCustomerStats = () => {
  const { data, isPending, isError, error } = useQuery({
    queryKey: queryKeys.customers.stats(),
    queryFn: async () => {
      const result = await getHttpClient().get<{
        totalCustomers: number;
        customersThisWeek: number;
        customersWithAccounts: number;
        accountsThisWeek: number;
        customersThisMonth: number;
        monthlyGrowthPercentage: number;
        customersWithActiveOrders: number;
        avgCustomerLifetimeValue: number;
        repeatCustomersCount: number;
        repeatCustomerRate: number;
        topCustomers: Array<{
          customerId: string;
          totalSpent: number;
          orderCount: number;
        }>;
      }>('/customers/stats');
      return result.payload;
    },
  });

  return {
    stats: data || {
      totalCustomers: 0,
      customersThisWeek: 0,
      customersWithAccounts: 0,
      accountsThisWeek: 0,
      customersThisMonth: 0,
      monthlyGrowthPercentage: 0,
      customersWithActiveOrders: 0,
      avgCustomerLifetimeValue: 0,
      repeatCustomersCount: 0,
      repeatCustomerRate: 0,
      topCustomers: [],
    },
    isPending,
    isError,
    error,
  };
};

const createCustomerWithUserSchema = z.object({
  user: z.object({
    email: z.email().optional(),
    phone: z.string().min(1, 'Phone number is required'),
  }),
  customer: z.object({
    name: z.string().min(1, 'Name is required'),
    image: z.string().optional(),
  }),
});

export const useCreateCustomer = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (
      payload: z.infer<typeof createCustomerWithUserSchema>,
    ) => {
      const http = getHttpClient();
      const res = await http.post<Customer>('/customers', payload);
      return res.payload;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.customers.all,
      });
      toast.success('Customer created successfully!');
    },
  });
};

export const useUpdateCustomer = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: UpdateCustomer) => {
      const http = getHttpClient();
      const res = await http.put<Customer>('/customers/' + payload.id, payload);
      return res.payload;
    },
    onSuccess: async (data) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.customers.all,
      });
      await queryClient.invalidateQueries({
        queryKey: queryKeys.customers.detail(data.id),
      });
      toast.success('Customer updated successfully!');
    },
  });
};

export const useDeleteCustomer = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      const http = getHttpClient();
      const res = await http.delete<void>('/customers/' + id);
      return res.payload;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.customers.all,
      });
      toast.success('Customer deleted successfully!');
    },
  });
};
