import type { InventoryMatrixItem } from '@muraadso/models/inventory';
import { toast } from '@muraadso/ui';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getHttpClient } from '../lib/http';
import type { ApiResponse, InventoryQueryParams } from '../types';
import { queryKeys } from '../utils/query-keys';
import { buildQueryString } from '../utils/query-params';

type InventoryResponse = {
  inventory: InventoryMatrixItem[];
  pagination: {
    page: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
};

export const useGetInventory = (params: InventoryQueryParams = {}) => {
  return useQuery({
    queryKey: queryKeys.inventory.list(params),
    queryFn: async () => {
      const queryString = buildQueryString(params);
      const url = '/inventory' + queryString;
      const res = await getHttpClient().get<InventoryResponse>(url);
      return res.payload;
    },
  });
};

export const useGetInventoryItem = (id: string) => {
  return useQuery({
    queryKey: [...queryKeys.inventory.all, 'detail', id],
    queryFn: async () => {
      const res = await getHttpClient().get<InventoryMatrixItem>(
        `/inventory/${id}`,
      );
      return res.payload;
    },
    enabled: !!id,
  });
};

export const useGetInventoryStats = () => {
  const { data, isPending, isError, error } = useQuery({
    queryKey: queryKeys.inventory.stats(),
    queryFn: async () => {
      const result = await getHttpClient().get<{
        totalInventoryValue: number;
        totalUnitsInStock: number;
        lowStockCount: number;
        outOfStockCount: number;
        overstockedCount: number;
        topLocationsByValue: Array<{
          name: string;
          totalValue: number;
          totalUnits: number;
          uniqueProducts: number;
        }>;
        avgInventoryPerLocation: number;
        topStockedProducts: Array<{
          name: string;
          quantity: number;
          value: number;
        }>;
        activeLocationsCount: number;
      }>('/inventory/stats');
      return result.payload;
    },
  });

  return {
    stats: data || {
      totalInventoryValue: 0,
      totalUnitsInStock: 0,
      lowStockCount: 0,
      outOfStockCount: 0,
      overstockedCount: 0,
      topLocationsByValue: [],
      avgInventoryPerLocation: 0,
      topStockedProducts: [],
      activeLocationsCount: 0,
    },
    isPending,
    isError,
    error,
  };
};

export const useUpdateInventoryQuantity = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ id, quantity }: { id: string; quantity: number }) => {
      const res = await getHttpClient().patch<InventoryMatrixItem>(
        `/inventory/${id}`,
        {
          quantity,
        },
      );
      return res.payload;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.inventory.all,
      });
      toast.success('Inventory updated successfully!');
    },
  });
};

export const useCreateInventory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      variantId,
      locationId,
      quantity,
    }: { variantId: string; locationId: string; quantity: number }) => {
      const response = await getHttpClient().post<
        ApiResponse<InventoryMatrixItem>
      >('/inventory', { variantId, locationId, quantity });
      return response.payload;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.inventory.all });
    },
  });
};

export const useUpdateInventory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      id,
      variantId,
      locationId,
      quantity,
    }: {
      id: string;
      variantId: string;
      locationId: string;
      quantity: number;
    }) => {
      const response = await getHttpClient().put<
        ApiResponse<InventoryMatrixItem>
      >('/inventory/' + id, { variantId, locationId, quantity });
      return response.payload;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.inventory.all });
    },
  });
};
//
// export const useDeleteInventory = () => {
//   const queryClient = useQueryClient();
//   return useMutation({
//     mutationFn: async (id: string) => {
//       const response = await getHttpClient().delete<
//         ApiResponse<InventoryMatrixItem>
//       >('/inventory/' + id);
//       return response.payload;
//     },
//     onSuccess: () => {
//       queryClient.invalidateQueries({ queryKey: queryKeys.inventory.all });
//     },
//   });
// };

export const useBulkUpdateInventory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (updates: Array<{ id: string; quantity: number }>) => {
      const res = await getHttpClient().patch<{ updatedCount: number }>(
        '/inventory/bulk',
        { updates },
      );
      return res.payload;
    },
    onSuccess: async (data) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.inventory.all,
      });
      toast.success(
        `${data.updatedCount} inventory items updated successfully!`,
      );
    },
  });
};
