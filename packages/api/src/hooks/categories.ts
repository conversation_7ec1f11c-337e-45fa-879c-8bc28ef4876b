import type {
  Category,
  CreateCategory,
  UpdateCategory,
} from '@muraadso/models/categories';
import { toast } from '@muraadso/ui';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getHttpClient } from '../lib/http';
import { queryKeys } from '../utils/query-keys';

export const useGetCategories = () => {
  const { isPending, error, data } = useQuery({
    queryKey: queryKeys.categories.lists(),
    queryFn: async () => {
      const res = await getHttpClient().get<Category[]>('/categories');
      return res.payload;
    },
  });

  return {
    isPending,
    error,
    categories: data,
  };
};

export const useGetCategory = (id: string) => {
  return useQuery({
    queryKey: queryKeys.categories.detail(id),
    queryFn: async () => {
      return await getHttpClient().get<Category>('/categories/' + id);
    },
    enabled: !!id,
  });
};

export const useGetCategoryStats = () => {
  const { data, isPending, isError, error } = useQuery({
    queryKey: queryKeys.categories.stats(),
    queryFn: async () => {
      const result = await getHttpClient().get<{
        totalCategories: number;
        activeCategoriesCount: number;
        emptyCategoriesCount: number;
        avgProductsPerCategory: number;
        topCategoriesByProducts: Array<{ name: string; productCount: number }>;
        topCategoriesByPrice: Array<{
          name: string;
          avgPrice: number;
          publishedProducts: number;
        }>;
        categoriesWithBestPublishRate: Array<{
          name: string;
          publishRate: number;
          totalProducts: number;
        }>;
      }>('/categories/stats');
      return result.payload;
    },
  });

  return {
    stats: data || {
      totalCategories: 0,
      activeCategoriesCount: 0,
      emptyCategoriesCount: 0,
      avgProductsPerCategory: 0,
      topCategoriesByProducts: [],
      topCategoriesByPrice: [],
      categoriesWithBestPublishRate: [],
    },
    isPending,
    isError,
    error,
  };
};

export const useCreateCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: CreateCategory) => {
      const res = await getHttpClient().post<Category>('/categories', payload);
      return res.payload;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.categories.all,
      });
      toast.success('Category created successfully!');
    },
  });
};

export const useUpdateCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: UpdateCategory) => {
      const res = await getHttpClient().put<Category>(
        '/categories/' + payload.id,
        payload,
      );
      return res.payload;
    },
    onSuccess: async (data) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.categories.all,
      });
      await queryClient.invalidateQueries({
        queryKey: queryKeys.categories.detail(data.id),
      });
      toast.success('Category updated successfully!');
    },
  });
};

export const useDeleteCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      const res = await getHttpClient().delete<void>('/categories/' + id);
      return res.payload;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.categories.all,
      });
      toast.success('Category deleted successfully!');
    },
  });
};

export const useUploadCategoryImage = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ id, image }: { id: string; image: File }) => {
      const formData = new FormData();
      formData.append('image', image);

      const res = await getHttpClient().post<Category>(
        `/categories/${id}/image`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );
      return res.payload;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.categories.all,
      });
      toast.success('Category image uploaded successfully!');
    },
  });
};

export const useDeleteCategoryImage = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      const res = await getHttpClient().delete<Category>(
        `/categories/${id}/image`,
      );
      return res.payload;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.categories.all,
      });
      toast.success('Category image deleted successfully!');
    },
  });
};
