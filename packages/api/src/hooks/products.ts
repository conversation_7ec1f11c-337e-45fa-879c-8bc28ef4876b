import type {
  CreateOption,
  Option,
  UpdateOption,
} from '@muraadso/models/options';
import type {
  CreateProduct,
  Product,
  UpdateProduct,
} from '@muraadso/models/products';
import type {
  CreateVariant,
  UpdateVariant,
  Variant,
} from '@muraadso/models/variants';
import { toast } from '@muraadso/ui';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useQueryState } from 'nuqs';
import { getHttpClient } from '../lib/http';
import type { ProductsQueryParams } from '../types';
import { queryKeys } from '../utils/query-keys';
import { buildQueryString } from '../utils/query-params';
import { usePagination } from './pagination';

export const useGetProducts = (params: ProductsQueryParams = {}) => {
  const { data, isPending, isError, error } = useQuery({
    queryKey: queryKeys.products.list(params),
    queryFn: async () => {
      const result = await getHttpClient().get<{
        products: Product[];
        pagination: {
          page: number;
          pageSize: number;
          totalCount: number;
          totalPages: number;
          hasNextPage: boolean;
          hasPreviousPage: boolean;
        };
      }>('/products' + buildQueryString(params));
      return result.payload;
    },
  });

  return {
    products: data?.products || [],
    pagination: data?.pagination,
    isPending,
    isError,
    error,
  };
};

export const useGetProductStats = () => {
  const { data, isPending, isError, error } = useQuery({
    queryKey: queryKeys.products.stats(),
    queryFn: async () => {
      const result = await getHttpClient().get<{
        totalProducts: number;
        productsThisWeek: number;
        publishedProducts: number;
        publishedThisWeek: number;
        draftProducts: number;
        draftThisWeek: number;
        trashedProducts: number;
      }>('/products/stats');
      return result.payload;
    },
  });

  return {
    stats: data || {
      totalProducts: 0,
      productsThisWeek: 0,
      publishedProducts: 0,
      publishedThisWeek: 0,
      draftProducts: 0,
      draftThisWeek: 0,
      trashedProducts: 0,
    },
    isPending,
    isError,
    error,
  };
};

export const useGetProduct = (id: string) => {
  const { data, isPending, isError, error } = useQuery({
    queryKey: queryKeys.products.detail(id),
    queryFn: async () => {
      const result = await getHttpClient().get<Product>('/products/' + id);
      return result.payload;
    },
  });

  return {
    product: data,
    isPending,
    isError,
    error,
  };
};

export const useCreateProduct = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: CreateProduct) => {
      const res = await getHttpClient().post<Product>('/products', payload);
      return res.payload;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.products.all,
      });
      toast.success('Product created successfully!');
    },
  });
};

export const useUpdateProduct = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: UpdateProduct) => {
      const res = await getHttpClient().put<Product>(
        '/products/' + payload.id,
        payload,
      );
      return res.payload;
    },
    onSuccess: async (data) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.products.all,
      });
      await queryClient.invalidateQueries({
        queryKey: queryKeys.products.detail(data.id),
      });
      toast.success('Product updated successfully!');
    },
  });
};

export const useDeleteProduct = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      const http = getHttpClient();
      const res = await http.delete<void>('/products/' + id);
      return res.payload;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.products.all,
      });
      toast.success('Product deleted successfully!');
    },
  });
};

export const useCreateProductOption = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      productId,
      payload,
    }: { productId: string; payload: CreateOption }) => {
      const res = await getHttpClient().post<Option>(
        `/products/${productId}/options`,
        payload,
      );
      return res.payload;
    },
    onSuccess: async (data, variables) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.products.all,
      });
      toast.success('Option created successfully!');
    },
  });
};

export const useUpdateProductOption = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      productId,
      payload,
    }: { productId: string; payload: UpdateOption }) => {
      const res = await getHttpClient().put<Option>(
        `/products/${productId}/options/${payload.id}`,
        payload,
      );
      return res.payload;
    },
    onSuccess: async (data, variables) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.products.all,
      });
      toast.success('Option updated successfully!');
    },
  });
};

export const useDeleteProductOption = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      productId,
      optionId,
    }: { productId: string; optionId: string }) => {
      const res = await getHttpClient().delete<void>(
        `/products/${productId}/options/${optionId}`,
      );
      return res.payload;
    },
    onSuccess: async (data, variables) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.products.all,
      });
      toast.success('Option deleted successfully!');
    },
  });
};

export const useCreateProductVariant = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      productId,
      payload,
    }: { productId: string; payload: CreateVariant }) => {
      const http = getHttpClient();
      const res = await http.post<Variant>(
        `/products/${productId}/variants`,
        payload,
      );
      return res.payload;
    },
    onSuccess: async (data, variables) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.products.all,
      });
      toast.success('Variant created successfully!');
    },
  });
};

export const useUpdateProductVariant = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      productId,
      payload,
    }: { productId: string; payload: UpdateVariant }) => {
      const res = await getHttpClient().put<Variant>(
        `/products/${productId}/variants/${payload.id}`,
        payload,
      );
      return res.payload;
    },
    onSuccess: async (data, variables) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.products.all,
      });
      toast.success('Variant updated successfully!');
    },
  });
};

export const useDeleteProductVariant = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      productId,
      variantId,
    }: { productId: string; variantId: string }) => {
      const res = await getHttpClient().delete<void>(
        `/products/${productId}/variants/${variantId}`,
      );
      return res.payload;
    },
    onSuccess: async (data, variables) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.products.all,
      });
      toast.success('Variant deleted successfully!');
    },
  });
};

// Product Image Management
export const useUploadProductImages = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ id, images }: { id: string; images: File[] }) => {
      const formData = new FormData();
      for (const image of images) {
        formData.append('images', image);
      }

      const res = await getHttpClient().post<Product>(
        `/products/${id}/images`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );
      return res.payload;
    },
    onSuccess: async (data, variables) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.products.all,
      });
      await queryClient.invalidateQueries({
        queryKey: queryKeys.products.detail(variables.id),
      });
      toast.success('Product images uploaded successfully!');
    },
  });
};

export const useDeleteProductImage = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ id, imageId }: { id: string; imageId: string }) => {
      const res = await getHttpClient().delete<Product>(
        `/products/${id}/images/${imageId}`,
      );
      return res.payload;
    },
    onSuccess: async (data, variables) => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.products.all,
      });
      await queryClient.invalidateQueries({
        queryKey: queryKeys.products.detail(variables.id),
      });
      toast.success('Product image deleted successfully!');
    },
  });
};

export const useCreateProductWithImages = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (formData: FormData) => {
      const res = await getHttpClient().post<Product>(
        '/products/with-images',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );
      return res.payload;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKeys.products.all,
      });
      toast.success('Product created successfully!');
    },
  });
};
