import { useQuery } from '@tanstack/react-query';
import { getHttpClient } from '../lib/http';
import type { ApiResponse } from '../types';
import { queryKeys } from '../utils/query-keys';

export interface CategoryData {
  name: string;
  value: number;
}

export interface OrderStatusData {
  name: string;
  value: number;
}

export interface TopProductData {
  name: string;
  sales: number;
}

export const useGetDashboardStats = () => {
  const { isPending, error, data } = useQuery({
    queryKey: queryKeys.dashboard.stats(),
    queryFn: async () => {
      const res = await getHttpClient().get<{
        totalSales: number;
        totalOrders: number;
        totalProducts: number;
        totalCustomers: number;
      }>('/dashboard/stats');
      return res.payload;
    },
  });
  return {
    isPending,
    error,
    stats: data || {
      totalSales: 0,
      totalOrders: 0,
      totalProducts: 0,
      totalCustomers: 0,
    },
  };
};

export const useGetSalesData = (months = 6) => {
  const { isPending, error, data } = useQuery({
    queryKey: [...queryKeys.dashboard.all, 'sales', months],
    queryFn: async () => {
      const res = await getHttpClient().get<
        {
          month: string;
          sales: number;
        }[]
      >(`/dashboard/sales?months=${months}`);
      return res.payload;
    },
  });
  return {
    isPending,
    error,
    sales: data,
  };
};

export const useGetCustomerGrowth = (months = 6) => {
  const { isPending, error, data } = useQuery({
    queryKey: [...queryKeys.dashboard.all, 'customer-growth', months],
    queryFn: async () => {
      const res = await getHttpClient().get<
        {
          month: string;
          new: number;
          returning: number;
        }[]
      >(`/dashboard/customer-growth?months=${months}`);
      return res.payload;
    },
  });
  return {
    isPending,
    error,
    customerGrowth: data,
  };
};

export const useGetProductCategories = () => {
  const { isPending, error, data } = useQuery({
    queryKey: queryKeys.dashboard.productCategories(),
    queryFn: async () => {
      const res = await getHttpClient().get<CategoryData[]>(
        '/dashboard/product-categories',
      );
      return res.payload;
    },
  });
  return {
    isPending,
    error,
    productCategories: data,
  };
};

export const useGetOrderStatus = () => {
  const { isPending, error, data } = useQuery({
    queryKey: queryKeys.dashboard.orderStatus(),
    queryFn: async () => {
      const res = await getHttpClient().get<OrderStatusData[]>(
        '/dashboard/order-status',
      );
      return res.payload;
    },
  });
  return {
    isPending,
    error,
    orderStatus: data,
  };
};

export const useGetTopProducts = (limit = 5) => {
  const { isPending, error, data } = useQuery({
    queryKey: queryKeys.dashboard.topProducts(limit),
    queryFn: async () => {
      const res = await getHttpClient().get<TopProductData[]>(
        `/dashboard/top-products?limit=${limit}`,
      );
      return res.payload;
    },
  });
  return {
    isPending,
    error,
    topProducts: data,
  };
};
