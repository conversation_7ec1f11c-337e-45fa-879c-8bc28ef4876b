import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import React, { type ReactNode, useEffect } from 'react';
import {
  HttpClient,
  type HttpClientConfig,
  setDefaultHttpClient,
} from '../lib/http';

interface ApiClientProviderProps {
  children: ReactNode;
  config: HttpClientConfig;
  queryClient?: QueryClient;
  enableDevtools?: boolean;
}

const defaultQueryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        return failureCount < 3;
      },
    },
    mutations: {
      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      retry: (failureCount, error: any) => {
        if (error?.status >= 401 && error?.status < 500) {
          return false;
        }
        return failureCount < 3;
      },
    },
  },
});

export const ApiClientProvider = ({
  children,
  config,
  queryClient = defaultQueryClient,
  enableDevtools,
}: ApiClientProviderProps) => {
  useEffect(() => {
    const httpClient = new HttpClient(config);
    setDefaultHttpClient(httpClient);
  }, [config]);

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {enableDevtools && <ReactQueryDevtools initialIsOpen={false} />}
    </QueryClientProvider>
  );
};
