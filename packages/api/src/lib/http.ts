import axios, { type AxiosError, type AxiosInstance } from 'axios';
import <PERSON><PERSON> from 'js-cookie';
import type { ApiError, ApiResponse } from '../types';

export interface HttpClientConfig {
  baseURL: string;
  timeout?: number;
  getAuthToken?: () => string | null;
  onError?: (error: ApiError) => void;
}

export class HttpClient {
  private instance: AxiosInstance;
  private config: HttpClientConfig;

  constructor(config: HttpClientConfig) {
    this.config = config;

    this.instance = axios.create({
      baseURL: config.baseURL,
      timeout: config.timeout || 30000,
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      withCredentials: true,
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor for auth.ts
    this.instance.interceptors.request.use(
      (config) => {
        const token = this.config.getAuthToken?.() || Cookie.get('session');
        if (token && config.headers) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error),
    );

    // Response interceptor for error handling
    this.instance.interceptors.response.use(
      (response) => response,
      <T>(error: AxiosError<ApiResponse<T>>) => {
        const { response, status, code } = error;
        const data = response?.data;

        const apiError: ApiError = {
          code: data?.error?.code || code,
          message:
            data?.message ||
            error.message ||
            'Something went wrong! Please try again later.',
          status,
        };

        this.config.onError?.(apiError);
        return Promise.reject(apiError);
      },
    );
  }

  async get<T>(url: string): Promise<ApiResponse<T>> {
    const response = await this.instance.get<ApiResponse<T>>(url);
    return response.data;
  }

  async post<T>(
    url: string,
    data?: unknown,
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    config?: any,
  ): Promise<ApiResponse<T>> {
    const response = await this.instance.post<ApiResponse<T>>(
      url,
      data,
      config,
    );
    return response.data;
  }

  async put<T>(url: string, data?: unknown): Promise<ApiResponse<T>> {
    const response = await this.instance.put<ApiResponse<T>>(url, data);
    return response.data;
  }

  async patch<T>(url: string, data?: unknown): Promise<ApiResponse<T>> {
    const response = await this.instance.patch<ApiResponse<T>>(url, data);
    return response.data;
  }

  async delete<T>(url: string): Promise<ApiResponse<T>> {
    const response = await this.instance.delete<ApiResponse<T>>(url);
    return response.data;
  }
}

// Default instance - will be configured by the provider
let defaultHttpClient: HttpClient | null = null;

export const setDefaultHttpClient = (client: HttpClient) => {
  defaultHttpClient = client;
};

export const getHttpClient = (): HttpClient => {
  if (!defaultHttpClient) {
    throw new Error(
      'HttpClient not configured. Make sure to wrap your app with ApiClientProvider.',
    );
  }
  return defaultHttpClient;
};
