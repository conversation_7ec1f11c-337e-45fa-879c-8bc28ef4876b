import type { OrderChangesQueryParams } from '../hooks';
import type {
  CustomersQueryParams,
  InventoryQueryParams,
  OrdersQueryParams,
  ProductsQueryParams,
} from '../types';

export const queryKeys = {
  // Products
  products: {
    all: ['products'] as const,
    lists: () => [...queryKeys.products.all, 'list'] as const,
    list: (params: ProductsQueryParams) =>
      [...queryKeys.products.lists(), params] as const,
    details: () => [...queryKeys.products.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.products.details(), id] as const,
    stats: () => [...queryKeys.products.all, 'stats'] as const,
  },

  // Orders
  orders: {
    all: ['orders'] as const,
    lists: () => [...queryKeys.orders.all, 'list'] as const,
    list: (params: OrdersQueryParams) =>
      [...queryKeys.orders.lists(), params] as const,
    details: () => [...queryKeys.orders.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.orders.details(), id] as const,
    stats: (statsType?: string) =>
      [...queryKeys.orders.all, 'stats', statsType] as const,
    changes: (orderId: string, params?: OrderChangesQueryParams) =>
      [...queryKeys.orders.all, 'changes', orderId, params] as const,
  },

  // Customers
  customers: {
    all: ['customers'] as const,
    lists: () => [...queryKeys.customers.all, 'list'] as const,
    list: (params: CustomersQueryParams) =>
      [...queryKeys.customers.lists(), params] as const,
    details: () => [...queryKeys.customers.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.customers.details(), id] as const,
    stats: () => [...queryKeys.customers.all, 'stats'] as const,
  },

  // Categories
  categories: {
    all: ['categories'] as const,
    lists: () => [...queryKeys.categories.all, 'list'] as const,
    details: () => [...queryKeys.categories.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.categories.details(), id] as const,
    stats: () => [...queryKeys.categories.all, 'stats'] as const,
  },

  // Brands
  brands: {
    all: ['brands'] as const,
    lists: () => [...queryKeys.brands.all, 'list'] as const,
    details: () => [...queryKeys.brands.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.brands.details(), id] as const,
    stats: () => [...queryKeys.brands.all, 'stats'] as const,
  },

  // Inventory
  inventory: {
    all: ['inventory'] as const,
    lists: () => [...queryKeys.inventory.all, 'list'] as const,
    list: (params: InventoryQueryParams) =>
      [...queryKeys.inventory.lists(), params] as const,
    stats: () => [...queryKeys.inventory.all, 'stats'] as const,
  },

  // Countries
  countries: { all: ['countries'] as const },

  // Cities
  cities: { all: ['cities'] as const },

  // Districts
  districts: { all: ['districts'] as const },

  // Locations
  locations: {
    all: ['locations'] as const,
    lists: () => [...queryKeys.locations.all, 'list'] as const,
  },

  // Option Values
  optionValues: {
    all: ['option-values'] as const,
    lists: () => [...queryKeys.optionValues.all, 'list'] as const,
    byOption: (optionId: string) =>
      [...queryKeys.optionValues.all, 'option', optionId] as const,
  },

  // Dashboard
  dashboard: {
    all: ['dashboard'] as const,
    stats: () => [...queryKeys.dashboard.all, 'stats'] as const,
    salesOverTime: (period: string) =>
      [...queryKeys.dashboard.all, 'sales-over-time', period] as const,
    productCategories: () =>
      [...queryKeys.dashboard.all, 'product-categories'] as const,
    orderStatus: () => [...queryKeys.dashboard.all, 'order-status'] as const,
    topProducts: (limit: number) =>
      [...queryKeys.dashboard.all, 'top-products', limit] as const,
  },
} as const;
