import type { BaseQueryParams } from '../types';

export const buildQueryParams = (params: BaseQueryParams): URLSearchParams => {
  const queryParams = new URLSearchParams();

  for (const key in params) {
    const typedKey = key as keyof BaseQueryParams;
    const value = params[typedKey];
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        for (const item of value) {
          if (item !== undefined && item !== null && item !== '') {
            queryParams.append(key, String(item));
          }
        }
      } else {
        queryParams.append(key, String(value));
      }
    }
  }

  return queryParams;
};

export const buildQueryString = (params: BaseQueryParams): string => {
  const queryParams = buildQueryParams(params);
  const queryString = queryParams.toString();
  return queryString ? `?${queryString}` : '';
};
