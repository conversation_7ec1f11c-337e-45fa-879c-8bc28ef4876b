export type ApiResponse<T> = {
  status: number;
  message: string;
  payload: T;
  error?: {
    code: string;
    details?: unknown;
  };
};

export type PaginationResponse<T> = {
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
};

export type ApiError = {
  code?: string;
  message: string;
  status?: number;
};

export interface BaseQueryParams {
  q?: string;
  page?: number;
  pageSize?: number;
  sortOrder?: 'asc' | 'desc';
}

export interface ProductsQueryParams extends BaseQueryParams {
  categoryId?: string;
  brandId?: string;
  minPrice?: number;
  maxPrice?: number;
  isPublished?: boolean;
}

export interface OrdersQueryParams extends BaseQueryParams {
  status?: string;
  customerId?: string;
  startDate?: string;
  endDate?: string;
  serviceType?: ('retail' | 'consignment' | 'buyback' | 'trade-in')[];
  locationId?: string;
  statsType?: 'monetary' | 'quality-check' | 'platform';
}

export interface CustomersQueryParams extends BaseQueryParams {
  startDate?: string;
  endDate?: string;
  hasUser?: boolean;
}

export interface InventoryQueryParams extends BaseQueryParams {
  status?: string;
  variantId?: string;
  locationId?: string;
}
