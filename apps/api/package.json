{"name": "api", "version": "0.9.2-alpha", "scripts": {"dev": "wrangler dev --port 9921", "test": "vitest", "deploy": "wrangler deploy"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.53", "@cloudflare/workers-types": "^4.20250712.0", "@muraadso/db": "workspace:*", "@muraadso/models": "workspace:*", "@muraadso/tsconfig": "workspace:*", "@types/bcryptjs": "^3.0.0", "@types/react": "^19", "@vitest/coverage-v8": "catalog:", "typescript": "^5", "vitest": "catalog:", "wrangler": "catalog:"}, "dependencies": {"@hono/zod-validator": "^0.7.0", "bcryptjs": "^3.0.2", "hono": "^4.7.10", "zod": "catalog:"}}