import type { SafeUser, User } from '@muraadso/models/users';

export const sanitizeUser = (data: User | SafeUser): SafeUser => {
  return {
    id: data.id,
    createdAt: data.createdAt,
    updatedAt: data.updatedAt,
    email: data.email,
    emailVerifiedAt: data.emailVerifiedAt,
    phone: data.phone,
    phoneVerifiedAt: data.phoneVerifiedAt,
    invitedAt: data.invitedAt,
    lastSignInAt: data.lastSignInAt,
    isBanned: data.isBanned,
    isAnonymous: false,
    isSysop: data.isSysop,
  };
};
