import type { Context, <PERSON>rror<PERSON>and<PERSON> } from 'hono';
import { HTTPException } from 'hono/http-exception';
import { $ZodError } from 'zod/v4/core';
import { buildJsonResponse } from '../response';
import { DefaultErrorMessages, HttpStatusCode } from './constants';
import { BaseError, DatabaseError, ValidationError } from './custom-errors';

export const errorHandler: ErrorHandler = (err: Error, c: Context) => {
  let message =
    err.message || DefaultErrorMessages[HttpStatusCode.INTERNAL_SERVER_ERROR];
  let status = HttpStatusCode.INTERNAL_SERVER_ERROR;
  let code = 'INTERNAL_SERVER_ERROR';
  let details = undefined;

  if (err instanceof BaseError) {
    message = err.message;
    status = err.statusCode;
    code = err.code;
    details = err.context;
  }

  if (err instanceof HTTPException) {
    status = err.status as HttpStatusCode;
    message = err.message || DefaultErrorMessages[status] || message;
    code =
      Object.keys(HttpStatusCode).find(
        (key) =>
          HttpStatusCode[key as keyof typeof HttpStatusCode] === err.status,
      ) || code;
  }

  if (err instanceof DatabaseError) {
    details = {
      cause: err.cause?.message,
    };
  }

  if (err instanceof $ZodError) {
    const validationError = ValidationError.fromZod(err, {
      path: c.req.path,
      method: c.req.method,
    });
    message = validationError.message;
    status = validationError.statusCode;
    code = validationError.code;
    details = {
      issues: err.issues.map((issue) => ({
        path: issue.path,
        code: issue.code,
        message: issue.message,
      })),
    };
  }

  if (status === HttpStatusCode.TOO_MANY_REQUESTS) {
    const retryAfter =
      err instanceof BaseError
        ? err.context?.retryAfter
        : // biome-ignore lint/suspicious/noExplicitAny: <explanation>
          (err as any).context?.retryAfter;

    if (retryAfter) {
      c.res.headers.set('Retry-After', retryAfter.toString());
    }
  }

  return buildJsonResponse({
    c,
    status,
    message,
    error: {
      code,
      details: details,
    },
  });
};

export function handleZodError(
  result:
    | {
        success: true;
        data: unknown;
      }
    | {
        success: false;
        error: $ZodError;
      },
  c: Context,
) {
  if (!result.success) {
    const validationError = ValidationError.fromZodWithContext(result.error, c);
    return buildJsonResponse({
      c,
      status: validationError.statusCode,
      message: validationError.message,
      error: {
        code: validationError.code,
        details: {
          issues: result.error.issues.map((issue) => ({
            path: issue.path,
            code: issue.code,
            message: issue.message,
          })),
        },
      },
    });
  }
}
