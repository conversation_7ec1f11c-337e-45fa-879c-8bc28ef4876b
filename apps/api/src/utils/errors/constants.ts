export enum HttpStatusCode {
  // 2xx Success
  OK = 200,

  // 4xx Client Errors
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  PAYMENT_REQUIRED = 402,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  METHOD_NOT_ALLOWED = 405,
  NOT_ACCEPTABLE = 406,
  REQUEST_TIMEOUT = 408,
  CONFLICT = 409,
  GONE = 410,
  LENGTH_REQUIRED = 411,
  UNPROCESSABLE_ENTITY = 422,
  LOCKED = 423,
  TOO_MANY_REQUESTS = 429,

  // 5xx Server Errors
  INTERNAL_SERVER_ERROR = 500,
}

export enum ErrorCode {
  // General errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR',

  // Authentication/Authorization errors
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
  INVALID_EMAIL_OR_PASSWORD = 'INVALID_EMAIL_OR_PASSWORD',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',

  // Validation errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INVALID_INPUT = 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',

  // Resource errors
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS = 'RESOURCE_ALREADY_EXISTS',
  RESOURCE_CONFLICT = 'RESOURCE_CONFLICT',

  // Database errors
  DATABASE_ERROR = 'DATABASE_ERROR',
  FOREIGN_KEY_VIOLATION = 'FOREIGN_KEY_VIOLATION',
  UNIQUE_CONSTRAINT_VIOLATION = 'UNIQUE_CONSTRAINT_VIOLATION',

  // Rate limiting
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
}

export const DefaultErrorMessages = {
  // 2xx Success
  [HttpStatusCode.OK]: 'Done',

  // --- Client Errors (4xx) ---
  [HttpStatusCode.BAD_REQUEST]:
    "Hmm... something's off. Please check your input and try again.",
  [HttpStatusCode.UNAUTHORIZED]:
    'You must be logged in to access this resource.',
  [HttpStatusCode.PAYMENT_REQUIRED]: 'Payment Required.',
  [HttpStatusCode.FORBIDDEN]:
    'Access denied. You do not have permission to perform this action.',
  [HttpStatusCode.NOT_FOUND]: 'The requested resource could not be found.',
  [HttpStatusCode.METHOD_NOT_ALLOWED]:
    'The request method is not supported for this resource.',
  [HttpStatusCode.NOT_ACCEPTABLE]:
    'Cannot generate a response acceptable to the client (check Accept headers).',
  [HttpStatusCode.REQUEST_TIMEOUT]: 'Server timed out waiting for the request.',
  [HttpStatusCode.CONFLICT]:
    "There's a conflict with the current state of the resource.",
  [HttpStatusCode.GONE]: 'The requested resource is no longer available.',
  [HttpStatusCode.LENGTH_REQUIRED]: 'Request requires a Content-Length header.',
  [HttpStatusCode.UNPROCESSABLE_ENTITY]:
    'Request was well-formed but contained semantic errors.',
  [HttpStatusCode.LOCKED]: 'The resource is locked.',
  [HttpStatusCode.TOO_MANY_REQUESTS]:
    'Rate limit exceeded. Please slow down and try again later.',

  // --- Server Errors (5xx) ---
  [HttpStatusCode.INTERNAL_SERVER_ERROR]:
    'An unexpected error occurred on the server.',
};

export const ErrorCodeToStatusCode: Record<ErrorCode, HttpStatusCode> = {
  // General errors
  [ErrorCode.UNKNOWN_ERROR]: HttpStatusCode.INTERNAL_SERVER_ERROR,
  [ErrorCode.INTERNAL_ERROR]: HttpStatusCode.INTERNAL_SERVER_ERROR,

  // Authentication/Authorization errors
  [ErrorCode.AUTHENTICATION_FAILED]: HttpStatusCode.UNAUTHORIZED,
  [ErrorCode.INVALID_EMAIL_OR_PASSWORD]: HttpStatusCode.UNAUTHORIZED,
  [ErrorCode.TOKEN_EXPIRED]: HttpStatusCode.UNAUTHORIZED,
  [ErrorCode.INSUFFICIENT_PERMISSIONS]: HttpStatusCode.FORBIDDEN,

  // Validation errors
  [ErrorCode.VALIDATION_ERROR]: HttpStatusCode.UNPROCESSABLE_ENTITY,
  [ErrorCode.INVALID_INPUT]: HttpStatusCode.BAD_REQUEST,
  [ErrorCode.MISSING_REQUIRED_FIELD]: HttpStatusCode.BAD_REQUEST,

  // Resource errors
  [ErrorCode.RESOURCE_NOT_FOUND]: HttpStatusCode.NOT_FOUND,
  [ErrorCode.RESOURCE_ALREADY_EXISTS]: HttpStatusCode.CONFLICT,
  [ErrorCode.RESOURCE_CONFLICT]: HttpStatusCode.CONFLICT,

  // Database errors
  [ErrorCode.DATABASE_ERROR]: HttpStatusCode.INTERNAL_SERVER_ERROR,
  [ErrorCode.FOREIGN_KEY_VIOLATION]: HttpStatusCode.CONFLICT,
  [ErrorCode.UNIQUE_CONSTRAINT_VIOLATION]: HttpStatusCode.CONFLICT,

  // Rate limiting
  [ErrorCode.RATE_LIMIT_EXCEEDED]: HttpStatusCode.TOO_MANY_REQUESTS,
};
