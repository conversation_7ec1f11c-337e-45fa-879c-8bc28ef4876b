import type { Drizzle<PERSON><PERSON>yError, PostgresError } from '@muraadso/db/errors';
import type { Context } from 'hono';
import type { $ZodError, $ZodIssue } from 'zod/v4/core';
import { parseZodIssues } from '../zod';
import { DefaultErrorMessages, ErrorCode, HttpStatusCode } from './constants';

export interface ErrorContext {
  [key: string]: unknown;
}

export interface HttpErrorContext extends ErrorContext {
  url?: string;
  method?: string;
  statusCode?: number;
  retryAfter?: number;
}

export interface ValidationErrorContext extends ErrorContext {
  raw?: unknown;
  issues?: $ZodIssue[];
}

export interface DatabaseErrorContext extends ErrorContext {
  code?: string;
  detail?: string;
  hint?: string;
  constraint?: string;
  table?: string;
  column?: string;
}

export abstract class BaseError<
  TContext extends ErrorContext = ErrorContext,
> extends Error {
  public abstract readonly name: string;

  public readonly context?: TContext;
  public readonly statusCode: HttpStatusCode;
  public readonly code: string;
  public readonly cause?: Error;

  protected constructor(options: {
    message: string;
    statusCode?: HttpStatusCode;
    code?: string;
    cause?: Error;
    context?: TContext;
  }) {
    super(options.message);

    Object.setPrototypeOf(this, new.target.prototype);

    this.statusCode =
      options.statusCode ?? HttpStatusCode.INTERNAL_SERVER_ERROR;
    this.code = options.code ?? 'INTERNAL_ERROR';
    this.cause = options.cause;
    this.context = options.context;
  }

  public toJSON(): Record<string, unknown> {
    return {
      name: this.name,
      message: this.message,
      statusCode: this.statusCode,
      code: this.code,
      context: this.context,
      stack: this.stack,
      cause:
        this.cause instanceof BaseError
          ? this.cause.toJSON()
          : this.cause
            ? { message: this.cause.message, stack: this.cause.stack }
            : undefined,
    };
  }

  public toString(): string {
    return `${this.name} [${this.statusCode}]: ${this.message}`;
  }
}

export class HttpError extends BaseError<HttpErrorContext> {
  public readonly name: string = 'HttpError';

  constructor(options: {
    statusCode: HttpStatusCode;
    message?: string;
    code?: string;
    cause?: Error;
    context?: HttpErrorContext;
  }) {
    super({
      message:
        options.message ??
        DefaultErrorMessages[options.statusCode] ??
        'An error occurred',
      statusCode: options.statusCode,
      code:
        options.code ??
        Object.keys(HttpStatusCode).find(
          (key) =>
            HttpStatusCode[key as keyof typeof HttpStatusCode] ===
            options.statusCode,
        ) ??
        'UNKNOWN_ERROR',
      cause: options.cause,
      context: options.context,
    });
  }

  public static fromResponse(request: Request, response: Response): HttpError {
    const httpStatusCode = response.status as HttpStatusCode;
    return new HttpError({
      statusCode: response.status,
      message:
        response.statusText ||
        DefaultErrorMessages[httpStatusCode] ||
        'HTTP request failed',
      context: {
        url: request.url,
        method: request.method,
        statusCode: response.status,
      },
    });
  }
}

export class ValidationError extends BaseError<ValidationErrorContext> {
  public readonly name: string = 'ValidationError';

  constructor(options: {
    message: string;
    statusCode?: HttpStatusCode;
    code?: string;
    cause?: Error;
    context?: ValidationErrorContext;
  }) {
    super({
      message: options.message,
      statusCode: options.statusCode ?? HttpStatusCode.UNPROCESSABLE_ENTITY,
      code: options.code ?? ErrorCode.VALIDATION_ERROR,
      cause: options.cause,
      context: options.context,
    });
  }

  public static fromZod(
    error: $ZodError,
    raw?: unknown,
    options: {
      statusCode?: HttpStatusCode;
      code?: string;
      includeRaw?: boolean;
      maxMessageLength?: number;
    } = {},
  ): ValidationError {
    const message = parseZodIssues(error.issues, options.maxMessageLength);

    return new ValidationError({
      message,
      statusCode: options.statusCode ?? HttpStatusCode.UNPROCESSABLE_ENTITY,
      code: options.code ?? ErrorCode.VALIDATION_ERROR,
      cause: error,
      context: {
        issues: error.issues,
        raw: options.includeRaw ? raw : undefined,
      },
    });
  }

  public static fromZodWithContext(
    error: $ZodError,
    c: Context,
    options: {
      statusCode?: HttpStatusCode;
      code?: string;
      includeRaw?: boolean;
      maxMessageLength?: number;
    } = {},
  ): ValidationError {
    return ValidationError.fromZod(
      error,
      { path: c.req.path, method: c.req.method },
      options,
    );
  }
}

export class DatabaseError extends BaseError<DatabaseErrorContext> {
  public readonly name: string = 'DatabaseError';

  constructor(options: {
    message: string;
    statusCode?: HttpStatusCode;
    code?: string;
    cause?: Error;
    context?: DatabaseErrorContext;
  }) {
    super({
      message: options.message,
      statusCode: options.statusCode ?? HttpStatusCode.INTERNAL_SERVER_ERROR,
      code: options.code ?? ErrorCode.DATABASE_ERROR,
      cause: options.cause,
      context: options.context,
    });
  }

  public static fromPostgresError(error: PostgresError): DatabaseError {
    let statusCode = HttpStatusCode.INTERNAL_SERVER_ERROR;
    let message = 'A database error occurred';
    let code = ErrorCode.DATABASE_ERROR;

    const context: DatabaseErrorContext = {
      code: error.code,
      detail: error.detail || undefined,
      hint: error.hint || undefined,
      constraint: error.constraint_name || undefined,
      table: error.table_name || undefined,
      column: error.column_name || undefined,
    };

    switch (error.code) {
      // Foreign key violation
      case '23503': {
        statusCode = HttpStatusCode.CONFLICT;
        message =
          'Cannot modify this record because other records depend on it, or do not exist';
        code = ErrorCode.FOREIGN_KEY_VIOLATION;
        break;
      }

      // Unique constraint violation
      case '23505': {
        statusCode = HttpStatusCode.CONFLICT;
        message = error.detail?.includes('already exists')
          ? `A record with the same ${error.constraint_name?.replace(/_pkey$/, '') || 'identifier'} already exists`
          : 'A record with the same unique identifier already exists';
        code = ErrorCode.UNIQUE_CONSTRAINT_VIOLATION;
        break;
      }

      // Check constraint violation
      case '23514': {
        statusCode = HttpStatusCode.BAD_REQUEST;
        message = 'The data violates a check constraint';
        code = ErrorCode.INVALID_INPUT;
        break;
      }

      // Not null violation
      case '23502': {
        statusCode = HttpStatusCode.BAD_REQUEST;
        message = `The field '${error.column_name || 'unknown'}' cannot be null`;
        code = ErrorCode.MISSING_REQUIRED_FIELD;
        break;
      }

      // Invalid text representation
      case '22P02': {
        statusCode = HttpStatusCode.BAD_REQUEST;
        message = 'Invalid data format';
        code = ErrorCode.INVALID_INPUT;
        break;
      }

      // Division by zero
      case '22012': {
        statusCode = HttpStatusCode.BAD_REQUEST;
        message = 'Division by zero error';
        code = ErrorCode.INVALID_INPUT;
        break;
      }

      // Insufficient privileges
      case '42501': {
        statusCode = HttpStatusCode.FORBIDDEN;
        message = 'Insufficient database privileges to perform this operation';
        code = ErrorCode.INSUFFICIENT_PERMISSIONS;
        break;
      }

      // Connection failure
      case '08006': {
        statusCode = HttpStatusCode.INTERNAL_SERVER_ERROR;
        message = 'Database connection failure';
        code = ErrorCode.INTERNAL_ERROR;
        break;
      }

      // default: {
      //   if (error.message) {
      //     message = error.message;
      //   }
      //   break;
      // }
    }

    return new DatabaseError({
      message,
      statusCode,
      code,
      cause: error,
      context,
    });
  }
}
