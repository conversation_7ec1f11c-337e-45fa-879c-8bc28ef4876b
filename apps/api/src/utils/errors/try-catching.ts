import { Drizzle<PERSON><PERSON><PERSON>Error, PostgresError } from '@muraadso/db/errors';
import { HttpStatusCode } from './constants';
import { BaseError, DatabaseError, HttpError } from './custom-errors';

export const tryCatching = async <T>(action: () => Promise<T> | T) => {
  try {
    return await action();
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  } catch (error: any) {
    if (error instanceof BaseError) {
      throw error;
    }

    if (error instanceof Response) {
      throw HttpError.fromResponse(new Request('unknown'), error);
    }

    if (error instanceof DrizzleQueryError) {
      throw new DatabaseError({
        message:
          error.cause?.message ||
          'An unexpected error occurred, please try again or contact support',
        cause: error,
      });
    }

    if (error instanceof PostgresError) {
      throw DatabaseError.fromPostgresError(error);
    }

    throw new HttpError({
      statusCode: HttpStatusCode.INTERNAL_SERVER_ERROR,
      message: error.message || 'An unexpected error occurred',
      cause: error,
    });
  }
};
