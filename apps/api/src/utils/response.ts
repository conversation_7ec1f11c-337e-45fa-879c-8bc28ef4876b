import type { Context } from 'hono';
import type { ContentfulStatusCode } from 'hono/utils/http-status';
import type { HttpStatusCode } from './errors/constants';

type ErrorDetails = Record<string, unknown>;

export type ApiResponse<P = unknown> = {
  message?: string;
  status: HttpStatusCode;
  payload?: P;
  error?: {
    code: string;
    details?: ErrorDetails;
  };
  meta: {
    timestamp: string;
    requestId: string;
    version: string;
  };
};

type ResponseOptions<P = unknown> =
  | {
      c: Context;
      status: HttpStatusCode;
      message: string;
      error?: {
        code: string;
        details?: ErrorDetails;
      };
      payload?: never;
    }
  | {
      c: Context;
      status?: HttpStatusCode;
      message?: string;
      payload?: P;
      error?: never;
    };

export const buildJsonResponse = <T = unknown>(options: ResponseOptions<T>) => {
  const { c, message, status = 200, payload, error } = options;
  const requestId = c.get('requestId') || 'unknown';

  const responseBody = {
    status,
    message,
    ...(payload ? { payload } : {}),
    ...(error ? { error } : {}),
    meta: {
      timestamp: new Date().toISOString(),
      requestId,
      version: '1.0',
    },
  };

  return c.json(responseBody, {
    status: status as ContentfulStatusCode,
    headers: {
      'Content-Type': 'application/json',
    },
  });
};
