import type { $ZodIssue } from 'zod/v4/core';

export const parseZodIssues = (
  issues: $ZodIssue[],
  maxLength?: number,
): string => {
  const message = issues
    .map((i) =>
      i.code === 'invalid_union'
        ? i.errors.map((ue) => parseZodIssues(ue)).join('; ')
        : i.message,
    )
    .join(', ');

  // Truncate the message if it's too long
  if (maxLength && message.length > maxLength) {
    return `${message.substring(0, maxLength - 3)}...`;
  }

  return message;
};
