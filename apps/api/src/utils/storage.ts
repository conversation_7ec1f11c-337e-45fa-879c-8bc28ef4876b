import type { Env } from '../env';
import { HttpError } from './errors/custom-errors';

export async function uploadObj(
  c: { env: Env['Bindings'] },
  file: File,
): Promise<string> {
  try {
    const fileExtension = file.name.split('.').pop() || '';
    const filePath = `${crypto.randomUUID()}-${Date.now()}.${fileExtension}`;
    await c.env.R2_BUCKET.put(filePath, await file.arrayBuffer());
    return filePath;
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  } catch (e: any) {
    throw new HttpError({
      statusCode: 500,
      message: `Failed to upload your file: ${e?.message}`,
    });
  }
}

export async function deleteObj(
  c: { env: Env['Bindings'] },
  url: string,
): Promise<boolean> {
  try {
    // Delete the file from R2
    await c.env.R2_BUCKET.delete(url);
    return true;
  } catch (_) {
    return false;
  }
}
