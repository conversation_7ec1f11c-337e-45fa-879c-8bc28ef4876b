// @ts-ignore
import { env } from 'cloudflare:test';
import type { Hono } from 'hono';
import type { Env } from '../env';

type RequestOptions<T = unknown> = {
  body?: T;
  headers?: Headers;
  bearer?: string;
};

export const apiRequest = (app: Hono<Env>) => {
  return {
    get: (path: string, options?: RequestOptions) =>
      sendReq(app, path, 'GET', options),
    post: (path: string, options?: RequestOptions) =>
      sendReq(app, path, 'POST', options),
    put: (path: string, options?: RequestOptions) =>
      sendReq(app, path, 'PUT', options),
    patch: (path: string, options?: RequestOptions) =>
      sendReq(app, path, 'PATCH', options),
    delete: (path: string, options?: RequestOptions) =>
      sendReq(app, path, 'DELETE', options),
  };
};

const sendReq = async (
  app: Hono<Env>,
  path: string,
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE',
  options?: RequestOptions,
): Promise<Response> => {
  const headers = new Headers(options?.headers);
  if (options?.body !== undefined && !headers.has('Content-Type')) {
    headers.set('Content-Type', 'application/json');
    headers.set('Accept', 'application/json');
  }

  // if (options?.bearer) {
  //   headers.set('Authorization', 'Bearer ' + btoa(options.bearer));
  // }

  const req: RequestInit = {
    method,
    headers,
  };

  if (options?.body) {
    req.body = JSON.stringify(options.body);
  }

  return app.request(path, req, {
    server: {
      requestIP: () => {
        return {
          address: '127.0.0.1',
          family: 'foo',
          port: '123',
        };
      },
    },
    ...env,
  });
};
