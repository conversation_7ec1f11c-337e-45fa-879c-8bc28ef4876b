type Props = {
  email: string;
  link: string;
  expiryMinutes: number;
};

export const InviteUser = ({ link, email, expiryMinutes }: Props) => {
  return (
    <div
      style={{
        fontFamily:
          '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
        maxWidth: '600px',
        margin: '0 auto',
        padding: '20px',
        backgroundColor: '#ffffff',
        borderRadius: '8px',
        color: '#333333',
      }}
    >
      <div
        style={{
          textAlign: 'center',
          marginBottom: '24px',
        }}
      >
        <div
          style={{
            fontSize: '24px',
            fontWeight: 'bold',
            color: '#4f46e5',
            padding: '16px 0',
          }}
        >
          Muraadso.com
        </div>
      </div>

      <div
        style={{
          padding: '20px',
          backgroundColor: '#f9fafb',
          borderRadius: '6px',
          marginBottom: '24px',
        }}
      >
        <h1
          style={{
            fontSize: '20px',
            fontWeight: '600',
            marginBottom: '16px',
            color: '#111827',
          }}
        >
          Your'e Invited
        </h1>

        <p
          style={{
            fontSize: '16px',
            lineHeight: '24px',
            marginBottom: '24px',
            color: '#4b5563',
          }}
        >
          Hello {email}
        </p>

        <p
          style={{
            fontSize: '16px',
            lineHeight: '24px',
            marginBottom: '24px',
            color: '#4b5563',
          }}
        >
          Please use this <a href={link}>Link</a> to accept invitation.
        </p>

        <p
          style={{
            fontSize: '16px',
            lineHeight: '24px',
            marginBottom: '8px',
            color: '#4b5563',
          }}
        >
          This link will expire in {expiryMinutes} minutes.
        </p>
      </div>
    </div>
  );
};
