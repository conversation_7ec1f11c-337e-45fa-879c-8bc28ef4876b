import { eq } from '@muraadso/db';
import { sessions } from '@muraadso/db/schema/sessions';
import type { Context } from 'hono';
import { bearerAuth } from 'hono/bearer-auth';
import { createMiddleware } from 'hono/factory';
import type { Env } from '../env';
import type { SessionCookie } from '../types';
import { tryCatching } from '../utils/errors/try-catching';
import { sanitizeUser } from '../utils/user';

export const requireAuth = createMiddleware(async (c: Context<Env>, next) => {
  const bearer = bearerAuth({
    async verifyToken(bearer): Promise<boolean> {
      return tryCatching(async () => {
        const { token }: SessionCookie = JSON.parse(atob(bearer));
        if (!token) return false;

        const db = c.get('db');
        const session = await db.query.sessions.findFirst({
          with: {
            user: true,
            // {
            // with: {
            //   userRoles: {
            //     columns: {},
            //     with: {
            //       role: true,
            //     },
            //   },
            // },
            // },
          },
          where: eq(sessions.token, token),
        });

        if (
          !session ||
          !session.user ||
          session.expiresAt < new Date() ||
          session.isRevoked
        ) {
          if (session) {
            await db.delete(sessions).where(eq(sessions.token, token));
          }
          return false;
        }

        c.set('session', session);
        c.set('user', {
          ...sanitizeUser(session.user),
          // roles: session.user.userRoles.map((it) => it.role),
        });

        return true;
      });
    },
  });

  return bearer(c, next);
});
