import { zValidator } from '@hono/zod-validator';
import {
  and,
  asc,
  count,
  eq,
  gte,
  ilike,
  inArray,
  lte,
  sum,
} from '@muraadso/db';
import { customers } from '@muraadso/db/schema/customers';
import { orderChanges, orderItems, orders } from '@muraadso/db/schema/orders';
import { products } from '@muraadso/db/schema/products';
import { sysops } from '@muraadso/db/schema/sysops';
import { variants } from '@muraadso/db/schema/variants';
import { createOrderChangeSchema } from '@muraadso/models/orderChanges';
import {
  createOrderSchema,
  orderStatus,
  qchStatus,
  serviceTypes,
  updateOrderSchema,
} from '@muraadso/models/orders';
import { Hono } from 'hono';
import { z } from 'zod/v4';
import type { Env } from '../env';
import { requireAuth } from '../middlewares/auth';
import { HttpError } from '../utils/errors/custom-errors';
import { handleZodError } from '../utils/errors/error-handler';
import { tryCatching } from '../utils/errors/try-catching';
import { buildJsonResponse } from '../utils/response';

// Helper function for quality check stats
const getQualityCheckStats = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  db: any,
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  whereClause: any,
  oneWeekAgo: Date,
) => {
  // Total orders in QCH process
  const [totalQchOrdersResult] = await db
    .select({ count: count() })
    .from(orders)
    .where(whereClause);
  const totalQchOrders = totalQchOrdersResult?.count || 0;

  // Orders passed QCH this week
  const [passedThisWeekResult] = await db
    .select({ count: count() })
    .from(orders)
    .where(
      and(
        whereClause,
        eq(orders.qchStatus, 'passed'),
        gte(orders.updatedAt, oneWeekAgo.toISOString()),
      ),
    );
  const passedThisWeek = passedThisWeekResult?.count || 0;

  // Orders failed QCH this week
  const [failedThisWeekResult] = await db
    .select({ count: count() })
    .from(orders)
    .where(
      and(
        whereClause,
        eq(orders.qchStatus, 'failed'),
        gte(orders.updatedAt, oneWeekAgo.toISOString()),
      ),
    );
  const failedThisWeek = failedThisWeekResult?.count || 0;

  // Orders pending QCH review (processing status)
  const [pendingQchResult] = await db
    .select({ count: count() })
    .from(orders)
    .where(and(whereClause, eq(orders.qchStatus, 'processing')));
  const pendingQch = pendingQchResult?.count || 0;

  // Calculate QCH pass rate
  const totalProcessed = passedThisWeek + failedThisWeek;
  const qchPassRate =
    totalProcessed > 0 ? (passedThisWeek / totalProcessed) * 100 : 0;

  return {
    totalQchOrders,
    passedThisWeek,
    failedThisWeek,
    pendingQch,
    qchPassRate,
  };
};

// Helper function for platform stats
const getPlatformStats = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  db: any,
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  whereClause: any,
  oneWeekAgo: Date,
) => {
  // Total platform orders
  const [totalPlatformOrdersResult] = await db
    .select({ count: count() })
    .from(orders)
    .where(whereClause);
  const totalPlatformOrders = totalPlatformOrdersResult?.count || 0;

  // Orders awaiting processing (pending status)
  const [awaitingProcessingResult] = await db
    .select({ count: count() })
    .from(orders)
    .where(and(whereClause, eq(orders.status, 'pending')));
  const awaitingProcessing = awaitingProcessingResult?.count || 0;

  // Orders completed this week
  const [completedThisWeekResult] = await db
    .select({ count: count() })
    .from(orders)
    .where(
      and(
        whereClause,
        eq(orders.status, 'completed'),
        gte(orders.updatedAt, oneWeekAgo.toISOString()),
      ),
    );
  const completedThisWeek = completedThisWeekResult?.count || 0;

  // Orders by service type breakdown
  const serviceTypeBreakdown = await db
    .select({
      serviceType: orders.serviceType,
      count: count(),
    })
    .from(orders)
    .where(whereClause)
    .groupBy(orders.serviceType);

  return {
    totalPlatformOrders,
    awaitingProcessing,
    completedThisWeek,
    serviceTypeBreakdown,
  };
};

const route = new Hono<Env>()
  .get(
    '/stats',
    requireAuth,
    zValidator(
      'query',
      z.object({
        serviceType: z
          .union([z.enum(serviceTypes), z.array(z.enum(serviceTypes))])
          .transform((value) => (Array.isArray(value) ? value : [value]))
          .optional(),
        locationId: z.uuid().optional(),
        statsType: z
          .enum(['monetary', 'quality-check', 'platform'])
          .default('monetary'),
      }),
    ),
    async (c) => {
      const db = c.get('db');
      const currentUser = c.get('user');
      const { serviceType, locationId, statsType } = c.req.valid('query');

      const result = await tryCatching(async () => {
        const whereConditions = [];
        const currentUserSysop = await db.query.sysops.findFirst({
          where: eq(sysops.userId, currentUser.id),
          with: {
            user: true,
          },
        });

        if (currentUserSysop && !currentUserSysop?.isSuperAdmin) {
          // Non-superadmin users can only see orders from their assigned location
          if (currentUserSysop.locationId) {
            whereConditions.push(
              eq(orders.locationId, currentUserSysop.locationId),
            );
          } else {
            // If sysop has no location assigned, they see no orders
            whereConditions.push(eq(orders.id, 'no-access'));
          }
        } else if (locationId) {
          // Superadmin users can filter by specific location
          whereConditions.push(eq(orders.locationId, locationId));
        }

        if (serviceType) {
          whereConditions.push(inArray(orders.serviceType, serviceType));
        }

        // Get current date and one week ago
        const now = new Date();
        const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

        const whereClause =
          whereConditions.length > 0 ? and(...whereConditions) : undefined;

        // Handle different stat types
        if (statsType === 'quality-check') {
          return await getQualityCheckStats(db, whereClause, oneWeekAgo);
        }

        if (statsType === 'platform') {
          return await getPlatformStats(db, whereClause, oneWeekAgo);
        }

        // Default monetary stats for orders page
        // Total orders count
        const [totalOrdersResult] = await db
          .select({ count: count() })
          .from(orders)
          .where(whereClause);

        const totalOrders = totalOrdersResult?.count || 0;

        // Orders created this week
        const [ordersThisWeekResult] = await db
          .select({ count: count() })
          .from(orders)
          .where(
            and(whereClause, gte(orders.createdAt, oneWeekAgo.toISOString())),
          );
        const ordersThisWeek = ordersThisWeekResult?.count || 0;

        // Total revenue
        const [totalRevenueResult] = await db
          .select({ total: sum(orders.totalAmount) })
          .from(orders)
          .where(whereClause);
        const totalRevenue = Number(totalRevenueResult?.total) || 0;

        // Revenue this week
        const [revenueThisWeekResult] = await db
          .select({ total: sum(orders.totalAmount) })
          .from(orders)
          .where(
            and(whereClause, gte(orders.createdAt, oneWeekAgo.toISOString())),
          );
        const revenueThisWeek = Number(revenueThisWeekResult?.total) || 0;

        // Revenue previous week for comparison
        const twoWeeksAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);
        const [revenuePreviousWeekResult] = await db
          .select({ total: sum(orders.totalAmount) })
          .from(orders)
          .where(
            and(
              whereClause,
              gte(orders.createdAt, twoWeeksAgo.toISOString()),
              lte(orders.createdAt, oneWeekAgo.toISOString()),
            ),
          );
        const revenuePreviousWeek =
          Number(revenuePreviousWeekResult?.total) || 0;

        // Calculate revenue percentage change
        const revenuePercentageChange =
          revenuePreviousWeek > 0
            ? ((revenueThisWeek - revenuePreviousWeek) / revenuePreviousWeek) *
              100
            : 0;

        // Average order value
        const averageOrderValue =
          totalOrders > 0 ? totalRevenue / totalOrders : 0;

        // Average order value this week
        const averageOrderValueThisWeek =
          ordersThisWeek > 0 ? revenueThisWeek / ordersThisWeek : 0;

        // Average order value previous week
        const [ordersPreviousWeekResult] = await db
          .select({ count: count() })
          .from(orders)
          .where(
            and(
              whereClause,
              gte(orders.createdAt, twoWeeksAgo.toISOString()),
              lte(orders.createdAt, oneWeekAgo.toISOString()),
            ),
          );
        const ordersPreviousWeek = ordersPreviousWeekResult?.count || 0;
        const averageOrderValuePreviousWeek =
          ordersPreviousWeek > 0 ? revenuePreviousWeek / ordersPreviousWeek : 0;

        // Calculate AOV percentage change
        const aovPercentageChange =
          averageOrderValuePreviousWeek > 0
            ? ((averageOrderValueThisWeek - averageOrderValuePreviousWeek) /
                averageOrderValuePreviousWeek) *
              100
            : 0;

        // Pending orders count
        const [pendingOrdersResult] = await db
          .select({ count: count() })
          .from(orders)
          .where(and(whereClause, eq(orders.status, 'pending')));
        const pendingOrders = pendingOrdersResult?.count;

        return {
          totalOrders,
          ordersThisWeek,
          totalRevenue,
          revenueThisWeek,
          revenuePercentageChange,
          averageOrderValue,
          averageOrderValueThisWeek,
          aovPercentageChange,
          pendingOrders,
        };
      });

      return buildJsonResponse({
        c,
        message: 'Order stats retrieved successfully',
        payload: result,
      });
    },
  )
  .get(
    '/',
    requireAuth,
    zValidator(
      'query',
      z.object({
        q: z.string().optional(),
        page: z.coerce.number().positive().default(1),
        pageSize: z.coerce.number().positive().max(100).default(10),
        customerId: z.uuid().optional(),
        serviceType: z
          .union([z.enum(serviceTypes), z.array(z.enum(serviceTypes))])
          .transform((value) => (Array.isArray(value) ? value : [value]))
          .optional(),
        status: z.enum(orderStatus).optional(),
        startDate: z
          .string()
          .optional()
          .transform((val) => (val ? new Date(val) : undefined)),
        endDate: z
          .string()
          .optional()
          .transform((val) => (val ? new Date(val) : undefined)),
        minAmount: z.coerce.number().positive().optional(),
        maxAmount: z.coerce.number().positive().optional(),
        sortBy: z
          .enum(['createdAt', 'updatedAt', 'totalAmount'])
          .default('createdAt'),
        sortOrder: z.enum(['asc', 'desc']).default('desc'),
        locationId: z.uuid().optional(),
      }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const currentUser = c.get('user');
      const {
        q,
        page,
        pageSize,
        customerId,
        serviceType,
        status,
        startDate,
        endDate,
        minAmount,
        maxAmount,
        sortBy,
        sortOrder,
        locationId,
      } = c.req.valid('query');

      const offset = (page - 1) * pageSize;

      // Get current user's sysop information for location-based access control
      const currentUserSysop = await db.query.sysops.findFirst({
        where: eq(sysops.userId, currentUser.id),
        with: {
          user: true,
        },
      });

      const whereConditions = [];

      // Location-based access control
      if (currentUserSysop && !currentUserSysop?.isSuperAdmin) {
        // Non-superadmin users can only see orders from their assigned location
        if (currentUserSysop.locationId) {
          whereConditions.push(
            eq(orders.locationId, currentUserSysop.locationId),
          );
        } else {
          // If sysop has no location assigned, they see no orders
          whereConditions.push(eq(orders.id, 'no-access'));
        }
      } else if (locationId) {
        // Superadmin users can filter by specific location
        whereConditions.push(eq(orders.locationId, locationId));
      }

      if (q) {
        whereConditions.push(ilike(orders.trackingId, `%${q}%`));
      }

      if (customerId) {
        whereConditions.push(eq(orders.customerId, customerId));
      }

      if (serviceType) {
        whereConditions.push(inArray(orders.serviceType, serviceType));
      }

      if (status) {
        whereConditions.push(eq(orders.status, status));
      }

      if (startDate) {
        whereConditions.push(gte(orders.createdAt, startDate.toISOString()));
      }

      if (endDate) {
        whereConditions.push(lte(orders.createdAt, endDate.toISOString()));
      }

      if (minAmount) {
        whereConditions.push(gte(orders.totalAmount, minAmount));
      }

      if (maxAmount) {
        whereConditions.push(lte(orders.totalAmount, maxAmount));
      }

      const whereClause =
        whereConditions.length > 0 ? and(...whereConditions) : undefined;

      const result = await tryCatching(async () => {
        // Get total count
        // @ts-ignore
        const [{ totalCount }] = await db
          .select({ totalCount: count() })
          .from(orders)
          .leftJoin(customers, eq(orders.customerId, customers.id))
          .where(whereClause);

        // Get orders with pagination
        const ordersData = await db.query.orders.findMany({
          with: {
            customer: true,
            location: true,
            items: {
              with: {
                product: true,
                variant: true,
              },
            },
          },
          where: whereClause,
          orderBy: (orders, { asc, desc }) => [
            sortOrder === 'asc' ? asc(orders[sortBy]) : desc(orders[sortBy]),
          ],
          limit: pageSize,
          offset,
        });

        return {
          orders: ordersData,
          totalCount,
        };
      });

      return buildJsonResponse({
        c,
        message: 'Orders retrieved successfully',
        payload: {
          orders: result.orders,
          pagination: {
            page,
            pageSize,
            totalCount: result.totalCount,
            totalPages: Math.ceil(result.totalCount / pageSize),
            hasNextPage: page < Math.ceil(result.totalCount / pageSize),
            hasPreviousPage: page > 1,
          },
        },
      });
    },
  )
  .get(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');

      const result = await tryCatching(async () => {
        const order = await db.query.orders.findFirst({
          where: eq(orders.id, id),
          with: {
            customer: {
              with: {
                user: true,
              },
            },
            location: true,
            items: {
              with: {
                product: true,
                variant: true,
              },
            },
          },
        });

        if (!order) {
          throw new HttpError({
            statusCode: 404,
            message: `Order with ID ${id} not found`,
          });
        }

        return order;
      });

      return buildJsonResponse({
        c,
        message: 'Order retrieved successfully',
        payload: result,
      });
    },
  )
  .post(
    '/',
    requireAuth,
    zValidator('json', createOrderSchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const {
        customerId,
        customer: customerSnapshot,
        locationId,
        guarantor,
        items,
        address,
        serviceType,
        commissionRate,
        status,
        note,
      } = c.req.valid('json');

      const result = await tryCatching(async () => {
        if (!customerId && !customerSnapshot)
          throw new HttpError({
            statusCode: 404,
            message: 'Customer not found',
          });
        return await db.transaction(async (tx) => {
          let resolvedCustomerId: string;
          if (customerId) {
            resolvedCustomerId = customerId;
            const existingCustomer = await tx.query.customers.findFirst({
              where: eq(customers.id, customerId),
            });

            if (!existingCustomer)
              throw new HttpError({
                statusCode: 404,
                message: 'Customer not found',
              });
          } else {
            const [newCustomer] = await tx
              .insert(customers)
              .values(customerSnapshot!)
              .returning({ id: customers.id });
            resolvedCustomerId = newCustomer!.id;
          }

          // Verify all products and variants exist and get their data for snapshots
          const itemsWithSnapshots = await Promise.all(
            items.map(async (item) => {
              const product = await tx.query.products.findFirst({
                with: {
                  category: true,
                  brand: true,
                  images: true,
                },
                where: eq(products.id, item.productId),
              });

              if (!product)
                throw new HttpError({
                  statusCode: 404,
                  message: `Product with ID ${item.productId} not found`,
                });

              const variant = await tx.query.variants.findFirst({
                where: eq(variants.id, item.variantId),
                with: {
                  variantImages: {
                    with: {
                      image: true,
                    },
                  },
                },
              });

              if (!variant)
                throw new HttpError({
                  statusCode: 404,
                  message: `Variant with ID ${item.variantId} not found`,
                });

              // Verify variant belongs to product
              if (variant.productId !== product.id)
                throw new HttpError({
                  statusCode: 400,
                  message: `Variant ${item.variantId} does not belong to product ${item.productId}`,
                });

              const { variantImages, ...rest } = variant;

              return {
                ...item,
                productSnapshot: product,
                variantSnapshot: {
                  ...rest,
                  images: variantImages.map(({ image, ...rest }) => ({
                    ...rest,
                    ...image,
                  })),
                },
              };
            }),
          );

          const totalAmount = items.reduce(
            (acc, item) => acc + item.unitPrice * item.quantity,
            0,
          );

          const [createdOrder] = await tx
            .insert(orders)
            .values({
              customerId: resolvedCustomerId,
              locationId,
              guarantor,
              address,
              serviceType,
              totalAmount,
              note,
              isPaid: false,
              commissionRate,
              status,
            })
            .returning();

          const orderItemsData = itemsWithSnapshots.map((item) => ({
            orderId: createdOrder!.id,
            productId: item.productId,
            variantId: item.variantId,
            productSnapshot: item.productSnapshot,
            variantSnapshot: item.variantSnapshot,
            unitPrice: item.unitPrice,
            quantity: item.quantity,
            metadata: item.metadata,
          }));

          await tx.insert(orderItems).values(orderItemsData);

          // Get the sysop record for the current user
          const currentSysop = await tx.query.sysops.findFirst({
            where: eq(sysops.userId, c.get('user').id),
          });

          // Create initial order change record for order creation
          await tx.insert(orderChanges).values({
            orderId: createdOrder!.id,
            sysopId: currentSysop?.id || null,
            changeType: 'other',
            previousValues: null,
            newValues: { status: 'pending' },
            comment: 'Order created',
          });

          return tx.query.orders.findFirst({
            where: eq(orders.id, createdOrder!.id),
            with: {
              customer: true,
              items: {
                with: {
                  product: true,
                  variant: true,
                },
              },
            },
          });
        });
      });

      return buildJsonResponse({
        c,
        status: 200,
        message: 'Order created successfully',
        payload: result,
      });
    },
  )
  .put(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator('json', updateOrderSchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');
      const updateData = c.req.valid('json');

      const result = await tryCatching(async () => {
        // Get the current order
        const currentOrder = await db.query.orders.findFirst({
          where: eq(orders.id, id),
        });

        if (!currentOrder) {
          throw new HttpError({
            statusCode: 404,
            message: `Order with ID ${id} not found`,
          });
        }

        // Update the order
        await db
          .update(orders)
          .set({
            ...updateData,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(orders.id, id))
          .returning();

        // Return the updated order with relations
        return db.query.orders.findFirst({
          where: eq(orders.id, id),
          with: {
            customer: {
              with: {
                user: true,
              },
            },
            location: true,
            items: {
              with: {
                product: true,
                variant: true,
              },
            },
          },
        });
      });

      return buildJsonResponse({
        c,
        message: 'Order updated successfully',
        payload: result,
      });
    },
  )
  .put(
    '/:id/status',
    requireAuth,
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator(
      'json',
      z.object({
        status: z.enum(orderStatus),
        comment: z.string().max(1000).optional(),
      }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');
      const { status, comment } = c.req.valid('json');

      const result = await tryCatching(async () => {
        // Get the current order
        const currentOrder = await db.query.orders.findFirst({
          where: eq(orders.id, id),
        });

        if (!currentOrder) {
          throw new HttpError({
            statusCode: 404,
            message: `Order with ID ${id} not found`,
          });
        }

        // Update the order
        await db
          .update(orders)
          .set({
            status,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(orders.id, id))
          .returning();

        // Get the sysop record for the current user
        const currentSysop = await db.query.sysops.findFirst({
          where: eq(sysops.userId, c.get('user').id),
        });

        // Create order change record
        await db.insert(orderChanges).values({
          orderId: id,
          sysopId: currentSysop?.id || null,
          changeType: 'status_update',
          previousValues: { status: currentOrder.status },
          newValues: { status },
          comment,
        });

        // Return the updated order with relations
        return db.query.orders.findFirst({
          where: eq(orders.id, id),
          with: {
            customer: {
              with: {
                user: true,
              },
            },
            location: true,
            items: {
              with: {
                product: true,
                variant: true,
              },
            },
          },
        });
      });

      return buildJsonResponse({
        c,
        message: 'Status updated successfully',
        payload: result,
      });
    },
  )
  .put(
    '/:id/qch-status',
    requireAuth,
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator(
      'json',
      z.object({
        qchStatus: z.enum(qchStatus),
        comment: z.string().max(1000).optional(),
      }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');
      const { qchStatus: newQchStatus, comment } = c.req.valid('json');

      const result = await tryCatching(async () => {
        // Get the current order
        const currentOrder = await db.query.orders.findFirst({
          where: eq(orders.id, id),
        });

        if (!currentOrder) {
          throw new HttpError({
            statusCode: 404,
            message: `Order with ID ${id} not found`,
          });
        }

        // Prepare update data
        // biome-ignore lint/suspicious/noExplicitAny: <explanation>
        const updateData: any = {
          qchStatus: newQchStatus,
          updatedAt: new Date().toISOString(),
        };

        // If QCH status is set to failed, automatically cancel the order
        if (newQchStatus === 'failed') {
          updateData.status = 'canceled';
        }

        // Update the order
        await db
          .update(orders)
          .set(updateData)
          .where(eq(orders.id, id))
          .returning();

        // Get the sysop record for the current user
        const currentSysop = await db.query.sysops.findFirst({
          where: eq(sysops.userId, c.get('user').id),
        });

        // Create order change record for QCH status
        await db.insert(orderChanges).values({
          orderId: id,
          sysopId: currentSysop?.id || null,
          changeType: 'status_update',
          previousValues: { qchStatus: currentOrder.qchStatus },
          newValues: { qchStatus: newQchStatus },
          comment,
        });

        // If order was automatically canceled, create additional change record
        if (newQchStatus === 'failed') {
          await db.insert(orderChanges).values({
            orderId: id,
            sysopId: currentSysop?.id || null,
            changeType: 'status_update',
            previousValues: { status: currentOrder.status },
            newValues: { status: 'canceled' },
            comment: 'Order automatically canceled due to failed quality check',
          });
        }

        // Return the updated order with relations
        return db.query.orders.findFirst({
          where: eq(orders.id, id),
          with: {
            customer: {
              with: {
                user: true,
              },
            },
            location: true,
            items: {
              with: {
                product: true,
                variant: true,
              },
            },
          },
        });
      });

      return buildJsonResponse({
        c,
        message: 'QCH status updated successfully',
        payload: result,
      });
    },
  )
  .get(
    '/:id/changes',
    requireAuth,
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator(
      'query',
      z.object({
        page: z.coerce.number().positive().default(1),
        pageSize: z.coerce.number().positive().max(100).default(20),
      }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');
      const { page, pageSize } = c.req.valid('query');

      const offset = (page - 1) * pageSize;

      const result = await tryCatching(async () => {
        // Check if order exists
        const order = await db.query.orders.findFirst({
          where: eq(orders.id, id),
        });

        if (!order) {
          throw new HttpError({
            statusCode: 404,
            message: `Order with ID ${id} not found`,
          });
        }

        // Get order changes with pagination
        const changes = await db.query.orderChanges.findMany({
          where: eq(orderChanges.orderId, id),
          with: {
            sysop: true,
          },
          orderBy: [asc(orderChanges.createdAt)],
          limit: pageSize,
          offset,
        });

        console.log('all changes', changes);

        // Get total count for pagination
        const totalResult = await db
          .select({ count: count() })
          .from(orderChanges)
          .where(eq(orderChanges.orderId, id));

        const total = totalResult[0]?.count || 0;

        return {
          changes,
          pagination: {
            page,
            pageSize,
            total,
            totalPages: Math.ceil(total / pageSize),
          },
        };
      });

      return buildJsonResponse({
        c,
        message: 'Order changes retrieved successfully',
        payload: result,
      });
    },
  )
  .post(
    '/:id/changes',
    requireAuth,
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator(
      'json',
      createOrderChangeSchema.omit({ orderId: true }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const currentUser = c.get('user');

      const { id } = c.req.valid('param');
      const changeData = c.req.valid('json');

      const result = await tryCatching(async () => {
        // Check if order exists
        const order = await db.query.orders.findFirst({
          where: eq(orders.id, id),
        });

        if (!order) {
          throw new HttpError({
            statusCode: 404,
            message: `Order with ID ${id} not found`,
          });
        }

        // Get the sysop record for the current user
        const currentSysop = await db.query.sysops.findFirst({
          where: eq(sysops.userId, currentUser.id),
        });

        // Create order change record
        const [orderChange] = await db
          .insert(orderChanges)
          .values({
            ...changeData,
            orderId: id,
            sysopId: currentSysop?.id || null,
          })
          .returning();

        if (!orderChange) {
          throw new HttpError({
            statusCode: 500,
            message: "Couldn't create order change",
          });
        }

        // Return the created change with sysop information
        return db.query.orderChanges.findFirst({
          where: eq(orderChanges.id, orderChange.id),
          with: {
            sysop: true,
          },
        });
      });

      return buildJsonResponse({
        c,
        message: 'Order change recorded successfully',
        payload: result,
      });
    },
  )
  .put(
    '/:id/cancel',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator(
      'json',
      z.object({
        reason: z.string().max(1000).optional(),
      }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');
      const { reason } = c.req.valid('json');

      const result = await tryCatching(async () => {
        // Get the current order
        const currentOrder = await db.query.orders.findFirst({
          where: eq(orders.id, id),
          with: {
            customer: {
              with: {
                user: true,
              },
            },
          },
        });

        if (!currentOrder) {
          throw new HttpError({
            statusCode: 404,
            message: `Order with ID ${id} not found`,
          });
        }

        // Check if order can be canceled
        if (
          currentOrder.status === 'completed' ||
          currentOrder.status === 'canceled'
        ) {
          throw new HttpError({
            statusCode: 400,
            message: 'Cannot cancel completed or already canceled orders',
          });
        }

        // Update the order status to canceled
        await db
          .update(orders)
          .set({
            status: 'canceled',
            updatedAt: new Date().toISOString(),
          })
          .where(eq(orders.id, id))
          .returning();

        // Create order change record - customer cancellations have null sysopId
        await db.insert(orderChanges).values({
          orderId: id,
          sysopId: null, // Customer action, no sysop involved
          changeType: 'status_update',
          previousValues: { status: currentOrder.status },
          newValues: { status: 'canceled' },
          comment: reason
            ? `Customer cancellation: ${reason}`
            : 'Order canceled by customer',
        });

        // Return the updated order with relations
        return db.query.orders.findFirst({
          where: eq(orders.id, id),
          with: {
            customer: {
              with: {
                user: true,
              },
            },
            location: true,
            items: {
              with: {
                product: true,
                variant: true,
              },
            },
          },
        });
      });

      return buildJsonResponse({
        c,
        message: 'Order canceled successfully',
        payload: result,
      });
    },
  );

export { route };
