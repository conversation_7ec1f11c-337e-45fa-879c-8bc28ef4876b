import { zValidator } from '@hono/zod-validator';
import { eq } from '@muraadso/db';
import { brandCategories } from '@muraadso/db/schema/brand-categories';
import { brands } from '@muraadso/db/schema/brands';
import { createBrandSchema, updateBrandSchema } from '@muraadso/models/brands';
import { Hono } from 'hono';
import { z } from 'zod/v4';
import type { Env } from '../../env';
import { HttpError } from '../../utils/errors/custom-errors';
import { handleZodError } from '../../utils/errors/error-handler';
import { tryCatching } from '../../utils/errors/try-catching';
import { buildJsonResponse } from '../../utils/response';
import { deleteObj, uploadObj } from '../../utils/storage';

const route = new Hono<Env>()
  .get('/', async (c) => {
    const db = c.get('db');
    const result = await tryCatching(async () =>
      db.query.brands.findMany({
        with: {
          brandCategories: {
            with: {
              category: true,
            },
          },
        },
        orderBy: (brands, { asc }) => [asc(brands.rank)],
      }),
    );

    return buildJsonResponse({
      c,
      message: 'Brands retrieved successfully',
      payload: result.map(({ brandCategories, ...rest }) => ({
        ...rest,
        categories: brandCategories.map((bc) => bc.category),
      })),
    });
  })
  .get(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');

      const result = await tryCatching(async () => {
        const brand = await db.query.brands.findFirst({
          where: (brands, { eq }) => eq(brands.id, id),
          with: {
            brandCategories: {
              with: {
                category: true,
              },
            },
          },
        });

        if (!brand) {
          throw new HttpError({
            statusCode: 404,
            message: `Brand with ID ${id} not found`,
          });
        }

        // Transform the data to include categories directly
        const { brandCategories, ...rest } = brand;
        return {
          ...rest,
          categories: brandCategories.map((bc) => bc.category),
        };
      });

      return buildJsonResponse({
        c,
        message: 'Brand retrieved successfully',
        payload: result,
      });
    },
  )
  .post(
    '/',
    zValidator('json', createBrandSchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const { name, slug, description, rank, categoryIds } =
        c.req.valid('json');
      const result = await tryCatching(async () => {
        return await db.transaction(async (tx) => {
          // Create the brand
          const [brand] = await tx
            .insert(brands)
            .values({
              name,
              slug,
              description,
              rank,
            })
            .returning();

          if (!brand || !brand.id) {
            throw new HttpError({
              statusCode: 500,
              message: "Couldn't create brand",
            });
          }

          // Create brand-category relationships if categoryIds provided
          if (categoryIds && categoryIds.length > 0) {
            await tx.insert(brandCategories).values(
              categoryIds.map((categoryId) => ({
                brandId: brand.id,
                categoryId,
              })),
            );
          }

          return brand;
        });
      });

      return buildJsonResponse({
        c,
        message: 'Brand created successfully',
        payload: result,
      });
    },
  )
  .put(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator('json', updateBrandSchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');
      const { name, slug, description, rank, categoryIds } =
        c.req.valid('json');
      const result = await tryCatching(async () => {
        const existingBrand = await db.query.brands.findFirst({
          where: eq(brands.id, id),
        });

        if (!existingBrand) {
          throw new HttpError({
            statusCode: 404,
            message: `Brand with ID ${id} not found`,
          });
        }

        return await db.transaction(async (tx) => {
          // Update the brand
          const [brand] = await tx
            .update(brands)
            .set({
              ...{ name, slug, description, rank },
              updatedAt: new Date().toISOString(),
            })
            .where(eq(brands.id, id))
            .returning();

          // Update brand-category relationships if categoryIds provided
          if (categoryIds !== undefined) {
            // Delete existing relationships
            await tx
              .delete(brandCategories)
              .where(eq(brandCategories.brandId, id));

            // Insert new relationships
            if (categoryIds.length > 0) {
              await tx.insert(brandCategories).values(
                categoryIds.map((categoryId) => ({
                  brandId: id,
                  categoryId,
                })),
              );
            }
          }

          return brand;
        });
      });

      return buildJsonResponse({
        c,
        message: 'Brand updated successfully',
        payload: result,
      });
    },
  )
  .post(
    '/:id/image',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator(
      'form',
      z.object({
        image: z.instanceof(File, { message: 'Image is required.' }),
      }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');
      const { image } = c.req.valid('form');

      const result = await tryCatching(async () => {
        const brand = await db.query.brands.findFirst({
          where: eq(brands.id, id),
        });

        if (!brand) {
          throw new HttpError({
            statusCode: 404,
            message: `Brand with ID ${id} not found`,
          });
        }

        if (brand.image) {
          await deleteObj(c, brand.image);
        }

        const imageKey = await uploadObj(c, image);

        const [updatedBrand] = await db
          .update(brands)
          .set({
            image: imageKey,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(brands.id, id))
          .returning();

        return updatedBrand;
      });

      return buildJsonResponse({
        c,
        message: 'Brand image updated successfully',
        payload: result,
      });
    },
  )
  .delete(
    '/:id/image',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');

      const result = await tryCatching(async () => {
        const brand = await db.query.brands.findFirst({
          where: eq(brands.id, id),
        });

        if (!brand) {
          throw new HttpError({
            statusCode: 404,
            message: `Brand with ID ${id} not found`,
          });
        }

        if (brand.image) {
          await deleteObj(c, brand.image);
        }

        const [updatedBrand] = await db
          .update(brands)
          .set({
            image: null,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(brands.id, id))
          .returning();

        return updatedBrand;
      });

      return buildJsonResponse({
        c,
        payload: result,
        message: 'Brand image deleted successfully',
      });
    },
  )
  .delete(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');

      await tryCatching(async () => {
        const brand = await db.query.brands.findFirst({
          where: eq(brands.id, id),
        });

        if (!brand) {
          throw new HttpError({
            statusCode: 404,
            message: `Brand with ID ${id} not found`,
          });
        }

        if (brand.image) {
          await deleteObj(c, brand.image);
        }

        await db.delete(brands).where(eq(brands.id, id));
      });

      return buildJsonResponse({
        c,
        message: 'Brand deleted successfully',
      });
    },
  );

export { route };
