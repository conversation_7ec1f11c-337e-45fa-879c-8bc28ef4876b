import { type Brand, selectBrandSchema } from '@muraadso/models/brands';
import {
  type Category,
  selectCategorySchema,
} from '@muraadso/models/categories';
import {
  type CreateProduct,
  type Product,
  type UpdateProduct,
  selectProductSchema,
} from '@muraadso/models/products';
import { beforeAll, describe, expect, it } from 'vitest';
import app from '../../server';
import { apiRequest } from '../../utils/request';
import type { ApiResponse } from '../../utils/response';

let productId: string;

describe('POST /v1/products', () => {
  const product = {
    name: 'Test Product',
    slug: 'test-product',
    description: 'Test Product Description',
    brandId: '',
    categoryId: '',
    isPublished: true,
    variantsEnabled: false,
    options: [
      {
        name: 'Default option',
        values: [
          {
            value: 'Default option value',
          },
        ],
      },
    ],
    variants: [
      {
        name: 'Default variant',
        sku: 'test-product-default',
        rank: 0,
        isDefault: true,
        shouldCreate: true,
        options: {
          'Default option': 'Default option value',
        },
        prices: {
          retailPrice: 100,
          buybackPrice: 50,
          consignmentPrice: 75,
          tradeInPrice: 60,
        },
      },
    ],
  } satisfies CreateProduct;

  beforeAll(async () => {
    // Get a category ID
    const categoriesRes = await apiRequest(app).get('/v1/categories');
    const categoriesBody = (await categoriesRes.json()) as ApiResponse<
      Category[]
    >;
    const { data: categories } = selectCategorySchema
      .array()
      .safeParse(categoriesBody.payload);
    const firstCategory = categories?.at(0);
    if (firstCategory?.id) {
      product.categoryId = firstCategory.id;
    }

    // Get a brand ID
    const brandsRes = await apiRequest(app).get('/v1/brands');
    const brandsBody = (await brandsRes.json()) as ApiResponse<Brand[]>;
    const { data: brands } = selectBrandSchema
      .array()
      .safeParse(brandsBody.payload);
    const firstBrand = brands?.at(0);
    if (firstBrand?.id) {
      product.brandId = firstBrand.id;
    }
  });

  it('should create a new product', async () => {
    const res = await apiRequest(app).post('/v1/products', {
      body: product,
    });
    const body = (await res.json()) as ApiResponse<Product>;
    const { data, success } = selectProductSchema.safeParse(body.payload);
    expect(res.ok).toBe(true);
    expect(success).toBe(true);
    expect(data?.name).toBe(product.name);
    expect(data?.slug).toBe(product.slug);

    if (data?.id) {
      productId = data.id;
    }
  });
});

describe('GET /v1/products', () => {
  it('should return a list of products', async () => {
    const res = await apiRequest(app).get('/v1/products');
    const body = (await res.json()) as ApiResponse<{
      products: Product[];
      pagination: {
        page: number;
        pageSize: number;
        totalCount: number;
        totalPages: number;
        hasNextPage: boolean;
        hasPreviousPage: boolean;
      };
    }>;

    expect(res.ok).toBe(true);
    expect(body.payload).toBeDefined();
    expect(body.payload?.products).toBeDefined();
    expect(Array.isArray(body.payload?.products)).toBe(true);
    expect(body.payload?.products.length).toBeGreaterThan(0);
    expect(body.payload?.pagination).toBeDefined();
  });

  it('should return a product by id', async () => {
    const res = await apiRequest(app).get('/v1/products/' + productId);
    const body = (await res.json()) as ApiResponse<Product>;
    const { data, success } = selectProductSchema.safeParse(body.payload);
    expect(res.ok).toBe(true);
    expect(success).toBe(true);
    expect(data?.name).toBe('Test Product');
    expect(data?.slug).toBe('test-product');
  });
});

describe('PUT /v1/products', () => {
  const updateData = {
    name: 'Updated Product',
    description: 'Updated Product Description',
  } satisfies UpdateProduct;

  it('should update an existing product', async () => {
    const res = await apiRequest(app).put('/v1/products/' + productId, {
      body: updateData,
    });
    const body = (await res.json()) as ApiResponse<Product>;
    const { data, success } = selectProductSchema.safeParse(body.payload);
    expect(res.ok).toBe(true);
    expect(success).toBe(true);
    expect(data?.name).toBe(updateData.name);
    expect(data?.description).toBe(updateData.description);
  });
});

describe('DELETE /v1/products', () => {
  it('should delete an existing product', async () => {
    const res = await apiRequest(app).delete('/v1/products/' + productId);
    expect(res.ok).toBe(true);
  });
});
