import { type Brand, selectBrandSchema } from '@muraadso/models/brands';
import {
  type Category,
  selectCategorySchema,
} from '@muraadso/models/categories';
import { beforeAll, describe, expect, it } from 'vitest';
import app from '../../server';
import { apiRequest } from '../../utils/request';
import type { ApiResponse } from '../../utils/response';

let brandId: string;
let categoryIds: string[] = [];

describe('Brands with Categories API', () => {
  beforeAll(async () => {
    // Get some category IDs for testing
    const categoriesRes = await apiRequest(app).get('/v1/categories');
    const categoriesBody = (await categoriesRes.json()) as ApiResponse<
      Category[]
    >;
    const { data: categories } = selectCategorySchema
      .array()
      .safeParse(categoriesBody.payload);
    
    if (categories && categories.length >= 2) {
      categoryIds = categories.slice(0, 2).map(cat => cat.id);
    }
  });

  describe('POST /v1/brands with categories', () => {
    it('should create a brand with categories', async () => {
      const brandData = {
        name: 'Test Brand with Categories',
        slug: 'test-brand-with-categories',
        description: 'A test brand with multiple categories',
        rank: 999,
        categoryIds: categoryIds,
      };

      const res = await apiRequest(app).post('/v1/brands', {
        body: brandData,
      });
      
      const body = (await res.json()) as ApiResponse<Brand>;
      const { data, success } = selectBrandSchema.safeParse(body.payload);
      
      expect(res.ok).toBe(true);
      expect(success).toBe(true);
      expect(data?.name).toBe(brandData.name);
      expect(data?.slug).toBe(brandData.slug);

      if (data?.id) {
        brandId = data.id;
      }
    });
  });

  describe('GET /v1/brands/:id with categories', () => {
    it('should return a brand with its categories', async () => {
      const res = await apiRequest(app).get('/v1/brands/' + brandId);
      const body = (await res.json()) as ApiResponse<Brand>;
      
      expect(res.ok).toBe(true);
      expect(body.payload).toBeDefined();
      expect(body.payload?.categories).toBeDefined();
      expect(Array.isArray(body.payload?.categories)).toBe(true);
      expect(body.payload?.categories?.length).toBe(categoryIds.length);
    });
  });

  describe('PUT /v1/brands/:id with categories', () => {
    it('should update a brand and its categories', async () => {
      // Update with only one category
      const updateData = {
        name: 'Updated Test Brand',
        description: 'Updated description',
        categoryIds: categoryIds.slice(0, 1), // Only first category
      };

      const res = await apiRequest(app).put('/v1/brands/' + brandId, {
        body: updateData,
      });
      
      const body = (await res.json()) as ApiResponse<Brand>;
      const { data, success } = selectBrandSchema.safeParse(body.payload);
      
      expect(res.ok).toBe(true);
      expect(success).toBe(true);
      expect(data?.name).toBe(updateData.name);
      expect(data?.description).toBe(updateData.description);

      // Verify categories were updated by fetching the brand
      const getRes = await apiRequest(app).get('/v1/brands/' + brandId);
      const getBody = (await getRes.json()) as ApiResponse<Brand>;
      
      expect(getBody.payload?.categories?.length).toBe(1);
      expect(getBody.payload?.categories?.[0]?.id).toBe(categoryIds[0]);
    });
  });

  describe('DELETE /v1/brands/:id', () => {
    it('should delete the test brand', async () => {
      const res = await apiRequest(app).delete('/v1/brands/' + brandId);
      expect(res.ok).toBe(true);
    });
  });
});
