import { zValidator } from '@hono/zod-validator';
import { count, eq, sql } from '@muraadso/db';
import { categories } from '@muraadso/db/schema/categories';
import { prices } from '@muraadso/db/schema/prices';
import { products } from '@muraadso/db/schema/products';
import { variants } from '@muraadso/db/schema/variants';
import {
  createCategorySchema,
  updateCategorySchema,
} from '@muraadso/models/categories';
import { Hono } from 'hono';
import { z } from 'zod/v4';
import type { Env } from '../../env';
import { HttpError } from '../../utils/errors/custom-errors';
import { handleZodError } from '../../utils/errors/error-handler';
import { tryCatching } from '../../utils/errors/try-catching';
import { buildJsonResponse } from '../../utils/response';
import { deleteObj, uploadObj } from '../../utils/storage';

const route = new Hono<Env>()
  .get('/', async (c) => {
    const db = c.get('db');
    const result = await tryCatching(async () =>
      db.query.categories.findMany({
        orderBy: (categories, { asc }) => [asc(categories.rank)],
      }),
    );

    return buildJsonResponse({
      c,
      message: 'Categories retrieved successfully',
      payload: result,
    });
  })
  .get(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');

      const result = await tryCatching(async () => {
        const category = await db.query.categories.findFirst({
          where: (categories, { eq }) => eq(categories.id, id),
          orderBy: (categories, { asc }) => [asc(categories.rank)],
        });

        if (!category) {
          throw new HttpError({
            statusCode: 404,
            message: `Category with ID ${id} not found`,
          });
        }

        return category;
      });

      return buildJsonResponse({
        c,
        message: 'Category retrieved successfully',
        payload: result,
      });
    },
  )
  .get(
    '/:id/subcategories',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');

      const result = await tryCatching(async () => {
        const parent = await db.query.categories.findFirst({
          where: (categories, { eq }) => eq(categories.id, id),
        });

        if (!parent) {
          throw new HttpError({
            statusCode: 404,
            message: `Parent category with ID ${id} not found`,
          });
        }

        // Then get all subcategories
        return await db.query.categories.findMany({
          where: (categories, { eq }) => eq(categories.parentId, id),
          orderBy: (categories, { asc }) => [asc(categories.rank)],
        });
      });

      return buildJsonResponse({
        c,
        message: 'Subcategories retrieved successfully',
        payload: result,
      });
    },
  )
  .post(
    '/',
    zValidator('json', createCategorySchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const { parentId, name, slug, description, rank } = c.req.valid('json');
      const result = await tryCatching(async () => {
        if (parentId) {
          const parentCategory = await db.query.categories.findFirst({
            where: (categories, { eq }) => eq(categories.id, parentId),
          });

          if (!parentCategory) {
            throw new HttpError({
              statusCode: 404,
              message: `Parent category with ID ${parentId} not found`,
            });
          }
        }

        const [category] = await db
          .insert(categories)
          .values({
            parentId,
            name,
            slug,
            description,
            rank,
          })
          .returning();

        return category;
      });

      return buildJsonResponse({
        c,
        message: 'Category created successfully',
        payload: result,
      });
    },
  )
  .put(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator('json', updateCategorySchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');
      const { parentId, name, slug, description, rank } = c.req.valid('json');
      const result = await tryCatching(async () => {
        const existingCategory = await db.query.categories.findFirst({
          where: eq(categories.id, id),
        });

        if (!existingCategory) {
          throw new HttpError({
            statusCode: 404,
            message: `Category with ID ${id} not found`,
          });
        }

        if (parentId) {
          const parentCategory = await db.query.categories.findFirst({
            where: eq(categories.id, parentId),
          });

          if (!parentCategory) {
            throw new HttpError({
              statusCode: 404,
              message: `Parent category with ID ${parentId} not found`,
            });
          }

          if (parentId === id) {
            throw new HttpError({
              statusCode: 400,
              message: 'A category cannot be its own parent',
            });
          }
        }

        const [category] = await db
          .update(categories)
          .set({
            ...{ parentId, name, slug, description, rank },
            updatedAt: new Date().toISOString(),
          })
          .where(eq(categories.id, id))
          .returning();

        return category;
      });

      return buildJsonResponse({
        c,
        message: 'Category updated successfully',
        payload: result,
      });
    },
  )
  .post(
    '/:id/image',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator(
      'form',
      z.object({
        image: z.instanceof(File, { message: 'Image is required.' }),
      }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');
      const { image } = c.req.valid('form');

      const result = await tryCatching(async () => {
        const category = await db.query.categories.findFirst({
          where: eq(categories.id, id),
        });

        if (!category) {
          throw new HttpError({
            statusCode: 404,
            message: `Category with ID ${id} not found`,
          });
        }

        if (category.image) {
          await deleteObj(c, category.image);
        }

        const imageKey = await uploadObj(c, image);

        const [updatedCategory] = await db
          .update(categories)
          .set({
            image: imageKey,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(categories.id, id))
          .returning();

        return updatedCategory;
      });

      return buildJsonResponse({
        c,
        message: 'Category image updated successfully',
        payload: result,
      });
    },
  )
  .delete(
    '/:id/image',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');

      const result = await tryCatching(async () => {
        const category = await db.query.categories.findFirst({
          where: eq(categories.id, id),
        });

        if (!category) {
          throw new HttpError({
            statusCode: 404,
            message: `Category with ID ${id} not found`,
          });
        }

        if (category.image) {
          await deleteObj(c, category.image);
        }

        const [updatedCategory] = await db
          .update(categories)
          .set({
            image: null,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(categories.id, id))
          .returning();

        return updatedCategory;
      });

      return buildJsonResponse({
        c,
        payload: result,
        message: 'Category image deleted successfully',
      });
    },
  )
  .delete(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');

      await tryCatching(async () => {
        const subcategories = await db.query.categories.findMany({
          where: eq(categories.parentId, id),
        });

        if (subcategories.length > 0) {
          throw new HttpError({
            statusCode: 400,
            message: 'Cannot delete a category that has subcategories',
          });
        }

        const category = await db.query.categories.findFirst({
          where: eq(categories.id, id),
        });

        if (!category) {
          throw new HttpError({
            statusCode: 404,
            message: `Category with ID ${id} not found`,
          });
        }

        if (category.image) {
          await deleteObj(c, category.image);
        }

        await db.delete(categories).where(eq(categories.id, id));
      });

      return buildJsonResponse({
        c,
        message: 'Category deleted successfully',
      });
    },
  );

export { route };
