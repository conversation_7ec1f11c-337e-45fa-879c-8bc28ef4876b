import { zValidator } from '@hono/zod-validator';
import { and, count, eq, gte, ilike } from '@muraadso/db';
import { images, images as imagesTable } from '@muraadso/db/schema/images';
import { optionValues } from '@muraadso/db/schema/option-values';
import { options } from '@muraadso/db/schema/options';
import { prices } from '@muraadso/db/schema/prices';
import { products } from '@muraadso/db/schema/products';
import { variantImages } from '@muraadso/db/schema/variant-images';
import { variantOptionValues } from '@muraadso/db/schema/variant-option-values';
import { variants } from '@muraadso/db/schema/variants';
import {
  createOptionSchema,
  updateOptionSchema,
} from '@muraadso/models/options';
import {
  createProductSchema,
  updateProductSchema,
} from '@muraadso/models/products';
import {
  createVariantSchema,
  updateVariantSchema,
} from '@muraadso/models/variants';
import { Hono } from 'hono';
import { z } from 'zod/v4';
import type { Env } from '../../env';
import { HttpError } from '../../utils/errors/custom-errors';
import { handleZodError } from '../../utils/errors/error-handler';
import { tryCatching } from '../../utils/errors/try-catching';
import { buildJsonResponse } from '../../utils/response';
import { deleteObj, uploadObj } from '../../utils/storage';

const route = new Hono<Env>()
  .get('/stats', async (c) => {
    const db = c.get('db');

    const result = await tryCatching(async () => {
      const now = new Date();
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      // Total products count
      const [totalProductsResult] = await db
        .select({ count: count() })
        .from(products);
      const totalProducts = totalProductsResult?.count;

      // Products created this week
      const [productsThisWeekResult] = await db
        .select({ count: count() })
        .from(products)
        .where(gte(products.createdAt, oneWeekAgo.toISOString()));
      const productsThisWeek = productsThisWeekResult?.count;

      // Published products count
      const [publishedResult] = await db
        .select({ count: count() })
        .from(products)
        .where(eq(products.isPublished, true));
      const publishedProducts = publishedResult?.count;

      // Published products created this week
      const [publishedThisWeekResult] = await db
        .select({ count: count() })
        .from(products)
        .where(
          and(
            eq(products.isPublished, true),
            gte(products.createdAt, oneWeekAgo.toISOString()),
          ),
        );
      const publishedThisWeek = publishedThisWeekResult?.count;

      // Draft products count
      const [draftResult] = await db
        .select({ count: count() })
        .from(products)
        .where(eq(products.isPublished, false));
      const draftProducts = draftResult?.count;

      // Draft products created this week
      const [draftThisWeekResult] = await db
        .select({ count: count() })
        .from(products)
        .where(
          and(
            eq(products.isPublished, false),
            gte(products.createdAt, oneWeekAgo.toISOString()),
          ),
        );
      const draftThisWeek = draftThisWeekResult?.count;

      const trashedProducts = 0;

      return {
        totalProducts,
        productsThisWeek,
        publishedProducts,
        publishedThisWeek,
        draftProducts,
        draftThisWeek,
        trashedProducts,
      };
    });

    return buildJsonResponse({
      c,
      message: 'Product stats retrieved successfully',
      payload: result,
    });
  })
  .get(
    '/',
    zValidator(
      'query',
      z.object({
        q: z.string().optional(),
        page: z.coerce.number().positive().default(1),
        pageSize: z.coerce.number().positive().default(10),
        categoryId: z.uuid().optional(),
        brandId: z.uuid().optional(),
        minPrice: z.coerce.number().optional(),
        maxPrice: z.coerce.number().optional(),
        isPublished: z
          .enum(['true', 'false'])
          .optional()
          .transform((val) => val === 'true'),
        sortOrder: z.enum(['asc', 'desc']).default('desc'),
      }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const { q, page, pageSize, categoryId, brandId, isPublished, sortOrder } =
        c.req.valid('query');

      const offset = (page - 1) * pageSize;

      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      const whereConditions: any[] = [];

      if (q) {
        whereConditions.push(
          ilike(products.name, `%${q.toLocaleLowerCase()}%`),
        );
      }

      if (categoryId) {
        whereConditions.push(eq(products.categoryId, categoryId));
      }

      if (brandId) {
        whereConditions.push(eq(products.brandId, brandId));
      }

      // if (isPublished !== undefined) {
      //   whereConditions.push(eq(products.isPublished, isPublished));
      // }

      const totalCount = await tryCatching(async () => {
        const result = await db
          .select({ count: count() })
          .from(products)
          .where(
            whereConditions.length > 0 ? and(...whereConditions) : undefined,
          );
        return Number(result[0]?.count || 0);
      });

      // Get products with pagination and filters
      const result = await tryCatching(async () =>
        db.query.products.findMany({
          with: {
            brand: true,
            category: true,
            images: true,
            options: {
              with: {
                optionValues: true,
              },
            },
            variants: {
              with: {
                prices: true,
                variantImages: {
                  with: {
                    image: true,
                  },
                },
                inventory: true,
                variantOptionValue: {
                  with: {
                    optionValue: true,
                  },
                },
              },
            },
          },
          where:
            whereConditions.length > 0 ? and(...whereConditions) : undefined,
          orderBy: (products, { asc, desc }) => {
            return sortOrder === 'asc'
              ? [asc(products.updatedAt)]
              : [desc(products.updatedAt)];
          },
          limit: pageSize,
          offset,
        }),
      );

      // // Filter by price range if specified (needs to be done after query since prices are in variants)
      // let filteredResult = result;
      // if (minPrice !== undefined || maxPrice !== undefined) {
      //   filteredResult = result.filter((product) => {
      //     // Check if any variant's price falls within the specified range
      //     return product.variants.some((variant) => {
      //       if (!variant.prices) return false;
      //
      //       const price = variant.prices.retailPrice;
      //       if (minPrice !== undefined && maxPrice !== undefined) {
      //         return price >= minPrice && price <= maxPrice;
      //       }
      //       if (minPrice !== undefined) {
      //         return price >= minPrice;
      //       } else if (maxPrice !== undefined) {
      //         return price <= maxPrice;
      //       }
      //       return true;
      //     });
      //   });
      // }

      return buildJsonResponse({
        c,
        message: 'Products retrieved successfully',
        payload: {
          products: result.map(({ options, variants, ...rest }) => ({
            ...rest,
            options: options.map(({ optionValues, ...rest }) => ({
              ...rest,
              values: optionValues,
            })),
            variants: variants.map(
              ({ variantImages, variantOptionValue, ...rest }) => ({
                ...rest,
                images: variantImages.map(({ image, ...rest }) => ({
                  ...rest,
                  ...image,
                })),
                optionValues: variantOptionValue.map(
                  ({ optionValue, ...rest }) => ({
                    ...rest,
                    ...optionValue,
                  }),
                ),
              }),
            ),
          })),
          pagination: {
            page,
            pageSize,
            totalCount,
            totalPages: Math.ceil(totalCount / pageSize),
            hasNextPage: page < Math.ceil(totalCount / pageSize),
            hasPreviousPage: page > 1,
          },
        },
      });
    },
  )
  .get(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');

      const result = await tryCatching(async () => {
        const product = await db.query.products.findFirst({
          with: {
            brand: true,
            category: true,
            images: true,
            options: {
              with: {
                optionValues: true,
              },
            },
            variants: {
              with: {
                prices: true,
                variantImages: {
                  with: {
                    image: true,
                  },
                },
                variantOptionValue: {
                  with: {
                    optionValue: true,
                  },
                },
              },
            },
          },
          where: (products, { eq }) => eq(products.id, id),
        });

        if (!product) {
          throw new HttpError({
            statusCode: 404,
            message: `Product with ID ${id} not found`,
          });
        }

        return product;
      });

      return buildJsonResponse({
        c,
        message: 'Product retrieved successfully',
        payload: {
          ...result,
          options: result.options.map(({ optionValues, ...rest }) => ({
            ...rest,
            values: optionValues,
          })),
          variants: result.variants.map(
            ({ variantImages, variantOptionValue, ...rest }) => ({
              ...rest,
              images: variantImages.map(({ image, ...rest }) => ({
                ...rest,
                ...image,
              })),
              optionValues: variantOptionValue.map(
                ({ optionValue, ...rest }) => ({
                  ...rest,
                  ...optionValue,
                }),
              ),
            }),
          ),
        },
      });
    },
  )
  .post(
    '/',
    zValidator('json', createProductSchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const {
        name,
        slug,
        description,
        brandId,
        categoryId,
        options: _options,
        variants: _variants,
        isPublished,
      } = c.req.valid('json');
      const result = await tryCatching(async () => {
        return db.transaction(async (tx) => {
          const [product] = await tx
            .insert(products)
            .values({
              name,
              slug,
              description,
              brandId,
              categoryId,
              isPublished,
            })
            .returning();

          // Create options and option values
          const optionValueMap = new Map();
          for (const _option of _options) {
            const [option] = await tx
              .insert(options)
              .values({ name: _option.name, productId: product!.id })
              .returning({ id: options.id });

            const createdOptionValues = await tx
              .insert(optionValues)
              .values(
                _option.values.map((it) => ({
                  value: it.value,
                  optionId: option!.id,
                })),
              )
              .returning();

            // Store option values for later use with variants
            for (const optionValue of createdOptionValues) {
              optionValueMap.set(
                `${_option.name}:${optionValue.value}`,
                optionValue.id,
              );
            }
          }

          if (_variants && _variants.length > 0) {
            for (const _variant of _variants) {
              if (_variant.shouldCreate) {
                const [variant] = await tx
                  .insert(variants)
                  .values({
                    name: _variant.name,
                    sku: _variant.sku || null,
                    productId: product!.id,
                    rank: _variant.rank,
                  })
                  .returning();

                // Create variant option values
                if (_variant.options) {
                  const variantOptionValueInserts = [];
                  for (const [optionName, optionValue] of Object.entries(
                    _variant.options,
                  )) {
                    const optionValueId = optionValueMap.get(
                      `${optionName}:${optionValue}`,
                    );
                    if (optionValueId) {
                      variantOptionValueInserts.push({
                        variantId: variant!.id,
                        optionValueId,
                      });
                    }
                  }

                  if (variantOptionValueInserts.length > 0) {
                    await tx
                      .insert(variantOptionValues)
                      .values(variantOptionValueInserts);
                  }
                }

                // Create prices
                if (_variant.prices) {
                  await tx.insert(prices).values({
                    variantId: variant!.id,
                    tradeInPrice: _variant.prices.tradeInPrice,
                    consignmentPrice: _variant.prices.consignmentPrice,
                    buybackPrice: _variant.prices.buybackPrice,
                    retailPrice: _variant.prices.retailPrice,
                  });
                }

                // Note: Variant images will be created separately after product images are uploaded
              }
            }
          }

          return product;
        });
      });

      return buildJsonResponse({
        c,
        message: 'Product created successfully',
        payload: result,
      });
    },
  )
  .post(
    '/with-images',
    zValidator(
      'form',
      z.object({
        productData: z.string().transform((str) => {
          try {
            return createProductSchema.parse(JSON.parse(str));
          } catch {
            throw new Error('Invalid product data');
          }
        }),
        images: z
          .union([
            z.instanceof(File, { message: 'Image is required' }),
            z.array(z.instanceof(File, { message: 'Image is required' })),
          ])
          .transform((value) => (Array.isArray(value) ? value : [value]))
          .optional(),
      }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const { productData, images: imageFiles } = c.req.valid('form');
      const {
        name,
        slug,
        description,
        brandId,
        categoryId,
        options: _options,
        variants: _variants,
        isPublished,
      } = productData;

      const result = await tryCatching(async () => {
        return db.transaction(async (tx) => {
          // Create product
          const [product] = await tx
            .insert(products)
            .values({
              name,
              slug,
              description,
              brandId,
              categoryId,
              isPublished,
            })
            .returning();

          // Upload images and create image records
          const imageIds: string[] = [];
          if (imageFiles && imageFiles.length > 0) {
            for (const imageFile of imageFiles) {
              const imageKey = await uploadObj(c, imageFile);
              const [createdImage] = await tx
                .insert(images)
                .values({
                  path: imageKey,
                  productId: product!.id,
                })
                .returning();
              imageIds.push(createdImage!.id);
            }
          }

          // Create options and option values
          const optionValueMap = new Map();
          for (const _option of _options) {
            const [option] = await tx
              .insert(options)
              .values({ name: _option.name, productId: product!.id })
              .returning({ id: options.id });

            const createdOptionValues = await tx
              .insert(optionValues)
              .values(
                _option.values.map((it) => ({
                  value: it.value,
                  optionId: option!.id,
                })),
              )
              .returning();

            for (const optionValue of createdOptionValues) {
              optionValueMap.set(
                `${_option.name}:${optionValue.value}`,
                optionValue.id,
              );
            }
          }

          // Create variants with images
          if (_variants && _variants.length > 0) {
            for (const _variant of _variants) {
              if (_variant.shouldCreate) {
                const [variant] = await tx
                  .insert(variants)
                  .values({
                    name: _variant.name,
                    sku: _variant.sku || null,
                    productId: product!.id,
                    rank: _variant.rank,
                  })
                  .returning();

                // Create variant option values
                if (_variant.options) {
                  const variantOptionValueInserts = [];
                  for (const [optionName, optionValue] of Object.entries(
                    _variant.options,
                  )) {
                    const optionValueId = optionValueMap.get(
                      `${optionName}:${optionValue}`,
                    );
                    if (optionValueId) {
                      variantOptionValueInserts.push({
                        variantId: variant!.id,
                        optionValueId,
                      });
                    }
                  }

                  if (variantOptionValueInserts.length > 0) {
                    await tx
                      .insert(variantOptionValues)
                      .values(variantOptionValueInserts);
                  }
                }

                // Create prices
                if (_variant.prices) {
                  await tx.insert(prices).values({
                    variantId: variant!.id,
                    tradeInPrice: _variant.prices.tradeInPrice,
                    consignmentPrice: _variant.prices.consignmentPrice,
                    buybackPrice: _variant.prices.buybackPrice,
                    retailPrice: _variant.prices.retailPrice,
                  });
                }

                // Create variant images using indices
                if (_variant.imageIndices && _variant.imageIndices.length > 0) {
                  const variantImageInserts = _variant.imageIndices
                    .map((index) => {
                      const imageId = imageIds[index];
                      return imageId
                        ? { variantId: variant!.id, imageId }
                        : null;
                    })
                    .filter(
                      (insert): insert is NonNullable<typeof insert> =>
                        insert !== null,
                    );

                  if (variantImageInserts.length > 0) {
                    await tx.insert(variantImages).values(variantImageInserts);
                  }
                }
              }
            }
          }

          return product;
        });
      });

      return buildJsonResponse({
        c,
        message: 'Product created successfully',
        payload: result,
      });
    },
  )
  .post(
    '/:id/options',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator('json', createOptionSchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');
      const { values, ...optionData } = c.req.valid('json');

      const result = await tryCatching(async () => {
        // Check if product exists
        const product = await db.query.products.findFirst({
          where: eq(products.id, id),
        });

        if (!product) {
          throw new HttpError({
            statusCode: 404,
            message: `Product with ID ${id} not found`,
          });
        }

        return db.transaction(async (tx) => {
          // Create option
          const [option] = await tx
            .insert(options)
            .values({
              ...optionData,
              productId: id,
            })
            .returning();

          // Create option values
          const createdOptionValues = await tx
            .insert(optionValues)
            .values(
              values.map((it) => ({
                value: it.value,
                optionId: option!.id,
              })),
            )
            .returning();

          return {
            ...option,
            values: createdOptionValues,
          };
        });
      });

      return buildJsonResponse({
        c,
        message: 'Product option with values created successfully',
        payload: result,
      });
    },
  )
  .delete(
    '/:id/options/:optionId',
    zValidator(
      'param',
      z.object({ id: z.uuid(), optionId: z.uuid() }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const { id, optionId } = c.req.valid('param');

      await tryCatching(async () => {
        const product = await db.query.products.findFirst({
          where: eq(products.id, id),
        });

        if (!product) {
          throw new HttpError({
            statusCode: 404,
            message: `Product with ID ${id} not found`,
          });
        }

        await db.delete(options).where(eq(options.id, optionId));
      });

      return buildJsonResponse({
        c,
        message: 'Product option deleted successfully',
      });
    },
  )
  .put(
    '/:id/options/:optionId',
    zValidator(
      'param',
      z.object({ id: z.uuid(), optionId: z.uuid() }),
      handleZodError,
    ),
    zValidator('json', updateOptionSchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id, optionId } = c.req.valid('param');
      const { values, ...optionData } = c.req.valid('json');

      const result = await tryCatching(async () => {
        // Check if product exists
        const product = await db.query.products.findFirst({
          where: eq(products.id, id),
        });

        if (!product) {
          throw new HttpError({
            statusCode: 404,
            message: `Product with ID ${id} not found`,
          });
        }

        // Check if option exists
        const existingOption = await db.query.options.findFirst({
          where: and(eq(options.id, optionId), eq(options.productId, id)),
        });

        if (!existingOption) {
          throw new HttpError({
            statusCode: 404,
            message: `Option with ID ${optionId} not found`,
          });
        }

        return db.transaction(async (tx) => {
          // Update option
          const [option] = await tx
            .update(options)
            .set({
              ...optionData,
              updatedAt: new Date().toISOString(),
            })
            .where(eq(options.id, optionId))
            .returning();

          // Delete existing option values
          await tx
            .delete(optionValues)
            .where(eq(optionValues.optionId, optionId));

          // Create new option values
          const createdOptionValues = await tx
            .insert(optionValues)
            .values(
              values.map((it) => ({
                value: it.value,
                optionId: option!.id,
              })),
            )
            .returning();

          return {
            ...option,
            values: createdOptionValues,
          };
        });
      });

      return buildJsonResponse({
        c,
        message: 'Product option updated successfully',
        payload: result,
      });
    },
  )
  .post(
    '/:id/variants',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator('json', createVariantSchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');
      const {
        options: variantOptions,
        prices: variantPrices,
        imageIndices,
        ...variantData
      } = c.req.valid('json');

      const result = await tryCatching(async () => {
        // Check if product exists
        const product = await db.query.products.findFirst({
          where: eq(products.id, id),
          with: {
            options: {
              with: {
                optionValues: true,
              },
            },
          },
        });

        if (!product) {
          throw new HttpError({
            statusCode: 404,
            message: `Product with ID ${id} not found`,
          });
        }

        return db.transaction(async (tx) => {
          // Create variant
          const [variant] = await tx
            .insert(variants)
            .values({
              ...variantData,
              productId: id,
            })
            .returning();

          // Create variant option values
          if (variantOptions) {
            const optionValueMap = new Map();

            // Build a map of option name:value to optionValueId
            for (const option of product.options) {
              for (const optionValue of option.optionValues) {
                optionValueMap.set(
                  `${option.name}:${optionValue.value}`,
                  optionValue.id,
                );
              }
            }

            const variantOptionValueInserts = [];
            for (const [optionName, optionValue] of Object.entries(
              variantOptions,
            )) {
              const optionValueId = optionValueMap.get(
                `${optionName}:${optionValue}`,
              );
              if (optionValueId) {
                variantOptionValueInserts.push({
                  variantId: variant!.id,
                  optionValueId,
                });
              }
            }

            if (variantOptionValueInserts.length > 0) {
              await tx
                .insert(variantOptionValues)
                .values(variantOptionValueInserts);
            }
          }

          // Create prices
          if (variantPrices) {
            await tx.insert(prices).values({
              variantId: variant!.id,
              tradeInPrice: variantPrices.tradeInPrice,
              consignmentPrice: variantPrices.consignmentPrice,
              buybackPrice: variantPrices.buybackPrice,
              retailPrice: variantPrices.retailPrice,
            });
          }

          // Create variant images using indices
          if (imageIndices && imageIndices.length > 0) {
            // Get product images to map indices to IDs
            const productImages = await tx.query.images.findMany({
              where: eq(images.productId, id),
              orderBy: images.createdAt, // Maintain order
            });

            const variantImageInserts = imageIndices
              .map((index) => {
                const image = productImages[index];
                return image
                  ? { variantId: variant!.id, imageId: image.id }
                  : null;
              })
              .filter(
                (insert): insert is NonNullable<typeof insert> =>
                  insert !== null,
              );

            if (variantImageInserts.length > 0) {
              await tx.insert(variantImages).values(variantImageInserts);
            }
          }

          return variant;
        });
      });

      return buildJsonResponse({
        c,
        message: 'Product variant created successfully',
        payload: result,
      });
    },
  )
  .put(
    '/:id/variants/:variantId',
    zValidator(
      'param',
      z.object({ id: z.uuid(), variantId: z.uuid() }),
      handleZodError,
    ),
    zValidator('json', updateVariantSchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id, variantId } = c.req.valid('param');
      const {
        options: variantOptions,
        prices: variantPrices,
        imageIndices,
        ...variantData
      } = c.req.valid('json');

      const result = await tryCatching(async () => {
        // Check if product exists
        const product = await db.query.products.findFirst({
          where: eq(products.id, id),
          with: {
            options: {
              with: {
                optionValues: true,
              },
            },
          },
        });

        if (!product) {
          throw new HttpError({
            statusCode: 404,
            message: `Product with ID ${id} not found`,
          });
        }

        // Check if variant exists
        const existingVariant = await db.query.variants.findFirst({
          where: and(eq(variants.id, variantId), eq(variants.productId, id)),
        });

        if (!existingVariant) {
          throw new HttpError({
            statusCode: 404,
            message: `Variant with ID ${variantId} not found`,
          });
        }

        return db.transaction(async (tx) => {
          // Update variant
          const [variant] = await tx
            .update(variants)
            .set({
              ...variantData,
              updatedAt: new Date().toISOString(),
            })
            .where(eq(variants.id, variantId))
            .returning();

          // Update prices if provided
          if (variantPrices) {
            await tx
              .update(prices)
              .set({
                tradeInPrice: variantPrices.tradeInPrice,
                consignmentPrice: variantPrices.consignmentPrice,
                buybackPrice: variantPrices.buybackPrice,
                retailPrice: variantPrices.retailPrice,
                updatedAt: new Date().toISOString(),
              })
              .where(eq(prices.variantId, variantId));
          }

          // Update variant option values if provided
          if (variantOptions) {
            // Delete existing variant option values
            await tx
              .delete(variantOptionValues)
              .where(eq(variantOptionValues.variantId, variantId));

            // Create new variant option values
            for (const [optionName, optionValue] of Object.entries(
              variantOptions,
            )) {
              const option = product.options.find(
                (opt) => opt.name === optionName,
              );
              if (option) {
                const optionValueRecord = option.optionValues.find(
                  (val) => val.value === optionValue,
                );
                if (optionValueRecord) {
                  await tx.insert(variantOptionValues).values({
                    variantId: variant!.id,
                    optionValueId: optionValueRecord.id,
                  });
                }
              }
            }
          }

          // Update variant images if provided
          if (imageIndices !== undefined) {
            // Delete existing variant images
            await tx
              .delete(variantImages)
              .where(eq(variantImages.variantId, variantId));

            // Create new variant images if any
            if (imageIndices.length > 0) {
              // Get product images to map indices to IDs
              const productImages = await tx.query.images.findMany({
                where: eq(images.productId, id),
                orderBy: images.createdAt, // Maintain order
              });

              const variantImageInserts = imageIndices
                .map((index) => {
                  const image = productImages[index];
                  return image
                    ? { variantId: variantId, imageId: image.id }
                    : null;
                })
                .filter(
                  (insert): insert is NonNullable<typeof insert> =>
                    insert !== null,
                );

              if (variantImageInserts.length > 0) {
                await tx.insert(variantImages).values(variantImageInserts);
              }
            }
          }

          return variant;
        });
      });

      return buildJsonResponse({
        c,
        message: 'Product variant updated successfully',
        payload: result,
      });
    },
  )
  .delete(
    '/:id/variants/:variantId',
    zValidator(
      'param',
      z.object({ id: z.uuid(), variantId: z.uuid() }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const { id, variantId } = c.req.valid('param');

      await tryCatching(async () => {
        const product = await db.query.products.findFirst({
          where: eq(products.id, id),
        });

        if (!product) {
          throw new HttpError({
            statusCode: 404,
            message: `Product with ID ${id} not found`,
          });
        }

        await db.delete(variants).where(eq(variants.id, variantId));
      });

      return buildJsonResponse({
        c,
        message: 'Product variant deleted successfully',
      });
    },
  )
  .post(
    '/:id/images',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator(
      'form',
      z.object({
        images: z
          .union([
            z.instanceof(File, { message: 'Image is required' }),
            z.array(z.instanceof(File, { message: 'Image is required' })),
          ])
          .transform((value) => (Array.isArray(value) ? value : [value])),
      }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');
      const { images } = c.req.valid('form');

      await tryCatching(async () => {
        const product = await db.query.products.findFirst({
          where: eq(products.id, id),
        });

        if (!product) {
          throw new HttpError({
            statusCode: 404,
            message: `Product with ID ${id} not found`,
          });
        }

        const imageKeys: string[] = [];
        for (const image of images) {
          const key = await uploadObj(c, image);
          imageKeys.push(key);
        }

        await db.insert(imagesTable).values(
          imageKeys.map((key) => ({
            path: key,
            productId: id,
          })),
        );
      });

      return buildJsonResponse({
        c,
        message: 'Product images uploaded successfully',
      });
    },
  )

  .put(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator('json', updateProductSchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');
      const { name, slug, description, isPublished, categoryId, brandId } =
        c.req.valid('json');
      const result = await tryCatching(async () => {
        // Get the existing product to check for an image
        const existingProduct = await db.query.products.findFirst({
          where: eq(products.id, id),
        });

        if (!existingProduct) {
          throw new HttpError({
            statusCode: 404,
            message: `Product with ID ${id} not found`,
          });
        }

        const [product] = await db
          .update(products)
          .set({
            ...{ name, slug, description, categoryId, brandId, isPublished },
            updatedAt: new Date().toISOString(),
          })
          .where(eq(products.id, id))
          .returning();

        return product;
      });

      return buildJsonResponse({
        c,
        message: 'Product updated successfully',
        payload: result,
      });
    },
  )
  .delete(
    '/:id/images/:imageId',
    zValidator(
      'param',
      z.object({ id: z.uuid(), imageId: z.uuid() }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const { id, imageId } = c.req.valid('param');

      await tryCatching(async () => {
        const product = await db.query.products.findFirst({
          where: eq(products.id, id),
        });

        if (!product) {
          throw new HttpError({
            statusCode: 404,
            message: `Product with ID ${id} not found`,
          });
        }

        const image = await db.query.images.findFirst({
          where: eq(images.id, imageId),
        });

        if (!image) {
          throw new HttpError({
            statusCode: 404,
            message: `Product image with ID ${imageId} not found`,
          });
        }

        await deleteObj(c, imageId);
        await db.delete(images).where(eq(images.id, imageId));
      });

      return buildJsonResponse({
        c,
        message: 'Category image deleted successfully',
      });
    },
  )
  .delete(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');

      await tryCatching(async () => {
        // Get the product to check if it has an image
        const product = await db.query.products.findFirst({
          where: eq(products.id, id),
        });

        if (!product) {
          throw new HttpError({
            statusCode: 404,
            message: `Product with ID ${id} not found`,
          });
        }

        // Delete the product
        await db.delete(products).where(eq(products.id, id));
      });

      return buildJsonResponse({
        c,
        message: 'Product deleted successfully',
      });
    },
  );

export { route };
