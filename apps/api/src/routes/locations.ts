import { zValidator } from '@hono/zod-validator';
import { and, count, eq, ilike } from '@muraadso/db';
import { locations } from '@muraadso/db/schema/locations';
import {
  createLocationSchema,
  updateLocationSchema,
} from '@muraadso/models/locations';
import { Hono } from 'hono';
import { z } from 'zod/v4';
import type { Env } from '../env';
import { HttpError } from '../utils/errors/custom-errors';
import { handleZodError } from '../utils/errors/error-handler';
import { tryCatching } from '../utils/errors/try-catching';
import { buildJsonResponse } from '../utils/response';

const route = new Hono<Env>()
  .get('/', async (c) => {
    const db = c.get('db');
    const result = await tryCatching(async () => db.query.locations.findMany());

    return buildJsonResponse({
      c,
      message: 'Locations retrieved successfully',
      payload: result,
    });
  })
  .get(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');

      const result = await tryCatching(async () => {
        const location = await db.query.locations.findFirst({
          where: (locations, { eq }) => eq(locations.id, id),
        });

        if (!location) {
          throw new HttpError({
            statusCode: 404,
            message: `Location with ID ${id} not found`,
          });
        }

        return location;
      });

      return buildJsonResponse({
        c,
        message: 'Location retrieved successfully',
        payload: result,
      });
    },
  )
  .post(
    '/',
    zValidator('json', createLocationSchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const locationData = c.req.valid('json');

      const result = await tryCatching(async () => {
        // Create the location
        const [location] = await db
          .insert(locations)
          .values(locationData)
          .returning();

        if (!location || !location.id) {
          throw new HttpError({
            statusCode: 500,
            message: "Error! Couldn't create location",
          });
        }

        return location;
      });

      return buildJsonResponse({
        c,
        message: 'Location created successfully',
        payload: result,
      });
    },
  )
  .put(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator('json', updateLocationSchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');
      const locationData = c.req.valid('json');

      const result = await tryCatching(async () => {
        const existingLocation = await db.query.locations.findFirst({
          where: eq(locations.id, id),
        });

        if (!existingLocation) {
          throw new HttpError({
            statusCode: 404,
            message: `Location with ID ${id} not found`,
          });
        }

        const [location] = await db
          .update(locations)
          .set({
            ...locationData,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(locations.id, id))
          .returning();

        return location;
      });

      return buildJsonResponse({
        c,
        message: 'Location updated successfully',
        payload: result,
      });
    },
  )
  .delete(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');

      await tryCatching(async () => {
        const location = await db.query.locations.findFirst({
          where: eq(locations.id, id),
        });

        if (!location) {
          throw new HttpError({
            statusCode: 404,
            message: `Location with ID ${id} not found`,
          });
        }

        await db.delete(locations).where(eq(locations.id, id));
      });

      return buildJsonResponse({
        c,
        message: 'Location deleted successfully',
      });
    },
  );

export { route };
