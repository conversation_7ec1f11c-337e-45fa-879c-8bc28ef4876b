import {
  type Customer,
  selectCustomerSchema,
} from '@muraadso/models/customers';
import {
  type CreateOrder,
  type Order,
  selectOrderSchema,
} from '@muraadso/models/orders';
import type { Product } from '@muraadso/models/products';
import { beforeAll, describe, expect, it } from 'vitest';
import app from '../server';
import { apiRequest } from '../utils/request';
import type { ApiResponse } from '../utils/response';

// Test data variables
let orderId: string;
let customerId: string;
let productId: string;
let variantId: string;

// Response schemas for validation
describe('Orders API Setup', () => {
  beforeAll(async () => {
    // Get a customer for testing
    await new Promise((resolve, reject) => {
      apiRequest(app)
        .get('/v1/customers')
        .then((res) => res.json())
        .then((body: ApiResponse<{ customers: Customer[] }>) => {
          const { data } = selectCustomerSchema
            .array()
            .safeParse(body?.payload?.customers);
          const first = data?.at(0)?.id;
          if (first) customerId = first;
          resolve(true);
        })
        .catch((err) => {
          reject(err);
        });
    });

    // Get a product and variant for testing
    await new Promise((resolve, reject) => {
      apiRequest(app)
        .get('/v1/products')
        .then((res) => res.json())
        .then((body: ApiResponse<{ products: Product[] }>) => {
          const products = body?.payload?.products;
          if (products && products.length > 0) {
            const product = products[0];
            if (product) {
              productId = product.id;
              const firstVariant = product.variants[0];
              if (
                product.variants &&
                product.variants.length > 0 &&
                firstVariant
              ) {
                variantId = firstVariant.id;
              }
            }
          }
          resolve(true);
        })
        .catch((err) => {
          reject(err);
        });
    });

    // Get an existing order for testing updates
    await new Promise((resolve, reject) => {
      apiRequest(app)
        .get('/v1/orders')
        .then((res) => res.json())
        .then((body: ApiResponse<{ orders: Order[] }>) => {
          const orders = body?.payload?.orders;
          const firstOrder = orders?.[0];
          if (firstOrder) orderId = firstOrder.id;
          resolve(true);
        })
        .catch((err) => {
          reject(err);
        });
    });
  });

  // Add a dummy test to ensure the suite is not empty
  it('should complete setup successfully', () => {
    expect(customerId).toBeDefined();
    expect(productId).toBeDefined();
    expect(orderId).toBeDefined();
  });
});

describe('GET /v1/orders', () => {
  it('should return a list of orders', async () => {
    const res = await apiRequest(app).get('/v1/orders');
    const body = (await res.json()) as ApiResponse<{
      orders: Order[];
      pagination: {
        page: number;
        pageSize: number;
        totalCount: number;
        totalPages: number;
        hasNextPage: boolean;
        hasPreviousPage: boolean;
      };
    }>;

    expect(res.ok).toBe(true);
    expect(body.payload).toBeDefined();
    expect(body.payload?.orders).toBeDefined();
    expect(Array.isArray(body.payload?.orders)).toBe(true);
    expect(body.payload?.orders.length).toBeGreaterThan(0);
    expect(body.payload?.pagination).toBeDefined();
  });

  it('should filter orders by status', async () => {
    const res = await apiRequest(app).get('/v1/orders?status=completed');
    const body = (await res.json()) as ApiResponse<{
      orders: Order[];
      pagination: unknown;
    }>;

    expect(res.ok).toBe(true);
    expect(body.payload?.orders).toBeDefined();
    for (const order of body.payload?.orders || []) {
      expect(order.status).toBe('completed');
    }
  });
});

describe('GET /v1/orders/:id', () => {
  it('should return a single order by id', async () => {
    const res = await apiRequest(app).get(`/v1/orders/${orderId}`);
    const body = (await res.json()) as ApiResponse<Order>;
    const { data, success } = selectOrderSchema.safeParse(body.payload);

    expect(res.ok).toBe(true);
    expect(success).toBe(true);
    expect(data?.id).toBe(orderId);
    expect(data?.customer).toBeDefined();
    expect(data?.items).toBeDefined();
  });

  it('should return 404 for non-existent order', async () => {
    const nonExistentId = '00000000-0000-0000-0000-000000000000';
    const res = await apiRequest(app).get(`/v1/orders/${nonExistentId}`);

    expect(res.status).toBe(404);
  });

  it('should return 422 for invalid UUID', async () => {
    const res = await apiRequest(app).get('/v1/orders/invalid-uuid');

    expect(res.status).toBe(422);
  });
});

describe('POST /v1/orders (Enhanced API)', () => {
  it('should create an order with existing customer successfully', async () => {
    const orderData: CreateOrder = {
      customerId,
      address: '123 Test Street, Test City, TC 12345',
      serviceType: 'retail',
      note: 'Test order created via enhanced API',
      commissionRate: 5,
      items: [
        {
          productId,
          variantId,
          unitPrice: 999.99,
          quantity: 1,
          metadata: { test: 'data' },
        },
      ],
    };

    const res = await apiRequest(app).post('/v1/orders', {
      body: orderData,
    });
    const body = (await res.json()) as ApiResponse<Order>;
    const { data, success } = selectOrderSchema.safeParse(body.payload);

    expect(res.status).toBe(200);
    expect(success).toBe(true);
    expect(data?.customerId).toBe(customerId);
    expect(data?.customer).toBeDefined();
    expect(data?.address).toBe(orderData.address);
    expect(data?.serviceType).toBe(orderData.serviceType);
    expect(data?.note).toBe(orderData.note);
    expect(data?.commissionRate).toBe(orderData.commissionRate);
    expect(data?.status).toBe('pending');
    expect(data?.trackingId).toBeDefined();
    expect(data?.customer).toBeDefined();
    expect(data?.items).toBeDefined();
    expect(data?.items?.length).toBe(1);
    expect(data?.items?.[0]?.productId).toBe(productId);
    expect(data?.items?.[0]?.variantId).toBe(variantId);
    expect(data?.items?.[0]?.unitPrice).toBe(999.99);
    expect(data?.items?.[0]?.quantity).toBe(1);
  });

  it('should create an order with new customer successfully', async () => {
    const orderData: CreateOrder = {
      address: '456 New Customer Street, Test City, TC 12345',
      serviceType: 'buyback',
      note: 'Test order with new customer',
      commissionRate: 5,
      customer: {
        name: 'John Doe',
        phone: '+1234567890',
        email: '<EMAIL>',
      },
      guarantor: {
        name: 'Jane Guarantor',
        phone: '+1111111111',
        relationship: 'spouse',
      },
      items: [
        {
          productId,
          variantId,
          unitPrice: 999.99,
          quantity: 1,
          metadata: { test: 'new_customer' },
        },
      ],
    };

    const res = await apiRequest(app).post('/v1/orders', {
      body: orderData,
    });
    const body = (await res.json()) as ApiResponse<Order>;

    expect(res.status).toBe(200);
    expect(body.payload?.customer?.name).toBe('John Doe');
    expect(body.payload?.customer?.email).toBe('<EMAIL>');
    expect(body.payload?.guarantor?.name).toBe('Jane Guarantor');
    expect(body.payload?.items?.length).toBe(1);
  });

  it('should create an order with new customer without user', async () => {
    const orderData: CreateOrder = {
      address: '789 Guest Customer Street, Test City, TC 12345',
      serviceType: 'retail',
      note: 'Test order with guest customer',
      commissionRate: 5,
      customer: {
        name: 'Jane Guest',
        phone: '+0987654321',
        email: '<EMAIL>',
      },
      items: [
        {
          productId,
          variantId,
          unitPrice: 999.99,
          quantity: 1,
          metadata: { test: 'guest_customer' },
        },
      ],
    };

    const res = await apiRequest(app).post('/v1/orders', {
      body: orderData,
    });
    const body = (await res.json()) as ApiResponse<Order>;

    expect(res.status).toBe(200);
    expect(body.payload?.customer?.name).toBe('Jane Guest');
    expect(body.payload?.customer?.email).toBe('<EMAIL>');
    expect(body.payload?.items?.length).toBe(1);
  });

  it('should create an order with multiple items', async () => {
    const orderData: CreateOrder = {
      customerId,
      address: '456 Multi Item Street, Test City, TC 12345',
      serviceType: 'retail',
      items: [
        {
          productId,
          variantId,
          unitPrice: 999.99,
          quantity: 1,
        },
        {
          productId,
          variantId,
          unitPrice: 999.99,
          quantity: 1,
        },
      ],
    };

    const res = await apiRequest(app).post('/v1/orders', {
      body: orderData,
    });
    const body = (await res.json()) as ApiResponse<Order>;

    expect(res.status).toBe(200);
    expect(body.payload?.items?.length).toBe(2);
  });

  it('should return 404 for non-existent customer', async () => {
    const nonExistentCustomerId = '00000000-0000-0000-0000-000000000000';
    const orderData: CreateOrder = {
      customerId: nonExistentCustomerId,
      address: '123 Test Street',
      serviceType: 'retail',
      items: [
        {
          productId,
          variantId,
          unitPrice: 999.99,
          quantity: 1,
        },
      ],
    };

    const res = await apiRequest(app).post('/v1/orders', {
      body: orderData,
    });

    expect(res.status).toBe(404);
  });

  it('should return 404 for non-existent product', async () => {
    const nonExistentProductId = '00000000-0000-0000-0000-000000000000';
    const orderData: CreateOrder = {
      customerId,
      address: '123 Test Street',
      serviceType: 'retail',
      items: [
        {
          productId: nonExistentProductId,
          variantId,
          unitPrice: 999.99,
          quantity: 1,
        },
      ],
    };

    const res = await apiRequest(app).post('/v1/orders', {
      body: orderData,
    });

    expect(res.status).toBe(404);
  });

  it('should return 404 for non-existent variant', async () => {
    const nonExistentVariantId = '00000000-0000-0000-0000-000000000000';
    const orderData: CreateOrder = {
      customerId,
      address: '123 Test Street',
      serviceType: 'retail',
      items: [
        {
          productId,
          variantId: nonExistentVariantId,
          unitPrice: 999.99,
          quantity: 1,
        },
      ],
    };

    const res = await apiRequest(app).post('/v1/orders', {
      body: orderData,
    });

    expect(res.status).toBe(404);
  });

  it('should return 422 for empty items array', async () => {
    const orderData = {
      customerId,
      address: '123 Test Street',
      serviceType: 'retail',
      items: [],
    };

    const res = await apiRequest(app).post('/v1/orders', {
      body: orderData,
    });

    expect(res.status).toBe(422);
  });

  it('should return 422 for missing required fields', async () => {
    const orderData = {
      // Missing customerId
      address: '123 Test Street',
      serviceType: 'retail',
      items: [
        {
          productId,
          variantId,
          unitPrice: 999.99,
          quantity: 1,
        },
      ],
    };

    const res = await apiRequest(app).post('/v1/orders', {
      body: orderData,
    });

    expect(res.status).toBe(422);
  });

  it('should return 422 for invalid service type', async () => {
    const orderData = {
      customerId,
      address: '123 Test Street',
      serviceType: 'invalid-service-type',
      items: [
        {
          productId,
          variantId,
          unitPrice: 999.99,
          quantity: 1,
        },
      ],
    };

    const res = await apiRequest(app).post('/v1/orders', {
      body: orderData,
    });

    expect(res.status).toBe(422);
  });

  it('should return 422 for negative unit price', async () => {
    const orderData: CreateOrder = {
      customerId,
      address: '123 Test Street',
      serviceType: 'retail',
      items: [
        {
          productId,
          variantId,
          unitPrice: -999.99, // Invalid negative price
          quantity: 1,
        },
      ],
    };

    const res = await apiRequest(app).post('/v1/orders', {
      body: orderData,
    });

    expect(res.status).toBe(422);
  });

  it('should return 422 for zero or negative quantity', async () => {
    const orderData: CreateOrder = {
      customerId,
      address: '123 Test Street',
      serviceType: 'retail',
      items: [
        {
          productId,
          variantId,
          unitPrice: 999.99,
          quantity: 0, // Invalid zero quantity
        },
      ],
    };

    const res = await apiRequest(app).post('/v1/orders', {
      body: orderData,
    });

    expect(res.status).toBe(422);
  });
});

describe('PUT /v1/orders/:id', () => {
  it('should update an order successfully', async () => {
    // First get the current order
    const getRes = await apiRequest(app).get(`/v1/orders/${orderId}`);
    const currentOrder = (await getRes.json()) as ApiResponse<Order>;

    const updateData = {
      note: 'Updated note via test',
    };

    const res = await apiRequest(app).put(`/v1/orders/${orderId}`, {
      body: updateData,
    });
    const body = (await res.json()) as ApiResponse<Order>;
    const { data, success } = selectOrderSchema.safeParse(body.payload);

    expect(res.ok).toBe(true);
    expect(success).toBe(true);
    expect(data?.id).toBe(orderId);
    expect(data?.note).toBe(updateData.note);
    expect(data?.updatedAt).not.toBe(currentOrder.payload?.updatedAt);
  });

  it('should return 404 for non-existent order', async () => {
    const nonExistentId = '00000000-0000-0000-0000-000000000000';
    const updateData = {
      note: 'Updated note',
    };

    const res = await apiRequest(app).put(`/v1/orders/${nonExistentId}`, {
      body: updateData,
    });

    expect(res.status).toBe(404);
  });

  it('should return 422 for invalid data', async () => {
    const invalidData = {
      status: 'invalid-status',
    };

    const res = await apiRequest(app).put(`/v1/orders/${orderId}`, {
      body: invalidData,
    });

    expect(res.status).toBe(422);
  });
});

describe('PUT /v1/orders/:id/status', () => {
  it('should update status successfully', async () => {
    const getRes = await apiRequest(app).get(`/v1/orders/${orderId}`);
    const currentOrder = (await getRes.json()) as ApiResponse<Order>;

    const newStatus =
      currentOrder.payload?.status === 'pending' ? 'completed' : 'pending';

    const res = await apiRequest(app).put(`/v1/orders/${orderId}/status`, {
      body: {
        status: newStatus,
        comment: 'Status updated via test',
      },
    });
    const body = (await res.json()) as ApiResponse<Order>;
    const { data, success } = selectOrderSchema.safeParse(body.payload);

    expect(res.ok).toBe(true);
    expect(success).toBe(true);
    expect(data?.id).toBe(orderId);
    expect(data?.status).toBe(newStatus);
    expect(data?.updatedAt).not.toBe(currentOrder.payload?.updatedAt);
  });

  it('should return 404 for non-existent order', async () => {
    const nonExistentId = '00000000-0000-0000-0000-000000000000';

    const res = await apiRequest(app).put(
      `/v1/orders/${nonExistentId}/status`,
      {
        body: {
          status: 'completed',
        },
      },
    );

    expect(res.status).toBe(404);
  });

  it('should return 422 for invalid status', async () => {
    const res = await apiRequest(app).put(`/v1/orders/${orderId}/status`, {
      body: {
        status: 'invalid-status',
      },
    });

    expect(res.status).toBe(422);
  });
});
