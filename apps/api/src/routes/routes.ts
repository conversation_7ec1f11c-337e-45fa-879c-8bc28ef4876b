import { Hono } from 'hono';
import { route as auth } from './auth';
import { route as cities } from './cities';
import { route as countries } from './countries';
import { route as customers } from './customers';
import { route as dashboard } from './dashboard';
import { route as districts } from './districts';
import { route as inventory } from './inventory';
import { route as locations } from './locations';
import { route as media } from './media';
import { route as orders } from './orders';
import { route as brands } from './products/brands';
import { route as categories } from './products/categories';
import { route as products } from './products/products';
import { route as sysops } from './sysops';
import { route as upload } from './upload';
import { route as users } from './users';

const routes = new Hono();
routes.route('/auth', auth);
routes.route('/media', media);
routes.route('/upload', upload);
routes.route('/categories', categories);
routes.route('/brands', brands);
routes.route('/products', products);
routes.route('/customers', customers);
routes.route('/countries', countries);
routes.route('/cities', cities);
routes.route('/districts', districts);
routes.route('/locations', locations);
routes.route('/inventory', inventory);
routes.route('/dashboard', dashboard);
routes.route('/orders', orders);
routes.route('/sysops', sysops);
routes.route('/sysops', users);

export { routes };
