import { zValidator } from '@hono/zod-validator';
import { and, count, eq, gt, gte, ilike, lte, or, sql } from '@muraadso/db';
import { inventory } from '@muraadso/db/schema/inventory';
import { locations } from '@muraadso/db/schema/locations';
import { prices } from '@muraadso/db/schema/prices';
import { variants } from '@muraadso/db/schema/variants';
import { Hono } from 'hono';
import { z } from 'zod/v4';
import type { Env } from '../env';
import { HttpError } from '../utils/errors/custom-errors';
import { handleZodError } from '../utils/errors/error-handler';
import { tryCatching } from '../utils/errors/try-catching';
import { buildJsonResponse } from '../utils/response';

import { products } from '@muraadso/db/schema/products';
import {
  createInventorySchema,
  updateInventorySchema,
} from '@muraadso/models/inventory';

const route = new Hono<Env>()
  .get('/stats', async (c) => {
    const db = c.get('db');

    const result = await tryCatching(async () => {
      // Total inventory value (quantity * retail price)
      const [inventoryValueResult] = await db
        .select({
          totalValue:
            sql<number>`COALESCE(SUM(${inventory.quantity} * ${prices.retailPrice}), 0)`.as(
              'totalValue',
            ),
          totalUnits: sql<number>`COALESCE(SUM(${inventory.quantity}), 0)`.as(
            'totalUnits',
          ),
        })
        .from(inventory)
        .leftJoin(variants, eq(inventory.variantId, variants.id))
        .leftJoin(prices, eq(variants.id, prices.variantId));

      const totalInventoryValue = inventoryValueResult?.totalValue || 0;
      const totalUnitsInStock = inventoryValueResult?.totalUnits || 0;

      // Get all variant-location combinations to properly calculate stock status
      const allVariantLocationCombinations = await db
        .select({
          variantId: variants.id,
          locationId: locations.id,
          quantity: sql<number>`COALESCE(${inventory.quantity}, 0)`.as(
            'quantity',
          ),
        })
        .from(variants)
        .crossJoin(locations)
        .leftJoin(
          inventory,
          sql`(${inventory.variantId} = ${variants.id} AND ${inventory.locationId} = ${locations.id})`,
        );

      // Calculate stock alerts properly
      const lowStockCount = allVariantLocationCombinations.filter(
        (item) => item.quantity > 0 && item.quantity <= 5,
      ).length;

      const outOfStockCount = allVariantLocationCombinations.filter(
        (item) => item.quantity === 0,
      ).length;

      // Overstocked products (quantity > 50) - only count actual inventory records
      const overstockedCount = allVariantLocationCombinations.filter(
        (item) => item.quantity >= 50,
      ).length;

      // Inventory by location
      const inventoryByLocation = await db
        .select({
          locationId: locations.id,
          locationName: locations.name,
          totalUnits: sql<number>`COALESCE(SUM(${inventory.quantity}), 0)`.as(
            'totalUnits',
          ),
          totalValue:
            sql<number>`COALESCE(SUM(${inventory.quantity} * ${prices.retailPrice}), 0)`.as(
              'totalValue',
            ),
          uniqueProducts: count(sql`DISTINCT ${inventory.variantId}`),
        })
        .from(locations)
        .leftJoin(inventory, eq(locations.id, inventory.locationId))
        .leftJoin(variants, eq(inventory.variantId, variants.id))
        .leftJoin(prices, eq(variants.id, prices.variantId))
        .groupBy(locations.id, locations.name);

      // Top locations by inventory value
      const topLocationsByValue = inventoryByLocation
        .sort((a, b) => b.totalValue - a.totalValue)
        .slice(0, 5)
        .map((l) => ({
          name: l.locationName,
          totalValue: l.totalValue,
          totalUnits: l.totalUnits,
          uniqueProducts: l.uniqueProducts,
        }));

      // Average inventory per location
      const activeLocations = inventoryByLocation.filter(
        (l) => l.totalUnits > 0,
      ).length;
      const avgInventoryPerLocation =
        activeLocations > 0 ? totalInventoryValue / activeLocations : 0;

      // Most stocked products
      const topStockedProducts = await db
        .select({
          productName: products.name,
          variantName: variants.name,
          totalQuantity: sql<number>`SUM(${inventory.quantity})`.as(
            'totalQuantity',
          ),
          totalValue:
            sql<number>`SUM(${inventory.quantity} * ${prices.retailPrice})`.as(
              'totalValue',
            ),
        })
        .from(inventory)
        .leftJoin(variants, eq(inventory.variantId, variants.id))
        .leftJoin(products, eq(variants.productId, products.id))
        .leftJoin(prices, eq(variants.id, prices.variantId))
        .groupBy(products.id, products.name, variants.id, variants.name)
        .orderBy(sql`SUM(${inventory.quantity}) DESC`)
        .limit(5);

      return {
        totalInventoryValue,
        totalUnitsInStock,
        lowStockCount,
        outOfStockCount,
        overstockedCount,
        topLocationsByValue,
        avgInventoryPerLocation,
        topStockedProducts: topStockedProducts.map((p) => ({
          name: `${p.productName} / ${p.variantName}`,
          quantity: p.totalQuantity,
          value: p.totalValue,
        })),
        activeLocationsCount: activeLocations,
      };
    });

    return buildJsonResponse({
      c,
      message: 'Inventory stats retrieved successfully',
      payload: result,
    });
  })
  .get(
    '/',
    zValidator(
      'query',
      z.object({
        q: z.string().optional(),
        page: z.coerce.number().positive().default(1),
        pageSize: z.coerce.number().positive().default(10),
        locationId: z.uuid().optional(),
        variantId: z.uuid().optional(),
      }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const { q, page, pageSize, locationId, variantId } = c.req.valid('query');

      const offset = (page - 1) * pageSize;

      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      const whereConditions: any[] = [];

      if (q) {
        whereConditions.push(
          or(
            ilike(products.name, `%${q}%`),
            ilike(variants.name, `%${q}%`),
            ilike(locations.name, `%${q}%`),
          ),
        );
      }

      if (locationId) {
        whereConditions.push(eq(locations.id, locationId));
      }

      if (variantId) {
        whereConditions.push(eq(variants.id, variantId));
      }

      const totalCount = await tryCatching(async () => {
        const result = await db
          .select({ count: count() })
          .from(variants)
          .innerJoin(products, sql`${products.id} = ${variants.productId}`)
          .crossJoin(locations)
          .leftJoin(
            inventory,
            sql`(${inventory.variantId} = ${variants.id} AND ${inventory.locationId} = ${locations.id})`,
          )
          .where(
            whereConditions.length > 0 ? and(...whereConditions) : undefined,
          );
        return Number(result[0]?.count || 0);
      });

      const result = await tryCatching(async () => {
        return await db
          .select({
            productId: products.id,
            productName: products.name,
            variantId: variants.id,
            variantName: variants.name,
            variantSKU: variants.sku,
            locationId: locations.id,
            locationName: locations.name,
            inventoryId: inventory.id,
            updatedAt: inventory.updatedAt,
            quantity: sql<number>`COALESCE(${inventory.quantity}, 0)`.as('qty'),
          })
          .from(variants)
          .innerJoin(products, sql`${products.id} = ${variants.productId}`)
          .crossJoin(locations)
          .leftJoin(
            inventory,
            sql`(${inventory.variantId} = ${variants.id} AND ${inventory.locationId} = ${locations.id})`,
          )
          .where(
            whereConditions.length > 0 ? and(...whereConditions) : undefined,
          )
          .orderBy(variants.name)
          .limit(pageSize)
          .offset(offset);
      });

      return buildJsonResponse({
        c,
        message: 'Inventory items retrieved successfully',
        payload: {
          inventory: result,
          pagination: {
            page,
            pageSize,
            totalCount,
            totalPages: Math.ceil(totalCount / pageSize),
            hasNextPage: page < Math.ceil(totalCount / pageSize),
            hasPreviousPage: page > 1,
          },
        },
      });
    },
  )
  .get(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');

      const result = await tryCatching(async () => {
        const inventoryItem = await db.query.inventory.findFirst({
          where: (inventory, { eq }) => eq(inventory.id, id),
          with: {
            variant: {
              with: {
                product: true,
              },
            },
            location: true,
          },
        });

        if (!inventoryItem) {
          throw new HttpError({
            statusCode: 404,
            message: `Inventory item with ID ${id} not found`,
          });
        }

        return inventoryItem;
      });

      return buildJsonResponse({
        c,
        message: 'Inventory item retrieved successfully',
        payload: result,
      });
    },
  )
  .get(
    '/:locationId/:variantId',
    zValidator(
      'param',
      z.object({
        locationId: z.uuid(),
        variantId: z.uuid(),
      }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const { locationId, variantId } = c.req.valid('param');

      // if (locationId) {
      //   whereConditions.push(eq(locations.id, locationId));
      // }
      //
      // if (variantId) {
      //   whereConditions.push(eq(variants.id, variantId));
      // }

      const result = await tryCatching(async () => {
        return await db
          .select({
            productId: products.id,
            productName: products.name,
            variantId: variants.id,
            variantName: variants.name,
            variantSKU: variants.sku,
            locationId: locations.id,
            locationName: locations.name,
            inventoryId: inventory.id,
            updatedAt: inventory.updatedAt,
            quantity: sql<number>`COALESCE(${inventory.quantity}, 0)`.as('qty'),
          })
          .from(variants)
          .innerJoin(products, sql`${products.id} = ${variants.productId}`)
          .crossJoin(locations)
          .leftJoin(
            inventory,
            sql`(${inventory.variantId} = ${variants.id} AND ${inventory.locationId} = ${locations.id})`,
          )
          .where(and(eq(locations.id, locationId), eq(variants.id, variantId)))
          .limit(1);
      });

      return buildJsonResponse({
        c,
        message: 'Inventory item retrieved successfully',
        payload: result,
      });
    },
  )
  .post(
    '/',
    zValidator('json', createInventorySchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const inventoryData = c.req.valid('json');

      const result = await tryCatching(async () => {
        // Check if variant exists
        const variant = await db.query.variants.findFirst({
          where: eq(variants.id, inventoryData.variantId),
        });

        if (!variant) {
          throw new HttpError({
            statusCode: 404,
            message: `Variant with ID ${inventoryData.variantId} not found`,
          });
        }

        const location = await db.query.locations.findFirst({
          where: eq(locations.id, inventoryData.locationId),
        });

        if (!location) {
          throw new HttpError({
            statusCode: 404,
            message: `Location with ID ${inventoryData.locationId} not found`,
          });
        }

        const existingInventory = await db.query.inventory.findFirst({
          where: and(
            eq(inventory.variantId, inventoryData.variantId),
            eq(inventory.locationId, inventoryData.locationId),
          ),
        });

        if (existingInventory) {
          throw new HttpError({
            statusCode: 400,
            message:
              'Inventory item already exists for this variant and location',
          });
        }

        // Create the inventory item
        const [inventoryItem] = await db
          .insert(inventory)
          .values(inventoryData)
          .returning();

        if (!inventoryItem || !inventoryItem.id) {
          throw new HttpError({
            statusCode: 500,
            message: "Error! Couldn't create inventory item",
          });
        }

        return inventoryItem;
      });

      return buildJsonResponse({
        c,
        message: 'Inventory item created successfully',
        payload: result,
      });
    },
  )
  .put(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator('json', updateInventorySchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');
      const inventoryData = c.req.valid('json');

      const result = await tryCatching(async () => {
        const existingInventory = await db.query.inventory.findFirst({
          where: eq(inventory.id, id),
        });

        if (!existingInventory) {
          throw new HttpError({
            statusCode: 404,
            message: `Inventory item with ID ${id} not found`,
          });
        }

        const [inventoryItem] = await db
          .update(inventory)
          .set({
            ...inventoryData,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(inventory.id, id))
          .returning();

        return inventoryItem;
      });

      return buildJsonResponse({
        c,
        message: 'Inventory item updated successfully',
        payload: result,
      });
    },
  )
  .delete(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');

      await tryCatching(async () => {
        const inventoryItem = await db.query.inventory.findFirst({
          where: eq(inventory.id, id),
        });

        if (!inventoryItem) {
          throw new HttpError({
            statusCode: 404,
            message: `Inventory item with ID ${id} not found`,
          });
        }

        await db.delete(inventory).where(eq(inventory.id, id));
      });

      return buildJsonResponse({
        c,
        message: 'Inventory item deleted successfully',
      });
    },
  );

export { route };
