import { z<PERSON>alida<PERSON> } from '@hono/zod-validator';
import { eq } from '@muraadso/db';
import { cities } from '@muraadso/db/schema/cities';
import { createCitySchema, updateCitySchema } from '@muraadso/models/cities';
import { Hono } from 'hono';
import { z } from 'zod/v4';
import type { Env } from '../env';
import { HttpError } from '../utils/errors/custom-errors';
import { handleZodError } from '../utils/errors/error-handler';
import { tryCatching } from '../utils/errors/try-catching';
import { buildJsonResponse } from '../utils/response';

const route = new Hono<Env>()
  .get('/', async (c) => {
    const db = c.get('db');
    const result = await tryCatching(async () => db.query.cities.findMany());

    return buildJsonResponse({
      c,
      message: 'Cities retrieved successfully',
      payload: result,
    });
  })
  .post(
    '/',
    zValidator('json', createCitySchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const cityData = c.req.valid('json');

      const result = await tryCatching(async () => {
        const [city] = await db.insert(cities).values(cityData).returning();

        if (!city || !city.id) {
          throw new HttpError({
            statusCode: 500,
            message: "Error! Couldn't create city",
          });
        }

        return city;
      });

      return buildJsonResponse({
        c,
        message: 'City created successfully',
        payload: result,
      });
    },
  )
  .put(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator('json', updateCitySchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');
      const cityData = c.req.valid('json');

      const result = await tryCatching(async () => {
        const existingCity = await db.query.cities.findFirst({
          where: eq(cities.id, id),
        });

        if (!existingCity) {
          throw new HttpError({
            statusCode: 404,
            message: `City with ID ${id} not found`,
          });
        }

        const [city] = await db
          .update(cities)
          .set({
            ...cityData,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(cities.id, id))
          .returning();

        return city;
      });

      return buildJsonResponse({
        c,
        message: 'City updated successfully',
        payload: result,
      });
    },
  )
  .delete(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');

      await tryCatching(async () => {
        const city = await db.query.cities.findFirst({
          where: eq(cities.id, id),
        });

        if (!city) {
          throw new HttpError({
            statusCode: 404,
            message: `City with ID ${id} not found`,
          });
        }

        await db.delete(cities).where(eq(cities.id, id));
      });

      return buildJsonResponse({
        c,
        message: 'City deleted successfully',
      });
    },
  );

export { route };
