import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator';
import { Hono } from 'hono';
import { z } from 'zod/v4';
import type { Env } from '../env';
import { handleZodError } from '../utils/errors/error-handler';
import { tryCatching } from '../utils/errors/try-catching';
import { buildJsonResponse } from '../utils/response';
import { uploadObj } from '../utils/storage';

const route = new Hono<Env>().post(
  '/',
  zValidator(
    'form',
    z.object({
      file: z.instanceof(File, { message: 'File is required.' }),
    }),
    handleZodError,
  ),
  async (c) => {
    const { file } = c.req.valid('form');

    const result = await tryCatching(async () => {
      const fileKey = await uploadObj(c, file);
      return { key: fileKey };
    });

    return buildJsonResponse({
      c,
      message: 'File uploaded successfully',
      payload: result,
    });
  },
);

export { route };
