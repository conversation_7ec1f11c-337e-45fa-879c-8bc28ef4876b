import { z<PERSON>alidator } from '@hono/zod-validator';
import { eq } from '@muraadso/db';
import { countries } from '@muraadso/db/schema/countries';
import {
  createCountrySchema,
  updateCountrySchema,
} from '@muraadso/models/countries';
import { Hono } from 'hono';
import { z } from 'zod/v4';
import type { Env } from '../env';
import { HttpError } from '../utils/errors/custom-errors';
import { handleZodError } from '../utils/errors/error-handler';
import { tryCatching } from '../utils/errors/try-catching';
import { buildJsonResponse } from '../utils/response';

const route = new Hono<Env>()
  .get('/', async (c) => {
    const db = c.get('db');
    const result = await tryCatching(async () => db.query.countries.findMany());

    return buildJsonResponse({
      c,
      message: 'Countries retrieved successfully',
      payload: result,
    });
  })
  .post(
    '/',
    zValidator('json', createCountrySchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const countryData = c.req.valid('json');

      const result = await tryCatching(async () => {
        const [country] = await db
          .insert(countries)
          .values(countryData)
          .returning();

        if (!country || !country.id) {
          throw new HttpError({
            statusCode: 500,
            message: "Error! Couldn't create country",
          });
        }

        return country;
      });

      return buildJsonResponse({
        c,
        message: 'Country created successfully',
        payload: result,
      });
    },
  )
  .put(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator('json', updateCountrySchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');
      const countryData = c.req.valid('json');

      const result = await tryCatching(async () => {
        const existingCountry = await db.query.countries.findFirst({
          where: eq(countries.id, id),
        });

        if (!existingCountry) {
          throw new HttpError({
            statusCode: 404,
            message: `Country with ID ${id} not found`,
          });
        }

        const [country] = await db
          .update(countries)
          .set({
            ...countryData,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(countries.id, id))
          .returning();

        return country;
      });

      return buildJsonResponse({
        c,
        message: 'Country updated successfully',
        payload: result,
      });
    },
  )
  .delete(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');

      await tryCatching(async () => {
        const country = await db.query.countries.findFirst({
          where: eq(countries.id, id),
        });

        if (!country) {
          throw new HttpError({
            statusCode: 404,
            message: `Country with ID ${id} not found`,
          });
        }

        await db.delete(countries).where(eq(countries.id, id));
      });

      return buildJsonResponse({
        c,
        message: 'Country deleted successfully',
      });
    },
  );

export { route };
