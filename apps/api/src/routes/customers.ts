import { zValidator } from '@hono/zod-validator';
import {
  and,
  count,
  eq,
  gte,
  ilike,
  inArray,
  isNotNull,
  lte,
  ne,
  or,
  sql,
} from '@muraadso/db';
import { customers } from '@muraadso/db/schema/customers';
import { orders } from '@muraadso/db/schema/orders';
import { users } from '@muraadso/db/schema/users';
import {
  createCustomerSchema,
  updateCustomerSchema,
} from '@muraadso/models/customers';
import { createUserSchema } from '@muraadso/models/users';
import { Hono } from 'hono';
import { z } from 'zod/v4';
import type { Env } from '../env';
import { HttpError } from '../utils/errors/custom-errors';
import { handleZodError } from '../utils/errors/error-handler';
import { tryCatching } from '../utils/errors/try-catching';
import { buildJsonResponse } from '../utils/response';

const route = new Hono<Env>()
  .get('/stats', async (c) => {
    const db = c.get('db');

    const result = await tryCatching(async () => {
      // Get current date and time periods
      const now = new Date();
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      const twoMonthsAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);

      // Total customers count
      const [totalCustomersResult] = await db
        .select({ count: count() })
        .from(customers);
      const totalCustomers = totalCustomersResult?.count || 0;

      // Customers created this week
      const [customersThisWeekResult] = await db
        .select({ count: count() })
        .from(customers)
        .where(gte(customers.createdAt, oneWeekAgo.toISOString()));
      const customersThisWeek = customersThisWeekResult?.count;

      // Customers with user accounts (registered)
      const [customersWithAccountsResult] = await db
        .select({ count: count() })
        .from(customers)
        .where(isNotNull(customers.userId));
      const customersWithAccounts = customersWithAccountsResult?.count;

      // Customers with accounts created this week
      const [accountsThisWeekResult] = await db
        .select({ count: count() })
        .from(customers)
        .where(
          and(
            isNotNull(customers.userId),
            gte(customers.createdAt, oneWeekAgo.toISOString()),
          ),
        );
      const accountsThisWeek = accountsThisWeekResult?.count || 0;

      // New customers this month
      const [customersThisMonthResult] = await db
        .select({ count: count() })
        .from(customers)
        .where(gte(customers.createdAt, oneMonthAgo.toISOString()));
      const customersThisMonth = customersThisMonthResult?.count || 0;

      // New customers previous month for comparison
      const [customersPreviousMonthResult] = await db
        .select({ count: count() })
        .from(customers)
        .where(
          and(
            gte(customers.createdAt, twoMonthsAgo.toISOString()),
            lte(customers.createdAt, oneMonthAgo.toISOString()),
          ),
        );
      const customersPreviousMonth = customersPreviousMonthResult?.count || 0;

      // Calculate month-over-month percentage change
      const monthlyGrowthPercentage =
        customersPreviousMonth > 0
          ? ((customersThisMonth - customersPreviousMonth) /
              customersPreviousMonth) *
            100
          : 0;

      // Customers with active orders (pending or confirmed)
      const [customersWithActiveOrdersResult] = await db
        .select({
          count: sql<number>`COUNT(DISTINCT ${orders.customerId})`.as('count'),
        })
        .from(orders)
        .where(inArray(orders.status, ['pending', 'confirmed']));
      const customersWithActiveOrders =
        customersWithActiveOrdersResult?.count || 0;

      // Customer lifetime value analysis
      const customerOrderStats = await db
        .select({
          customerId: orders.customerId,
          totalSpent: sql<number>`SUM(${orders.totalAmount})`.as('totalSpent'),
          orderCount: count(orders.id),
        })
        .from(orders)
        .groupBy(orders.customerId);

      // Calculate average customer lifetime value
      const totalCustomerValue = customerOrderStats.reduce(
        (sum, c) => sum + c.totalSpent,
        0,
      );
      const avgCustomerLifetimeValue =
        customerOrderStats.length > 0
          ? totalCustomerValue / customerOrderStats.length
          : 0;

      // Find top spending customers
      const topCustomers = customerOrderStats
        .sort((a, b) => b.totalSpent - a.totalSpent)
        .slice(0, 5);

      // Repeat customers (customers with more than 1 order)
      const repeatCustomersCount = customerOrderStats.filter(
        (c) => c.orderCount > 1,
      ).length;
      const repeatCustomerRate =
        totalCustomers > 0 ? (repeatCustomersCount / totalCustomers) * 100 : 0;

      return {
        totalCustomers,
        customersThisWeek,
        customersWithAccounts,
        accountsThisWeek,
        customersThisMonth,
        monthlyGrowthPercentage,
        customersWithActiveOrders,
        avgCustomerLifetimeValue,
        repeatCustomersCount,
        repeatCustomerRate,
        topCustomers: topCustomers.map((c) => ({
          customerId: c.customerId,
          totalSpent: c.totalSpent,
          orderCount: c.orderCount,
        })),
      };
    });

    return buildJsonResponse({
      c,
      message: 'Customer stats retrieved successfully',
      payload: result,
    });
  })
  .get(
    '/',
    zValidator(
      'query',
      z.object({
        q: z.string().optional(),
        page: z.coerce.number().positive().default(1),
        pageSize: z.coerce.number().positive().max(100).default(15),
        startDate: z.string().optional(),
        endDate: z.string().optional(),
        hasUser: z.coerce.boolean().optional(),
        sortOrder: z.enum(['asc', 'desc']).default('desc'),
        // Keep legacy limit parameter for backward compatibility
        limit: z.coerce.number().positive().optional(),
      }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const {
        q,
        page,
        pageSize,
        startDate,
        endDate,
        hasUser,
        sortOrder,
        limit,
      } = c.req.valid('query');

      const result = await tryCatching(async () => {
        // Use legacy behavior if limit is provided (for backward compatibility)
        if (limit) {
          const customersList = await db.query.customers.findMany({
            with: {
              user: true,
            },
            where: or(
              q
                ? ilike(customers.name, `%${q.toLocaleLowerCase()}%`)
                : undefined,
              q
                ? ilike(customers.email, `%${q.toLocaleLowerCase()}%`)
                : undefined,
              q
                ? ilike(customers.phone, `%${q.toLocaleLowerCase()}%`)
                : undefined,
            ),
            orderBy: (customers, { desc }) => [desc(customers.createdAt)],
            limit,
          });

          // Transform the response to match expected format
          return customersList.map((customer) => {
            const userRelation = customer.user;
            return {
              ...customer,
              user:
                userRelation === undefined
                  ? null
                  : Array.isArray(userRelation) && userRelation.length > 0
                    ? userRelation[0]
                    : null,
            };
          });
        }

        // Build where conditions
        const whereConditions = [];

        // Search conditions
        if (q) {
          whereConditions.push(
            or(
              ilike(customers.name, `%${q.toLocaleLowerCase()}%`),
              ilike(customers.email, `%${q.toLocaleLowerCase()}%`),
              ilike(customers.phone, `%${q.toLocaleLowerCase()}%`),
            ),
          );
        }

        // Date range filter
        if (startDate) {
          whereConditions.push(gte(customers.createdAt, startDate));
        }
        if (endDate) {
          whereConditions.push(lte(customers.createdAt, endDate));
        }

        // User account filter
        // if (hasUser !== undefined) {
        //   if (hasUser) {
        //     whereConditions.push(ne(customers.userId, null));
        //   } else {
        //     whereConditions.push(eq(customers.userId, null));
        //   }
        // }

        const whereClause =
          whereConditions.length > 0 ? and(...whereConditions) : undefined;

        // Get total count for pagination
        const totalCountResult = await db
          .select({ count: sql`count(*)` })
          .from(customers)
          .where(whereClause);

        const totalCount = Number(totalCountResult[0]?.count || 0);
        const totalPages = Math.ceil(totalCount / pageSize);
        const offset = (page - 1) * pageSize;

        // Get customers with pagination
        const customersList = await db.query.customers.findMany({
          with: { user: true },
          where: whereClause,
          orderBy: (customers, { desc, asc }) => [
            sortOrder === 'desc'
              ? desc(customers.createdAt)
              : asc(customers.createdAt),
          ],
          limit: pageSize,
          offset,
        });

        // Transform the response to match expected format
        const transformedCustomers = customersList.map((customer) => {
          const userRelation = customer?.user;
          return {
            ...customer,
            user:
              userRelation === undefined
                ? null
                : Array.isArray(userRelation) && userRelation.length > 0
                  ? userRelation[0]
                  : null,
          };
        });

        return {
          customers: transformedCustomers,
          pagination: {
            page,
            pageSize,
            totalCount,
            totalPages,
            hasNextPage: page < totalPages,
            hasPreviousPage: page > 1,
          },
        };
      });

      return buildJsonResponse({
        c,
        message: 'Customers retrieved successfully',
        payload: result,
      });
    },
  )
  .get(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');

      const result = await tryCatching(async () => {
        const customer = await db.query.customers.findFirst({
          where: (customers, { eq }) => eq(customers.id, id),
          with: { user: true },
        });

        if (!customer) {
          throw new HttpError({
            statusCode: 404,
            message: `Customer with ID ${id} not found`,
          });
        }

        // Transform the response to match expected format
        const userRelation = customer?.user;
        return {
          ...customer,
          user:
            userRelation === undefined
              ? null
              : Array.isArray(userRelation) && userRelation.length > 0
                ? userRelation[0]
                : null,
        };
      });

      return buildJsonResponse({
        c,
        message: 'Customer retrieved successfully',
        payload: result,
      });
    },
  )
  .post(
    '/',
    zValidator(
      'json',
      z.object({
        user: createUserSchema.optional(),
        customer: createCustomerSchema,
      }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const { user: userData, customer: customerData } = c.req.valid('json');

      const result = await tryCatching(async () => {
        return db.transaction(async (tx) => {
          let user = null;

          // Create user if user data is provided
          if (userData && (userData.email || userData.phone)) {
            [user] = await tx.insert(users).values(userData).returning();
          }

          const [customer] = await tx
            .insert(customers)
            .values({
              ...customerData,
              userId: user?.id || null,
            })
            .returning();

          // Fetch the complete customer with relations
          const completeCustomer = await tx.query.customers.findFirst({
            where: eq(customers.id, customer!.id),
            with: { user: true },
          });

          // Transform the response to match expected format
          // Drizzle returns user as array format [user_data] or undefined
          const userRelation = completeCustomer?.user;
          return {
            ...completeCustomer!,
            user:
              userRelation === undefined
                ? null
                : Array.isArray(userRelation) && userRelation.length > 0
                  ? userRelation[0]
                  : null,
          };
        });
      });

      return buildJsonResponse({
        c,
        message: 'Customer created successfully',
        payload: result,
      });
    },
  )
  .put(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator('json', updateCustomerSchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');
      const customerData = c.req.valid('json');

      const result = await tryCatching(async () => {
        const existingCustomer = await db.query.customers.findFirst({
          where: eq(customers.id, id),
        });

        if (!existingCustomer) {
          throw new HttpError({
            statusCode: 404,
            message: `Customer with ID ${id} not found`,
          });
        }

        const [customer] = await db
          .update(customers)
          .set({
            ...customerData,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(customers.id, id))
          .returning();

        // Fetch the complete customer with relations
        const completeCustomer = await db.query.customers.findFirst({
          where: eq(customers.id, customer!.id),
          with: { user: true },
        });

        // Transform the response to match expected format
        const userRelation = completeCustomer?.user;
        return {
          ...completeCustomer!,
          user:
            userRelation === undefined
              ? null
              : Array.isArray(userRelation) && userRelation.length > 0
                ? userRelation[0]
                : null,
        };
      });

      return buildJsonResponse({
        c,
        message: 'Customer updated successfully',
        payload: result,
      });
    },
  )
  .delete(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');

      await tryCatching(async () => {
        const customer = await db.query.customers.findFirst({
          where: eq(customers.id, id),
        });

        if (!customer) {
          throw new HttpError({
            statusCode: 404,
            message: `Customer with ID ${id} not found`,
          });
        }

        // Only delete the customer, not the user
        // The user might have other data or be used elsewhere
        await db.delete(customers).where(eq(customers.id, id));
      });

      return buildJsonResponse({
        c,
        message: 'Customer deleted successfully',
      });
    },
  );

export { route };
