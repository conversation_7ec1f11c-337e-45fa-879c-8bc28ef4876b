import { zValidator } from '@hono/zod-validator';
import { count, eq, sql, sum } from '@muraadso/db';
import { categories } from '@muraadso/db/schema/categories';
import { customers } from '@muraadso/db/schema/customers';
import { orderItems, orders } from '@muraadso/db/schema/orders';
import { products } from '@muraadso/db/schema/products';
import { variants } from '@muraadso/db/schema/variants';
import { Hono } from 'hono';
import { z } from 'zod/v4';
import type { Env } from '../env';
import { handleZodError } from '../utils/errors/error-handler';
import { tryCatching } from '../utils/errors/try-catching';
import { buildJsonResponse } from '../utils/response';

const route = new Hono<Env>()
  .get('/stats', async (c) => {
    const db = c.get('db');

    const result = await tryCatching(async () => {
      const [totalSalesResult] = await db
        .select({
          totalAmount: sum(orders.totalAmount),
        })
        .from(orders);
      const totalSales = Number.parseFloat(
        totalSalesResult?.totalAmount || '0',
      );

      const [totalOrdersResult] = await db
        .select({
          totalOrders: count(orders.id),
        })
        .from(orders);
      const totalOrders = totalOrdersResult?.totalOrders || 0;

      const [totalProductsResult] = await db
        .select({
          totalProducts: count(products.id),
        })
        .from(products);
      const totalProducts = totalProductsResult?.totalProducts || 0;

      const [totalCustomersResult] = await db
        .select({
          totalCustomers: count(customers.id),
        })
        .from(customers);
      const totalCustomers = totalCustomersResult?.totalCustomers || 0;

      return {
        totalSales,
        totalOrders,
        totalProducts,
        totalCustomers,
      };
    });

    return buildJsonResponse({
      c,
      message: 'Dashboard stats retrieved successfully',
      payload: result,
    });
  })
  .get(
    '/sales',
    zValidator(
      'query',
      z.object({
        months: z.coerce.number().positive().default(6),
      }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const { months } = c.req.valid('query');

      const result = await tryCatching(async () => {
        const startDate = new Date();
        startDate.setMonth(startDate.getMonth() - months);
        startDate.setDate(1); // Start from the 1st day of the month
        startDate.setHours(0, 0, 0, 0);

        const monthlySales = await db
          .select({
            month: sql`DATE_TRUNC('month', ${orders.createdAt})`.as('month'),
            sales: sum(orders.totalAmount).as('sales'),
          })
          .from(orders)
          .where(
            // sql`${orders.createdAt} >= ${startDate.toISOString()} AND ${orders.status} != 'cancelled'`
            sql`${orders.createdAt} >= ${startDate.toISOString()}`,
          )
          .groupBy(sql`DATE_TRUNC('month', ${orders.createdAt})`)
          .orderBy(sql`month ASC`);
        console.log(monthlySales);

        return monthlySales.map((row) => {
          const date = new Date(row.month as string);
          const monthName = date.toLocaleString('default', { month: 'long' });
          return {
            month: monthName,
            sales: Number.parseFloat(row.sales || '0'),
          };
        });
      });

      return buildJsonResponse({
        c,
        message: 'Sales data retrieved successfully',
        payload: result,
      });
    },
  )
  .get(
    '/customer-growth',
    zValidator(
      'query',
      z.object({
        months: z.coerce.number().positive().default(6),
      }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const { months } = c.req.valid('query');

      const result = await tryCatching(async () => {
        // Get the start date (X months ago)
        const startDate = new Date();
        startDate.setMonth(startDate.getMonth() - months);
        startDate.setDate(1); // Start from the 1st day of the month
        startDate.setHours(0, 0, 0, 0);

        const newCustomers = await db
          .select({
            month: sql`DATE_TRUNC('month', ${customers.createdAt})`.as('month'),
            new: count(customers.id).as('new'),
          })
          .from(customers)
          .where(sql`${customers.createdAt} >= ${startDate.toISOString()}`)
          .groupBy(sql`DATE_TRUNC('month', ${customers.createdAt})`)
          .orderBy(sql`month ASC`);

        const returningCustomers = await db
          .select({
            month: sql`month`.as('month'),
            returning: count(sql`customer_id`).as('returning'),
          })
          .from(
            sql`
              (
                SELECT
                  ${orders.customerId} AS customer_id,
                  DATE_TRUNC('month', ${orders.createdAt}) AS month,
                  COUNT(${orders.id}) AS order_count
                FROM ${orders}
                WHERE ${orders.createdAt} >= ${startDate.toISOString()}
                GROUP BY ${orders.customerId}, DATE_TRUNC('month', ${orders.createdAt})
              ) AS monthly_orders
            `,
          )
          .where(sql`order_count > 1`)
          .groupBy(sql`month`)
          .orderBy(sql`month ASC`);

        const monthsMap = new Map();

        for (let i = 0; i < months; i++) {
          const date = new Date();
          date.setMonth(date.getMonth() - i);
          date.setDate(1);
          date.setHours(0, 0, 0, 0);

          const monthKey = date.toISOString().substring(0, 7); // YYYY-MM format
          const monthName = date.toLocaleString('default', { month: 'long' });

          monthsMap.set(monthKey, {
            month: monthName,
            new: 0,
            returning: 0,
          });
        }

        for (const { month, new: list } of newCustomers) {
          const date = new Date(month as string);
          const monthKey = date.toISOString().substring(0, 7);
          if (monthsMap.has(monthKey)) {
            const data = monthsMap.get(monthKey);
            data.new = list;
            monthsMap.set(monthKey, data);
          }
        }

        for (const { month, returning } of returningCustomers) {
          const date = new Date(month as string);
          const monthKey = date.toISOString().substring(0, 7);
          if (monthsMap.has(monthKey)) {
            const data = monthsMap.get(monthKey);
            data.returning = returning;
            monthsMap.set(monthKey, data);
          }
        }

        return Array.from(monthsMap.values()).reverse();
      });

      return buildJsonResponse({
        c,
        message: 'Customer growth data retrieved successfully',
        payload: result,
      });
    },
  )
  .get('/product-categories', async (c) => {
    const db = c.get('db');

    const result = await tryCatching(async () => {
      return await db
        .select({
          name: categories.name,
          value: count(products.id).as('value'),
        })
        .from(products)
        .leftJoin(categories, eq(products.categoryId, categories.id))
        .groupBy(categories.id, categories.name)
        .orderBy(sql`value DESC`);
    });

    return buildJsonResponse({
      c,
      message: 'Product category distribution retrieved successfully',
      payload: result,
    });
  })

  .get('/order-status', async (c) => {
    const db = c.get('db');
    const result = await tryCatching(async () => {
      return await db
        .select({
          name: sql`CONCAT
          ('Payment: ',
          ${orders.status}
          )`.as('name'),
          value: count(orders.id).as('value'),
        })
        .from(orders)
        .groupBy(orders.status)
        .orderBy(sql`value DESC`);
    });

    return buildJsonResponse({
      c,
      message: 'Order status distribution retrieved successfully',
      payload: result,
    });
  })
  .get(
    '/top-products',
    zValidator(
      'query',
      z.object({
        limit: z.coerce.number().positive().default(5),
      }),
      handleZodError,
    ),
    async (c) => {
      const db = c.get('db');
      const { limit } = c.req.valid('query');
      const result = await tryCatching(async () => {
        return await db
          .select({
            name: products.name,
            sales: sum(orderItems.quantity).as('sales'),
          })
          .from(orderItems)
          .innerJoin(variants, eq(orderItems.variantId, variants.id))
          .innerJoin(products, eq(variants.productId, products.id))
          .innerJoin(orders, eq(orderItems.orderId, orders.id))
          .where(sql`${orders.status} != 'canceled'`)
          .groupBy(products.id, products.name)
          .orderBy(sql`sales DESC`)
          .limit(limit);
      });

      return buildJsonResponse({
        c,
        message: 'Top selling products retrieved successfully',
        payload: result,
      });
    },
  );

export { route };
