import { z<PERSON>alidator } from '@hono/zod-validator';
import { eq } from '@muraadso/db';
import { districts } from '@muraadso/db/schema/districts';
import {
  createDistrictSchema,
  updateDistrictSchema,
} from '@muraadso/models/districts';
import { Hono } from 'hono';
import { z } from 'zod/v4';
import type { Env } from '../env';
import { HttpError } from '../utils/errors/custom-errors';
import { handleZodError } from '../utils/errors/error-handler';
import { tryCatching } from '../utils/errors/try-catching';
import { buildJsonResponse } from '../utils/response';

const route = new Hono<Env>()
  .get('/', async (c) => {
    const db = c.get('db');
    const result = await tryCatching(async () => db.query.districts.findMany());

    return buildJsonResponse({
      c,
      message: 'Districts retrieved successfully',
      payload: result,
    });
  })
  .post(
    '/',
    zValidator('json', createDistrictSchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const districtData = c.req.valid('json');

      const result = await tryCatching(async () => {
        const [district] = await db
          .insert(districts)
          .values(districtData)
          .returning();

        if (!district || !district.id) {
          throw new HttpError({
            statusCode: 500,
            message: "Error! Couldn't create district",
          });
        }

        return district;
      });

      return buildJsonResponse({
        c,
        message: 'District created successfully',
        payload: result,
      });
    },
  )
  .put(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator('json', updateDistrictSchema, handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');
      const districtData = c.req.valid('json');

      const result = await tryCatching(async () => {
        const existingDistrict = await db.query.districts.findFirst({
          where: eq(districts.id, id),
        });

        if (!existingDistrict) {
          throw new HttpError({
            statusCode: 404,
            message: `District with ID ${id} not found`,
          });
        }

        const [district] = await db
          .update(districts)
          .set({
            ...districtData,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(districts.id, id))
          .returning();

        return district;
      });

      return buildJsonResponse({
        c,
        message: 'District updated successfully',
        payload: result,
      });
    },
  )
  .delete(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      const db = c.get('db');
      const { id } = c.req.valid('param');

      await tryCatching(async () => {
        const district = await db.query.districts.findFirst({
          where: eq(districts.id, id),
        });

        if (!district) {
          throw new HttpError({
            statusCode: 404,
            message: `District with ID ${id} not found`,
          });
        }

        await db.delete(districts).where(eq(districts.id, id));
      });

      return buildJsonResponse({
        c,
        message: 'District deleted successfully',
      });
    },
  );

export { route };
