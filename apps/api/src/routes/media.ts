import { Hono } from 'hono';
import type { Env } from '../env';
import { HttpError } from '../utils/errors/custom-errors';
import { tryCatching } from '../utils/errors/try-catching';

const route = new Hono<Env>().get('/:key', async (c) => {
  const key = c.req.param('key');
  return tryCatching(async () => {
    const file = await c.env.R2_BUCKET.get(key);
    if (!file) {
      throw new HttpError({
        statusCode: 404,
        message: 'File not found',
      });
    }

    // Set appropriate headers
    const headers = new Headers();
    headers.set(
      'Content-Type',
      file.httpMetadata?.contentType || 'application/octet-stream',
    );
    headers.set('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year

    return new Response(file.body as BodyInit, {
      headers,
    });
  });
});

export { route };
