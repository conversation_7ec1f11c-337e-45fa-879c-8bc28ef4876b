import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator';
import { eq } from '@muraadso/db';
import { sysops } from '@muraadso/db/schema/sysops';
import { createSysopSchema, updateSysopSchema } from '@muraadso/models/sysops';
import { Hono } from 'hono';
import { z } from 'zod/v4';
import type { Env } from '../env';
import { requireAuth } from '../middlewares/auth';
import { HttpError } from '../utils/errors/custom-errors';
import { handleZodError } from '../utils/errors/error-handler';
import { tryCatching } from '../utils/errors/try-catching';
import { buildJsonResponse } from '../utils/response';

const route = new Hono<Env>()
  .get('/me', requireAuth, async (c) => {
    return tryCatching(async () => {
      const db = c.get('db');
      const currentUser = c.get('user');

      const result = await db.query.sysops.findFirst({
        with: {
          user: true,
          location: true,
        },
        where: eq(sysops.userId, currentUser.id),
      });

      if (!result) {
        throw new HttpError({
          statusCode: 404,
          message: 'Sysop record not found for current user',
        });
      }

      return buildJsonResponse({
        c,
        message: 'Current user sysop retrieved successfully',
        payload: result,
      });
    });
  })
  .get('/', requireAuth, async (c) => {
    return tryCatching(async () => {
      const db = c.get('db');
      const result = await db.query.sysops.findMany({
        with: {
          user: true,
          location: true,
        },
        orderBy: sysops.createdAt,
      });
      return buildJsonResponse({
        c,
        message: 'Sysops retrieved successfully',
        payload: result,
      });
    });
  })
  .get(
    '/:id',
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      return tryCatching(async () => {
        const db = c.get('db');
        const { id } = c.req.valid('param');

        const result = await db.query.sysops.findFirst({
          with: {
            user: true,
            location: true,
          },
          where: eq(sysops.id, id),
        });

        if (!result) {
          throw new HttpError({
            statusCode: 404,
            message: `Sysop with ID ${id} not found`,
          });
        }

        return buildJsonResponse({
          c,
          message: 'Sysop retrieved successfully',
          payload: result,
        });
      });
    },
  )
  .post(
    '/',
    requireAuth,
    zValidator('json', createSysopSchema, handleZodError),
    async (c) => {
      return tryCatching(async () => {
        const db = c.get('db');
        const sysopData = c.req.valid('json');

        const result = await db.transaction(async (tx) => {
          // Create the sysop
          const [sysop] = await tx.insert(sysops).values(sysopData).returning();

          if (!sysop || !sysop.id) {
            throw new HttpError({
              statusCode: 500,
              message: "Error! Couldn't create sysop",
            });
          }

          // Fetch the complete sysop with relations
          return await tx.query.sysops.findFirst({
            with: {
              user: true,
              location: true,
            },
            where: eq(sysops.id, sysop.id),
          });
        });

        return buildJsonResponse({
          c,
          message: 'Sysop created successfully',
          payload: result,
        });
      });
    },
  )
  .put(
    '/:id',
    requireAuth,
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    zValidator('json', updateSysopSchema, handleZodError),
    async (c) => {
      return tryCatching(async () => {
        const db = c.get('db');
        const { id } = c.req.valid('param');
        const sysopData = c.req.valid('json');

        const result = await db.transaction(async (tx) => {
          const existingSysop = await tx.query.sysops.findFirst({
            where: eq(sysops.id, id),
          });

          if (!existingSysop) {
            throw new HttpError({
              statusCode: 404,
              message: `Sysop with ID ${id} not found`,
            });
          }

          const [sysop] = await tx
            .update(sysops)
            .set({
              ...sysopData,
              updatedAt: new Date().toISOString(),
            })
            .where(eq(sysops.id, id))
            .returning();

          // Fetch the complete sysop with relations
          const completeSysop = await tx.query.sysops.findFirst({
            with: {
              user: true,
              location: true,
            },
            where: eq(sysops.id, id),
          });

          return completeSysop;
        });

        return buildJsonResponse({
          c,
          message: 'Sysop updated successfully',
          payload: result,
        });
      });
    },
  )
  .delete(
    '/:id',
    requireAuth,
    zValidator('param', z.object({ id: z.uuid() }), handleZodError),
    async (c) => {
      return tryCatching(async () => {
        const db = c.get('db');
        const { id } = c.req.valid('param');

        const sysop = await db.query.sysops.findFirst({
          where: eq(sysops.id, id),
        });

        if (!sysop) {
          throw new HttpError({
            statusCode: 404,
            message: `Sysop with ID ${id} not found`,
          });
        }

        await db.delete(sysops).where(eq(sysops.id, id));

        return buildJsonResponse({
          c,
          message: 'Sysop deleted successfully',
        });
      });
    },
  );

export { route };
