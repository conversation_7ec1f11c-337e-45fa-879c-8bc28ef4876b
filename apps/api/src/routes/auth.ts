import { zValidator } from '@hono/zod-validator';
import { and, eq } from '@muraadso/db';
import { sessions } from '@muraadso/db/schema/sessions';
import { users } from '@muraadso/db/schema/users';
import { lower } from '@muraadso/db/utils';
import type { User } from '@muraadso/models/users';
import { type Context, Hono } from 'hono';
import { getConnInfo } from 'hono/cloudflare-workers';
import { z } from 'zod';
import type { Env } from '../env';
import { requireAuth } from '../middlewares/auth';
import { generateRandomToken } from '../utils/crypto';
import { ErrorCode, HttpStatusCode } from '../utils/errors/constants';
import { HttpError } from '../utils/errors/custom-errors';
import { tryCatching } from '../utils/errors/try-catching';
import { hashPassword, verifyPassword } from '../utils/password';
import { buildJsonResponse } from '../utils/response';
import { sanitizeUser } from '../utils/user';

const SESSION_TOKEN_BYTES = 32;
// const SESSION_EXPIRATION = 15 * 60; // 15 minutes
const SESSION_EXPIRATION = 60 * 60; // 60 minutes

const route = new Hono<Env>()
  .post(
    '/sign-in',
    zValidator(
      'json',
      z.object({
        email: z
          .string({
            message: 'Email is required!',
          })
          .email({
            message: 'Invalid email address!',
          }),
        password: z.string({ message: 'Password is required!' }).nonempty({
          message: 'Password cannot be empty!',
        }),
      }),
    ),
    async (c) => {
      return tryCatching(async () => {
        const db = c.get('db');
        const { email, password } = c.req.valid('json');

        const user = await db.query.users.findFirst({
          // with: {
          //   userRoles: {
          //     columns: {},
          //     with: {
          //       role: true,
          //     },
          //   },
          // },
          where: eq(lower(users.email), email.toLowerCase()),
        });

        if (
          !user ||
          !user.password ||
          !(await verifyPassword(password, user.password))
        ) {
          throw new HttpError({
            message: 'Incorrect email or password',
            statusCode: HttpStatusCode.UNAUTHORIZED,
            code: ErrorCode.INVALID_EMAIL_OR_PASSWORD,
          });
        }

        const session = await generateSession(c, 'sign_in', user);

        return buildJsonResponse({
          c,
          payload: { session },
        });
      });
    },
  )
  .post(
    '/verify',
    zValidator(
      'json',
      z.object({
        type: z.string(),
        token: z.string(),
      }),
    ),
    async (c) => {
      return tryCatching(async () => {
        const db = c.get('db');
        const { type, token } = c.req.valid('json');
        if (type === 'invite') {
          const user = await db.query.users.findFirst({
            // with: {
            //   userRoles: {
            //     columns: {},
            //     with: {
            //       role: true,
            //     },
            //   },
            // },
            where: and(eq(users.verificationToken, token)),
          });

          if (!user) {
            throw new HttpError({
              statusCode: 400,
              message: "Token has been expired or it's invalid",
            });
          }

          const session = await generateSession(c, 'verify_email', user);
          return buildJsonResponse({
            c,
            payload: { session },
          });
        }

        throw new HttpError({
          statusCode: 400,
          message: 'Unknown token type',
        });
      });
    },
  )
  .post(
    '/change-password',
    requireAuth,
    zValidator(
      'json',
      z
        .object({
          password: z
            .string()
            .nonempty({ message: 'Password cannot be empty!' }),
          confirm: z
            .string()
            .nonempty({ message: 'Password cannot be empty!' }),
        })
        .refine((data) => data.password === data.confirm, {
          message: "Passwords don't match",
          path: ['confirm'],
        }),
    ),
    async (c) => {
      return tryCatching(async () => {
        const db = c.get('db');
        const { password } = c.req.valid('json');
        const user = c.get('user');
        const hashedPassword = await hashPassword(password);
        await db
          .update(users)
          .set({ password: hashedPassword })
          .where(eq(users.id, user.id));

        return buildJsonResponse({
          c,
          message: 'Successfully changed password!',
        });
      });
    },
  );

type SessionMode = 'verify_email' | 'reset_password' | 'sign_in';

const generateSession = async (
  c: Context<Env>,
  mode: SessionMode,
  user: User & {
    // userRoles: {
    //   role: Role;
    // }[];
  },
): Promise<string> => {
  const db = c.get('db');

  // skip email verification requirement
  // if user is already verifying their email
  if (mode !== 'verify_email') {
    if (!user.emailVerifiedAt) {
      throw new HttpError({
        message:
          'Your account is pending verification. Please check your email to complete the process',
        statusCode: HttpStatusCode.UNAUTHORIZED,
        code: ErrorCode.AUTHENTICATION_FAILED,
      });
    }
  }

  if (user.isBanned) {
    throw new HttpError({
      message: 'Account banned. Please contact support for assistance',
      statusCode: HttpStatusCode.UNAUTHORIZED,
      code: ErrorCode.AUTHENTICATION_FAILED,
    });
  }

  const token = await generateRandomToken(SESSION_TOKEN_BYTES);
  const expiresAt = new Date(Date.now() + 1000 * SESSION_EXPIRATION);

  return db.transaction(async (tx) => {
    await tx.insert(sessions).values({
      token,
      expiresAt,
      ipAddress: getConnInfo(c)?.remote?.address,
      userAgent: c.req.header('User-Agent'),
      userId: user.id,
    });

    await tx
      .update(users)
      .set({
        lastSignInAt: new Date().toISOString(),
      })
      .where(eq(users.id, user.id));

    return btoa(
      JSON.stringify({
        token,
        expiresAt,
        user: {
          ...sanitizeUser(user),
          // roles: user.userRoles.map((it) => it.role),
        },
      }),
    );
  });
};

export { route };
