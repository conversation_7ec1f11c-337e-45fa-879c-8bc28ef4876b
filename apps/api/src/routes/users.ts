import { zValidator } from '@hono/zod-validator';
import { eq } from '@muraadso/db';
import { sysops } from '@muraadso/db/schema/sysops';
import { users } from '@muraadso/db/schema/users';
import { type Context, Hono } from 'hono';
import z from 'zod/v4';
import type { Env } from '../env';
import { requireAuth } from '../middlewares/auth';
import { generateRandomToken } from '../utils/crypto';
import { HttpError } from '../utils/errors/custom-errors';
import { tryCatching } from '../utils/errors/try-catching';
import { buildJsonResponse } from '../utils/response';
import { sanitizeUser } from '../utils/user';

const route = new Hono<Env>()
  .get('/', async (c) => {
    return tryCatching(async () => {
      const db = c.get('db');
      const result = await db.query.users.findMany({
        // with: {
        //   userRoles: {
        //     columns: {},
        //     with: {
        //       role: true,
        //     },
        //   },
        // },
        orderBy: users.createdAt,
      });
      return buildJsonResponse({
        c,
        payload: result.map((it) => ({
          ...sanitizeUser(it),
          // roles: it.userRoles.map((it) => it.role),
        })),
      });
    });
  })
  .delete(
    '/:id',
    zValidator('param', z.object({ id: z.string().uuid() })),
    async (c) => {
      return tryCatching(async () => {
        const db = c.get('db');
        const { id } = c.req.valid('param');
        await db.delete(users).where(eq(users.id, id));
        return buildJsonResponse({ c });
      });
    },
  )
  .get('/me', requireAuth, async (c: Context<Env>) => {
    return tryCatching(async () => {
      const currenUser = c.get('user');
      return buildJsonResponse({ c, payload: sanitizeUser(currenUser) });
    });
  })
  .post(
    '/invite',
    zValidator(
      'json',
      z.object({
        email: z.string().email(),
        name: z.string().min(1, 'Name is required'),
        locationId: z.string().uuid().optional(),
        isSuperAdmin: z.boolean().default(false),
      }),
    ),
    async (c) => {
      return tryCatching(async () => {
        const db = c.get('db');
        const { email, name, locationId, isSuperAdmin } = c.req.valid('json');

        // Check if user already exists
        const existingUser = await db.query.users.findFirst({
          where: eq(users.email, email),
        });

        if (existingUser) {
          throw new HttpError({
            statusCode: 400,
            message: 'User with this email already exists',
          });
        }

        // If not superadmin, locationId is required
        if (!isSuperAdmin && !locationId) {
          throw new HttpError({
            statusCode: 400,
            message: 'Location is required for non-superadmin sysops',
          });
        }

        const token = await generateRandomToken(32);

        const result = await db.transaction(async (tx) => {
          // Create user
          const [user] = await tx
            .insert(users)
            .values({
              email,
              verificationToken: token,
              verificationSentAt: new Date(),
            })
            .returning();

          if (!user || !user.id) {
            throw new HttpError({
              statusCode: 500,
              message: 'Failed to create user',
            });
          }

          const [sysop] = await tx
            .insert(sysops)
            .values({
              name,
              userId: user.id,
              locationId: isSuperAdmin ? null : locationId,
              isSuperAdmin,
            })
            .returning();

          return { user: sanitizeUser(user), sysop };
        });

        // TODO: Send invitation email
        // const origin = c.req.header('Origin');
        // const link = origin + '/invite?token=' + token;
        // Send email with link

        return buildJsonResponse({
          c,
          message: 'User invited successfully',
          payload: result,
        });
      });
    },
  );
// .post(
//   '/invite',
//   requireAuth,
//   zValidator(
//     'json',
//     z.object({
//       email: z.string().email(),
//       role: z.string().uuid(),
//     }),
//   ),
//   async (c) => {
//     const origin = c.req.header('Origin');
//     return tryCatching(async () => {
//       const db = c.get('db')
//       const { email, role: roleId } = c.req.valid('json');
//
//       const role = await db.query.roles.findFirst({
//         where: eq(roles.id, roleId),
//       });
//
//       if (!role)
//         throw new HttpError({
//           statusCode: 404,
//           message: 'Specified role not found!',
//         });
//
//       const inviter = c.get('user');
//       const token = await generateRandomToken(32);
//
//       const from = `Muraadso <${c.env.SMTP_FROM}>`;
//       const to = [email];
//       const subject = inviter.name + ' has invited you to join Muraadso';
//       const link = origin + '/invite?token=' + token;
//
//       const [user] = await db
//         .insert(sysops)
//         .values({
//           email: email,
//           verificationToken: token,
//           verificationSentAt: new Date(),
//           isSysop: true,
//         })
//         .returning();
//
//       await db.insert(userRoles).values({
//         userId: user.id,
//         roleId: role.id,
//       });
//

//
//       return buildJsonResponse({
//         c,
//         message: 'Invitation sent successfully!',
//         payload: {
//           user: {
//             ...sanitizeUser(user),
//             roles: [role],
//           },
//         },
//       });
//     });
//   },
// );

export { route };
