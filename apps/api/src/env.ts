import type { R2Bucket } from '@cloudflare/workers-types';
import type { DB } from '@muraadso/db';
import type { Session } from '@muraadso/models/sessions';
import type { SafeUser } from '@muraadso/models/users';
import type { RequestIdVariables } from 'hono/request-id';

export type Env = {
  Variables: RequestIdVariables & {
    db: DB;
    user: SafeUser;
    session: Session;
  };
  Bindings: {
    NODE_ENV: string;
    DATABASE_URL: string;
    CORS_ORIGINS: string;
    R2_BUCKET: R2Bucket;
    R2_PUBLIC_URL: string;
    CF_ACCOUNT_ID: string;
    CF_ACCESS_KEY_ID: string;
    CF_SECRET_ACCESS_KEY: string;
  };
};
