import { createDb } from '@muraadso/db';
import { Hono } from 'hono';
import { contextStorage } from 'hono/context-storage';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { requestId } from 'hono/request-id';
import { timing } from 'hono/timing';
import type { Env } from './env';
import { routes } from './routes/routes';
import { HttpError } from './utils/errors/custom-errors';
import { errorHandler } from './utils/errors/error-handler';
import { buildJsonResponse } from './utils/response';

const app = new Hono<Env>()
  .use(
    '*',
    cors({
      origin: [
        'http://localhost:5521',
        'http://localhost:7721',
        'https://dashboard.muraadsoict.workers.dev',
        'https://staging.dashboard.muraadso.com',
      ],
      allowMethods: ['GET', 'POST', 'PUT', 'DELETE'],
      credentials: true,
      maxAge: 86400,
    }),
  )
  .use('*', timing())
  .use('*', logger())
  .use('*', requestId())
  .use('*', prettyJSON())
  .use('*', contextStorage())
  .use('*', (c, next) => {
    const env = c.env.NODE_ENV;
    const url = c.env.DATABASE_URL;
    c.set('db', createDb(url, env));
    return next();
  })
  .get('/health', async (c) => {
    return buildJsonResponse({
      c,
      status: 200,
      message: 'OK!',
    });
  })
  .route('/v1', routes)
  .notFound((c) => {
    throw new HttpError({
      statusCode: 404,
      message: `404: Endpoint ${c.req.method} ${c.req.path} not found`,
    });
  })
  .onError(errorHandler);

export default app;
