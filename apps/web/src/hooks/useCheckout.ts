'use client';

import { useCart } from '@/contexts/cart-context';
import type {
  CheckoutData,
  CheckoutState,
  CheckoutStep,
} from '@/types/checkout';
import { useCreateOrder } from '@muraadso/api/hooks';
import { useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';

const initialState: CheckoutState = {
  currentStep: 'customer',
  data: {},
  isSubmitting: false,
  errors: {},
};

export const useCheckout = () => {
  const [state, setState] = useState<CheckoutState>(initialState);
  const { items, totalPrice, clearCart } = useCart();
  const { mutateAsync: createOrder } = useCreateOrder();
  const router = useRouter();

  const updateData = useCallback((stepData: Partial<CheckoutData>) => {
    setState((prev) => ({
      ...prev,
      data: { ...prev.data, ...stepData },
      errors: {},
    }));
  }, []);

  const setCurrentStep = useCallback((step: CheckoutStep) => {
    setState((prev) => ({ ...prev, currentStep: step }));
  }, []);

  const setErrors = useCallback((errors: Record<string, string>) => {
    setState((prev) => ({ ...prev, errors }));
  }, []);

  const validateCustomerInfo = useCallback(
    (data: CheckoutData['customerInfo']) => {
      const errors: Record<string, string> = {};

      if (!data?.firstName?.trim()) {
        errors.firstName = 'First name is required';
      }
      if (!data?.lastName?.trim()) {
        errors.lastName = 'Last name is required';
      }
      if (!data?.email?.trim()) {
        errors.email = 'Email is required';
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
        errors.email = 'Please enter a valid email address';
      }
      if (!data?.phone?.trim()) {
        errors.phone = 'Phone number is required';
      } else if (!/^\+?[\d\s\-\()]{10,}$/.test(data.phone)) {
        errors.phone = 'Please enter a valid phone number';
      }

      return errors;
    },
    [],
  );

  const validateAddress = useCallback(
    (data: CheckoutData['shippingAddress']) => {
      const errors: Record<string, string> = {};

      if (!data?.address?.trim()) {
        errors.address = 'Address is required';
      }
      if (!data?.city?.trim()) {
        errors.city = 'City is required';
      }
      if (!data?.postalCode?.trim()) {
        errors.postalCode = 'Postal code is required';
      }
      if (!data?.country?.trim()) {
        errors.country = 'Country is required';
      }
      if (!data?.state?.trim()) {
        errors.state = 'State/Province is required';
      }

      return errors;
    },
    [],
  );

  const nextStep = useCallback(() => {
    const { currentStep, data } = state;
    let errors: Record<string, string> = {};

    // Validate current step
    switch (currentStep) {
      case 'customer':
        errors = validateCustomerInfo(data.customerInfo!);
        break;
      case 'address':
        errors = validateAddress(data.shippingAddress!);
        break;
      case 'payment':
        if (!data.paymentMethod?.type) {
          errors.paymentMethod = 'Please select a payment method';
        }
        break;
    }

    if (Object.keys(errors).length > 0) {
      setErrors(errors);
      return false;
    }

    // Move to next step
    const stepOrder: CheckoutStep[] = [
      'customer',
      'address',
      'payment',
      'review',
    ];
    const currentIndex = stepOrder.indexOf(currentStep);
    if (currentIndex < stepOrder.length - 1) {
      setCurrentStep(stepOrder[currentIndex + 1]!);
    }

    return true;
  }, [state, validateCustomerInfo, validateAddress, setErrors, setCurrentStep]);

  const previousStep = useCallback(() => {
    const stepOrder: CheckoutStep[] = [
      'customer',
      'address',
      'payment',
      'review',
    ];
    const currentIndex = stepOrder.indexOf(state.currentStep);
    if (currentIndex > 0) {
      setCurrentStep(stepOrder[currentIndex - 1]!);
    }
  }, [state.currentStep, setCurrentStep]);

  const submitOrder = useCallback(async () => {
    if (
      !state.data.customerInfo ||
      !state.data.shippingAddress ||
      !state.data.paymentMethod
    ) {
      return;
    }

    setState((prev) => ({ ...prev, isSubmitting: true }));

    try {
      const orderData = {
        customer: {
          name: `${state.data.customerInfo.firstName} ${state.data.customerInfo.lastName}`,
          email: state.data.customerInfo.email,
          phone: state.data.customerInfo.phone,
        },
        address: `${state.data.shippingAddress.address}, ${state.data.shippingAddress.city}, ${state.data.shippingAddress.state} ${state.data.shippingAddress.postalCode}, ${state.data.shippingAddress.country}`,
        serviceType: 'retail' as const,
        items: items.map((item) => ({
          productId: item.product.id,
          variantId: item.variant.id,
          quantity: item.quantity,
          unitPrice: item.variant.prices?.retailPrice || 0,
        })),
      };

      const result = await createOrder(orderData);

      // Clear cart and redirect to success page
      router.push(`/checkout/success?orderId=${result.id}`);
      clearCart();
    } catch (error) {
      console.error('Order submission failed:', error);
    } finally {
      setState((prev) => ({ ...prev, isSubmitting: false }));
    }
  }, [state.data, createOrder, items, clearCart, router]);

  const orderSummary = {
    subtotal: totalPrice,
    shipping: 0, // Free shipping for now
    taxes: 0, // No taxes for now
    total: totalPrice,
  };

  return {
    state,
    items,
    orderSummary,
    updateData,
    setCurrentStep,
    nextStep,
    previousStep,
    submitOrder,
  };
};
