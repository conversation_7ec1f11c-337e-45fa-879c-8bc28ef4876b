import { z } from 'zod';

const envSchema = z.object({
  NEXT_PUBLIC_WEB_URL: z.coerce.string().nonempty(),
  NEXT_PUBLIC_API_URL: z.coerce.string().nonempty(),
});

/** Next.js handles environment variables prefixed with `NEXT_PUBLIC_` differently
 for client-side code. These variables are inlined at build time, meaning
 `process.env.NEXT_PUBLIC_API_URL` will be replaced with its actual value
 in the browser bundle.

 However, when <PERSON><PERSON> parses the entire `process.env` object (`{...process.env}`),
 especially in a client-side context, the `process.env` object itself might not
 reliably contain `NEXT_PUBLIC_API_URL` in a way that <PERSON><PERSON> can pick it up
 directly from the spread object. `process.env` on the client can be an empty
 object or might not have these variables readily enumerable for <PERSON><PERSON>'s schema
 matching after the spread.

 By explicitly setting `NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL`,
 we ensure that <PERSON><PERSON> receives the *actual inlined value* of this specific
 client-side environment variable. This guarantees that the variable defined
 in your .env file is correctly validated by <PERSON><PERSON>, even if the general
 `process.env` object on the client doesn't fully expose it in the same way
 server-side variables are exposed. The `...process.env` part handles
 other variables, typically server-side ones like `DATABASE_URL`, which are
 directly available in the `process.env` object when this code runs in a
 Node.js environment (server-side or at build time). */
export const env = envSchema.parse({
  ...process.env,
  NEXT_PUBLIC_WEB_URL: process.env.NEXT_PUBLIC_WEB_URL,
  NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
});
