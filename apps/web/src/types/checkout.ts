export interface CustomerInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

export interface ShippingAddress {
  address: string;
  company?: string;
  postalCode: string;
  city: string;
  country: string;
  state: string;
}

export interface BillingAddress extends ShippingAddress {
  sameAsShipping: boolean;
}

export interface PaymentMethod {
  type: 'cash_on_delivery';
  // Future payment methods can be added here
}

export interface CheckoutData {
  customerInfo: CustomerInfo;
  shippingAddress: ShippingAddress;
  billingAddress: BillingAddress;
  paymentMethod: PaymentMethod;
}

export type CheckoutStep = 'customer' | 'address' | 'payment' | 'review';

export interface CheckoutState {
  currentStep: CheckoutStep;
  data: Partial<CheckoutData>;
  isSubmitting: boolean;
  errors: Record<string, string>;
}

export interface OrderSummary {
  subtotal: number;
  shipping: number;
  taxes: number;
  total: number;
}
