import type { Product } from '@muraadso/models/products';
import type { Variant } from '@muraadso/models/variants';

export interface CartItem {
  id: string; // Format: ${product.id}-${variant.id}
  product: Product;
  variant: Variant;
  quantity: number;
  addedAt: Date;
}

export interface CartState {
  items: CartItem[];
  totalItems: number;
  totalPrice: number;
}

export interface CartActions {
  addItem: (product: Product, variant: Variant, quantity?: number) => void;
  removeItem: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  getItem: (productId: string, variantId: string) => CartItem | undefined;
}

export interface CartContextType extends CartState, CartActions {
  isLoading: boolean;
}
