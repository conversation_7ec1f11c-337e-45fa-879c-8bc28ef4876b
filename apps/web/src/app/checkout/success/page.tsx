'use client';

import { Footer } from '@/components/layout/footer';
import { Header } from '@/components/layout/header';
import { formatPrice, getImageUrl } from '@/lib/utils';
import { useGetOrder } from '@muraadso/api/hooks';
import { ShoppingCartIcon } from '@muraadso/icons';
import { Skeleton } from '@muraadso/ui';
import Image from 'next/image';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import React from 'react';

type PageProps = {
  searchParams: {
    orderId?: string;
  };
};

const Page = ({ searchParams }: PageProps) => {
  const orderId = searchParams.orderId;

  if (!orderId) {
    return notFound();
  }
  const { order, isPending, error } = useGetOrder(orderId);

  if (isPending) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="mx-auto max-w-2xl space-y-6">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96" />
            <div className="space-y-4">
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-20 w-full" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <div className="text-center">
          <h1 className="mb-4 font-bold text-2xl">Order Not Found</h1>
          <p className="mb-6 text-muted-foreground">
            We couldn't find your order. Please check your order ID or contact
            support.
          </p>
          <Link
            href="/"
            className="inline-flex items-center rounded-md bg-black px-4 py-2 text-white transition-colors hover:bg-gray-800"
          >
            Go Home
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <Header />

      <div className="container mx-auto px-4 py-8">
        <div className="mx-auto max-w-2xl">
          {/* Success Message */}
          <div className="mb-8 text-center">
            <h1 className="mb-2 font-bold text-3xl">Thank you!</h1>
            <p className="mb-4 text-muted-foreground text-xl">
              Your order was placed successfully.
            </p>
            <p className="mb-2 text-muted-foreground text-sm">
              We have sent the order confirmation details to{' '}
              <span className="font-medium">{order.customer.email}</span>.
            </p>
            <p className="mb-4 text-muted-foreground text-sm">
              Order date:{' '}
              {new Date(order.createdAt).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </p>
            <Link
              href={`/orders/${order.id}`}
              className="text-blue-600 text-sm hover:underline"
            >
              Order number: {order.trackingId}
            </Link>
          </div>

          {/* Order Summary */}
          <div className="mb-8">
            <h2 className="mb-4 font-semibold text-xl">Summary</h2>

            <div className="mb-6 space-y-4">
              {order.items.map((item) => (
                <div key={item.id} className="flex items-center gap-4">
                  <div className="relative h-16 w-16 flex-shrink-0 overflow-hidden rounded-md border border-border">
                    <div className="flex h-full w-full items-center justify-center bg-muted">
                      <ShoppingCartIcon className="h-6 w-6 text-muted-foreground" />
                    </div>
                  </div>

                  <div className="flex-1">
                    <h4 className="font-medium">
                      {item.productSnapshot?.name}
                    </h4>
                    <p className="text-muted-foreground text-sm">
                      Variant: {item.variantSnapshot?.name}
                    </p>
                  </div>

                  <div className="text-right">
                    <p className="text-sm">
                      {item.quantity}x {formatPrice(item.unitPrice)}
                    </p>
                    <p className="font-medium">
                      {formatPrice(item.unitPrice * item.quantity)}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            <div className="space-y-2 border-border border-t pt-4">
              <div className="flex justify-between text-sm">
                <span>Subtotal (excl. shipping and taxes)</span>
                <span>{formatPrice(order.totalAmount)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Shipping</span>
                <span>{formatPrice(0)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Taxes</span>
                <span>{formatPrice(0)}</span>
              </div>
              <div className="flex justify-between border-border border-t pt-2 font-semibold text-lg">
                <span>Total</span>
                <span>{formatPrice(order.totalAmount)}</span>
              </div>
            </div>
          </div>

          {/* Delivery Information */}
          <div className="mb-8">
            <h2 className="mb-4 font-semibold text-xl">Delivery</h2>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
              <div>
                <h3 className="mb-2 font-medium">Shipping Address</h3>
                <div className="space-y-1 text-muted-foreground text-sm">
                  <p>{order.customer.name}</p>
                  <p>{order.address}</p>
                </div>
              </div>

              <div>
                <h3 className="mb-2 font-medium">Contact</h3>
                <div className="space-y-1 text-muted-foreground text-sm">
                  <p>{order.customer.name}</p>
                  <p>{order.customer.email}</p>
                </div>
              </div>

              <div>
                <h3 className="mb-2 font-medium">Method</h3>
                <div className="text-muted-foreground text-sm">
                  <p>Express Shipping: Free</p>
                </div>
              </div>
            </div>
          </div>

          {/* Payment Information */}
          <div className="mb-8">
            <h2 className="mb-4 font-semibold text-xl">Payment</h2>

            <div className="text-muted-foreground text-sm">
              <p className="font-medium">Payment method</p>
              <p>Cash on Delivery</p>
            </div>
          </div>

          {/* Help Section */}
          <div className="border-border border-t pt-6">
            <h3 className="mb-2 font-medium">Need help?</h3>
            <div className="space-y-1 text-muted-foreground text-sm">
              <p>
                <Link href="/contact" className="text-blue-600 hover:underline">
                  Contact
                </Link>
              </p>
              <p>
                <Link href="/returns" className="text-blue-600 hover:underline">
                  Returns & Exchanges
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Page;
