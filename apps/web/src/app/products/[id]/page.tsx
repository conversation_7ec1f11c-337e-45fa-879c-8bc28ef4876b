'use client';

import { Footer } from '@/components/layout/footer';
import { Header } from '@/components/layout/header';
import { ProductDetailsActions } from '@/components/product-details/product-details-actions';
import { ProductDetailsImages } from '@/components/product-details/product-details-images';
import { ProductDetailsInfo } from '@/components/product-details/product-details-info';
import { ProductDetailsVariants } from '@/components/product-details/product-details-variants';
import { useGetProduct } from '@muraadso/api/hooks';
import { ChevronRightIcon, HomeIcon } from '@muraadso/icons';
import type { Variant } from '@muraadso/models/variants';
import { Skeleton } from '@muraadso/ui';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { use, useEffect, useState } from 'react';

const ProductDetailsSkeleton = () => (
  <div className="container mx-auto px-4 py-8">
    <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
      {/* Image Gallery Skeleton */}
      <div className="space-y-4">
        <Skeleton className="aspect-square w-full rounded-lg" />
        <div className="grid grid-cols-4 gap-2">
          {Array.from({ length: 4 }).map((_, i) => (
            <Skeleton key={i} className="aspect-square w-full rounded-md" />
          ))}
        </div>
      </div>

      {/* Product Info Skeleton */}
      <div className="space-y-6">
        <div className="space-y-2">
          <Skeleton className="h-8 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
          <Skeleton className="h-6 w-1/3" />
        </div>
        <Skeleton className="h-20 w-full" />
        <div className="space-y-2">
          <Skeleton className="h-4 w-1/4" />
          <div className="grid grid-cols-3 gap-2">
            {Array.from({ length: 3 }).map((_, i) => (
              <Skeleton key={i} className="h-10 w-full" />
            ))}
          </div>
        </div>
        <div className="space-y-2">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
        </div>
      </div>
    </div>
  </div>
);

const Page = ({ params }: { params: Promise<{ id: string }> }) => {
  const { id } = use(params);

  const { product, isPending, error } = useGetProduct(id);
  const [selectedVariant, setSelectedVariant] = useState<Variant | undefined>();

  // // Set default variant when product loads
  useEffect(() => {
    if (product?.variants?.length && !selectedVariant) {
      const defaultVariant =
        product.variants.find((v) => v.rank === 0) || product.variants[0];
      setSelectedVariant(defaultVariant);
    }
  }, [product, selectedVariant]);

  if (isPending) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <ProductDetailsSkeleton />
        <Footer />
      </div>
    );
  }

  if (error || !product) return notFound();

  return (
    <div className="min-h-screen bg-background">
      <Header />

      {/*/!* Breadcrumb *!/*/}
      <div className="border-border border-b ">
        <div className="container mx-auto px-4 py-4">
          <nav className="flex items-center space-x-2 text-muted-foreground text-sm">
            <Link href="/" className="hover:text-foreground">
              <HomeIcon className="h-4 w-4" />
            </Link>
            <ChevronRightIcon className="h-4 w-4" />
            <Link href="/products" className="hover:text-foreground">
              Products
            </Link>
            <ChevronRightIcon className="h-4 w-4" />
            <Link
              href={`/category/${product.category.slug}`}
              className="hover:text-foreground"
            >
              {product.category.name}
            </Link>
            <ChevronRightIcon className="h-4 w-4" />
            <span className="text-foreground">{product.name}</span>
          </nav>
        </div>
      </div>

      {/* Product Details */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
          {/* Left Column - Images */}
          <ProductDetailsImages
            images={product.images}
            productName={product.name}
            selectedVariant={selectedVariant}
          />

          {/* Right Column - Product Info */}
          <div className="space-y-6">
            <ProductDetailsInfo
              product={product}
              selectedVariant={selectedVariant}
            />

            <ProductDetailsVariants
              product={product}
              selectedVariant={selectedVariant}
              onVariantChange={setSelectedVariant}
            />

            <ProductDetailsActions
              product={product}
              selectedVariant={selectedVariant}
            />
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Page;
