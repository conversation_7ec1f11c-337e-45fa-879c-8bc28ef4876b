'use client';

import type { CartContextType, CartItem } from '@/types/cart';
import type { Product } from '@muraadso/models/products';
import type { Variant } from '@muraadso/models/variants';
import { toast } from '@muraadso/ui';
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
  type PropsWithChildren,
} from 'react';

const CartContext = createContext<CartContextType | null>(null);

const CART_STORAGE_KEY = 'muraadso-cart';

// Helper function to create cart item ID
const createCartItemId = (productId: string, variantId: string): string => {
  return `${productId}-${variantId}`;
};

// Helper function to calculate total price
const calculateTotalPrice = (items: CartItem[]): number => {
  return items.reduce((total, item) => {
    return total + (item.variant.prices?.retailPrice || 0) * item.quantity;
  }, 0);
};

// Helper function to calculate total items
const calculateTotalItems = (items: CartItem[]): number => {
  return items.reduce((total, item) => total + item.quantity, 0);
};

// Helper functions for localStorage
const loadCartFromStorage = (): CartItem[] => {
  try {
    if (typeof window === 'undefined') return [];

    const stored = localStorage.getItem(CART_STORAGE_KEY);
    if (!stored) return [];

    const parsed = JSON.parse(stored);
    // Convert addedAt back to Date objects
    return parsed.map((item) => ({
      ...item,
      addedAt: new Date(item.addedAt),
    }));
  } catch (error) {
    console.warn('Failed to load cart from localStorage:', error);
    return [];
  }
};

const saveCartToStorage = (items: CartItem[]): void => {
  try {
    if (typeof window === 'undefined') return;
    localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(items));
  } catch (error) {
    console.warn('Failed to save cart to localStorage:', error);
  }
};

export const CartProvider = ({ children }: PropsWithChildren) => {
  const [items, setItems] = useState<CartItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load cart from localStorage on mount
  useEffect(() => {
    const storedItems = loadCartFromStorage();
    setItems(storedItems);
    setIsLoading(false);
  }, []);

  // Save cart to localStorage whenever items change
  useEffect(() => {
    if (!isLoading) {
      saveCartToStorage(items);
    }
  }, [items, isLoading]);

  // Calculate derived values
  const totalItems = useMemo(() => calculateTotalItems(items), [items]);
  const totalPrice = useMemo(() => calculateTotalPrice(items), [items]);

  // Add item to cart
  const addItem = useCallback(
    (product: Product, variant: Variant, quantity = 1) => {
      const itemId = createCartItemId(product.id, variant.id);

      setItems((prevItems) => {
        const existingItemIndex = prevItems.findIndex(
          (item) => item.id === itemId,
        );

        if (existingItemIndex >= 0) {
          // Update existing item quantity
          const updatedItems = [...prevItems];
          const existingItem = updatedItems[existingItemIndex];
          if (existingItem) {
            updatedItems[existingItemIndex] = {
              ...existingItem,
              quantity: existingItem.quantity + quantity,
            };
          }
          return updatedItems;
        }
        const newItem: CartItem = {
          id: itemId,
          product,
          variant,
          quantity,
          addedAt: new Date(),
        };
        return [...prevItems, newItem];
      });

      toast.success(`Added ${product.name} to cart`);
    },
    [],
  );

  // Remove item from cart
  const removeItem = useCallback((itemId: string) => {
    setItems((prevItems) => {
      const item = prevItems.find((item) => item.id === itemId);
      const updatedItems = prevItems.filter((item) => item.id !== itemId);

      if (item) {
        toast.success(`Removed ${item.product.name} from cart`);
      }

      return updatedItems;
    });
  }, []);

  // Update item quantity
  const updateQuantity = useCallback(
    (itemId: string, quantity: number) => {
      if (quantity <= 0) {
        removeItem(itemId);
        return;
      }

      setItems((prevItems) => {
        return prevItems.map((item) =>
          item.id === itemId ? { ...item, quantity } : item,
        );
      });
    },
    [removeItem],
  );

  // Clear entire cart
  const clearCart = useCallback(() => {
    setItems([]);
    toast.success('Cart cleared');
  }, []);

  // Get specific item
  const getItem = useCallback(
    (productId: string, variantId: string) => {
      const itemId = createCartItemId(productId, variantId);
      return items.find((item) => item.id === itemId);
    },
    [items],
  );

  const contextValue: CartContextType = {
    items,
    totalItems,
    totalPrice,
    isLoading,
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    getItem,
  };

  return (
    <CartContext.Provider value={contextValue}>{children}</CartContext.Provider>
  );
};

// Custom hook to use cart context
export const useCart = (): CartContextType => {
  const context = useContext(CartContext);

  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }

  return context;
};
