import { getImageUrl } from '@/lib/utils';
import { ChevronLeftIcon, ChevronRightIcon, ZoomInIcon } from '@muraadso/icons';
import type { Image } from '@muraadso/models/images';
import type { Variant } from '@muraadso/models/variants';
import { Button } from '@muraadso/ui';
import NextImage from 'next/image';
import { useState } from 'react';

interface ProductImageGalleryProps {
  images: Image[];
  productName: string;
  selectedVariant?: Variant | null;
}

export const ProductDetailsImages = ({
  images,
  productName,
}: ProductImageGalleryProps) => {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isZoomed, setIsZoomed] = useState(false);

  // Get variant-specific images if available, otherwise use product images
  // For now, we'll use product images since variant images might not be implemented yet
  const displayImages = images;

  const currentImage = displayImages[selectedImageIndex];

  const handlePrevious = () => {
    setSelectedImageIndex((prev) =>
      prev === 0 ? displayImages.length - 1 : prev - 1,
    );
  };

  const handleNext = () => {
    setSelectedImageIndex((prev) =>
      prev === displayImages.length - 1 ? 0 : prev + 1,
    );
  };

  const handleThumbnailClick = (index: number) => {
    setSelectedImageIndex(index);
  };

  if (!displayImages.length) {
    return (
      <div className="space-y-4">
        <div className="flex aspect-square items-center justify-center rounded-lg bg-gray-100">
          <div className="text-center text-gray-400">
            <div className="mb-2 text-6xl">📱</div>
            <p className="text-sm">No image available</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Main Image */}
      <div className="group relative">
        <div
          className={`aspect-square overflow-hidden rounded-lg border bg-white ${
            isZoomed ? 'cursor-zoom-in-out' : 'cursor-zoom-in-in'
          }`}
          onClick={() => setIsZoomed(!isZoomed)}
        >
          <NextImage
            src={getImageUrl(currentImage?.path || '')}
            alt={`${productName} - Image ${selectedImageIndex + 1}`}
            width={600}
            height={600}
            className={`h-full w-full object-contain transition-transform duration-300 ${
              isZoomed ? 'scale-150' : 'scale-100'
            }`}
            priority
          />
        </div>

        {/* Navigation Arrows */}
        {displayImages.length > 1 && (
          <>
            <Button
              variant="outline"
              size="sm"
              className="-translate-y-1/2 absolute top-1/2 left-2 bg-white/90 opacity-0 backdrop-blur-sm transition-opacity group-hover:opacity-100"
              onClick={handlePrevious}
            >
              <ChevronLeftIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="-translate-y-1/2 absolute top-1/2 right-2 bg-white/90 opacity-0 backdrop-blur-sm transition-opacity group-hover:opacity-100"
              onClick={handleNext}
            >
              <ChevronRightIcon className="h-4 w-4" />
            </Button>
          </>
        )}

        {/* Zoom Indicator */}
        <div className="absolute top-2 right-2 opacity-0 transition-opacity group-hover:opacity-100">
          <div className="flex items-center gap-1 rounded bg-black/50 px-2 py-1 text-white text-xs">
            <ZoomInIcon className="size-3" />
            Click to zoom
          </div>
        </div>

        {/* Image Counter */}
        {displayImages.length > 1 && (
          <div className="absolute bottom-2 left-2 rounded bg-black/50 px-2 py-1 text-white text-xs">
            {selectedImageIndex + 1} / {displayImages.length}
          </div>
        )}
      </div>

      {/* Thumbnail Grid */}
      {displayImages.length > 1 && (
        <div className="grid grid-cols-4 gap-2 sm:grid-cols-5 md:grid-cols-6">
          {displayImages.map(({ id, path }, index) => (
            <button
              key={id}
              type="button"
              className={`aspect-square overflow-hidden rounded-md border-2 bg-white transition-all ${
                index === selectedImageIndex
                  ? 'border-primary ring-2 ring-primary/20'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => handleThumbnailClick(index)}
            >
              <NextImage
                src={getImageUrl(path)}
                alt={`${productName} - Thumbnail ${index + 1}`}
                width={100}
                height={100}
                className="h-full w-full object-contain"
              />
            </button>
          ))}
        </div>
      )}
    </div>
  );
};
