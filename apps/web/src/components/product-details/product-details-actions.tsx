'use client';

import { useCart } from '@/contexts/cart-context';
import { formatPrice } from '@/lib/utils';
import {
  CreditCardIcon,
  MinusIcon,
  PlusIcon,
  // HeartIcon,
  ShareIcon,
  // TruckIcon,
  ShieldCheckIcon,
  ShoppingCartIcon,
  // RefreshCwIcon
} from '@muraadso/icons';
import type { Product } from '@muraadso/models/products';
import type { Variant } from '@muraadso/models/variants';
import { Button, Separator, toast } from '@muraadso/ui';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

interface ProductActionsProps {
  product: Product;
  selectedVariant?: Variant | null;
}

export const ProductDetailsActions: React.FC<ProductActionsProps> = ({
  product,
  selectedVariant,
}) => {
  const { addItem, getItem } = useCart();
  const router = useRouter();
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  const [quantity, setQuantity] = useState(1);

  // Check if current product+variant is already in cart
  const cartItem = selectedVariant
    ? getItem(product.id, selectedVariant.id)
    : undefined;

  const currentPrice = selectedVariant?.prices?.retailPrice || 0;
  const isInStock = true; // This would come from inventory data

  const handleAddToCart = async () => {
    if (!selectedVariant) {
      toast.error('Please select a variant');
      return;
    }

    setIsAddingToCart(true);

    try {
      // Add a small delay for better UX
      await new Promise((resolve) => setTimeout(resolve, 300));
      addItem(product, selectedVariant, quantity);
    } catch (error) {
      toast.error('Failed to add to cart');
    } finally {
      setIsAddingToCart(false);
    }
  };

  const handleBuyNow = async () => {
    if (!selectedVariant) {
      toast.error('Please select a variant');
      return;
    }

    // Add item to cart and redirect to checkout
    addItem(product, selectedVariant, quantity);
    router.push('/checkout');
  };

  const handleToggleFavorite = () => {
    setIsFavorited(!isFavorited);
    toast.success(
      isFavorited ? 'Removed from favorites' : 'Added to favorites',
    );
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: product.name,
          text: `Check out this ${product.name} from ${product.brand.name}`,
          url: window.location.href,
        });
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      // Fallback: copy to clipboard
      await navigator.clipboard.writeText(window.location.href);
      toast.success('Link copied to clipboard!');
    }
  };

  return (
    <div className="space-y-6">
      <Separator />

      {/* Quantity Selector */}
      <div className="space-y-2">
        <p className="font-medium text-gray-900 text-sm">Quantity</p>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setQuantity(Math.max(1, quantity - 1))}
            disabled={quantity <= 1}
          >
            <MinusIcon className="size-4" />
          </Button>
          <span className="w-12 text-center font-medium">{quantity}</span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setQuantity(quantity + 1)}
            disabled={quantity >= 10}
          >
            <PlusIcon className="size-4" />
          </Button>
        </div>
      </div>

      {/* Price Summary */}
      <div className="space-y-2 rounded-lg bg-gray-50 p-4">
        <div className="flex items-center justify-between">
          <span className="text-muted-foreground text-sm">Unit Price:</span>
          <span className="font-medium">{formatPrice(currentPrice)}</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-muted-foreground text-sm">Quantity:</span>
          <span className="font-medium">{quantity}</span>
        </div>
        <Separator />
        <div className="flex items-center justify-between">
          <span className="font-semibold">Total:</span>
          <span className="font-bold text-lg">
            {formatPrice(currentPrice * quantity)}
          </span>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center space-x-3 ">
        <Button
          className="w-full"
          size="lg"
          onClick={handleAddToCart}
          loading={isAddingToCart}
          disabled={!isInStock || !selectedVariant}
        >
          Add to Cart
        </Button>

        <Button
          variant="outline"
          className="w-full"
          size="lg"
          onClick={handleBuyNow}
          disabled={!isInStock || !selectedVariant}
        >
          Buy Now
        </Button>
      </div>

      <Separator />

      {/* Shipping & Returns Info */}
      <div className="space-y-4">
        <h3 className="font-medium text-gray-900">Shipping & Returns</h3>

        <div className="space-y-3 text-sm">
          <div className="flex items-start gap-3">
            {/*<TruckIcon className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />*/}
            <div>
              <p className="font-medium">Free Standard Shipping</p>
              <p className="text-muted-foreground">
                Delivery in 3-5 business days
              </p>
            </div>
          </div>

          <div className="flex items-start gap-3">
            {/*<RefreshCwIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />*/}
            <div>
              <p className="font-medium">30-Day Returns</p>
              <p className="text-muted-foreground">
                Free returns on all orders
              </p>
            </div>
          </div>

          <div className="flex items-start gap-3">
            <ShieldCheckIcon className="mt-0.5 h-5 w-5 flex-shrink-0 text-purple-600" />
            <div>
              <p className="font-medium">1-Year Warranty</p>
              <p className="text-muted-foreground">
                Comprehensive coverage included
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Stock Status */}
      <div className="rounded-lg border border-green-200 bg-green-50 p-3">
        <div className="flex items-center gap-2">
          <div className="h-2 w-2 rounded-full bg-green-500"></div>
          <span className="font-medium text-green-800 text-sm">
            {isInStock ? 'In Stock - Ready to Ship' : 'Out of Stock'}
          </span>
        </div>
        {isInStock && (
          <p className="mt-1 text-green-600 text-xs">
            Order within 2 hours for same-day processing
          </p>
        )}
      </div>
    </div>
  );
};
