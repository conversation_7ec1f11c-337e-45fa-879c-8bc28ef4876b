import { formatPrice } from '@/lib/utils';
import {
  CreditCardRefreshIcon,
  ShieldCheckIcon,
  StarIcon,
  TruckIcon,
} from '@muraadso/icons';
import type { Product } from '@muraadso/models/products';
import type { Variant } from '@muraadso/models/variants';
import { Badge, Separator } from '@muraadso/ui';

export const ProductDetailsInfo = ({
  product,
  selectedVariant,
}: {
  product: Product;
  selectedVariant?: Variant | null;
}) => {
  const currentPrice = selectedVariant?.prices?.retailPrice || 0;
  const originalPrice = currentPrice * 1.2;

  return (
    <div className="space-y-6">
      {/* Product Title and Brand */}
      <div className="space-y-2">
        <div className="flex items-center gap-2 text-muted-foreground text-sm">
          <span>{product.brand.name}</span>
          <span>•</span>
          <span>{product.category.name}</span>
        </div>
        <h1 className="font-bold text-2xl text-gray-900 lg:text-3xl">
          {product.name}
        </h1>

        {/* Rating and Reviews */}
        <div className="flex items-center gap-2">
          <div className="flex items-center">
            {Array.from({ length: 5 }).map((_, i) => (
              <StarIcon
                key={i}
                className={`size-4 ${
                  i < 4 ? 'fill-current text-yellow-400' : 'text-gray-300'
                }`}
              />
            ))}
          </div>
          <span className="text-muted-foreground text-sm">
            (4.0) • 127 reviews
          </span>
        </div>
      </div>

      {/* Pricing */}
      <div className="space-y-2">
        <div className="flex items-baseline gap-3">
          <span className="font-bold text-3xl text-gray-900">
            {formatPrice(currentPrice)}
          </span>
          <span className="text-lg text-muted-foreground line-through">
            {formatPrice(originalPrice)}
          </span>
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            Save {formatPrice(originalPrice - currentPrice)}
          </Badge>
        </div>

        {/* Additional Pricing Info */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-1">
            <p className="text-muted-foreground">Trade-in value:</p>
            <p className="font-semibold text-blue-600">
              {formatPrice(selectedVariant?.prices?.tradeInPrice || 0)}
            </p>
          </div>
          <div className="space-y-1">
            <p className="text-muted-foreground">Buyback guarantee:</p>
            <p className="font-semibold text-green-600">
              {formatPrice(selectedVariant?.prices?.buybackPrice || 0)}
            </p>
          </div>
        </div>
      </div>

      <Separator />

      {/* Product Description */}
      {product.description && (
        <div className="space-y-2">
          <h3 className="font-semibold text-gray-900">Description</h3>
          <p className="text-muted-foreground leading-relaxed">
            {product.description}
          </p>
        </div>
      )}

      {/* Key Features */}
      <div className="space-y-3">
        <h3 className="font-semibold text-gray-900">Key Features</h3>
        <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
          <div className="flex items-center gap-2 text-sm">
            <ShieldCheckIcon className="size-4 text-green-600" />
            <span>Certified Refurbished</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <TruckIcon className="size-4 text-blue-600" />
            <span>Free Shipping</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <CreditCardRefreshIcon className="size-4 text-purple-600" />
            <span>30-Day Return Policy</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <ShieldCheckIcon className="size-4 text-orange-600" />
            <span>1-Year Warranty</span>
          </div>
        </div>
      </div>

      <Separator />

      {/* Product Specifications */}
      <div className="space-y-3">
        <h3 className="font-semibold text-gray-900">Specifications</h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Brand:</span>
            <span className="font-medium">{product.brand.name}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Category:</span>
            <span className="font-medium">{product.category.name}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Condition:</span>
            <span className="font-medium">Excellent</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">SKU:</span>
            <span className="font-medium">{selectedVariant?.sku || 'N/A'}</span>
          </div>
        </div>
      </div>
    </div>
  );
};
