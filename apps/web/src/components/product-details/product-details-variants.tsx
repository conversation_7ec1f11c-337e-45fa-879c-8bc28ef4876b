import { formatPrice } from '@/lib/utils';
import { CheckIcon } from '@muraadso/icons';
import type { Product } from '@muraadso/models/products';
import type { Variant } from '@muraadso/models/variants';
import { <PERSON><PERSON>, Button, Separator } from '@muraadso/ui';
import { useEffect, useState } from 'react';

interface ProductVariantsProps {
  product: Product;
  selectedVariant?: Variant | null;
  onVariantChange: (variant: Variant) => void;
}

export const ProductDetailsVariants = ({
  product,
  selectedVariant,
  onVariantChange,
}: ProductVariantsProps) => {
  const [selectedOptions, setSelectedOptions] = useState<
    Record<string, string>
  >({});

  useEffect(() => {
    if (selectedVariant && product.options.length > 0) {
      const options: Record<string, string> = {};

      // Get the option values for the selected variant
      for (const optionValue of selectedVariant.optionValues) {
        const option = product.options.find((opt) =>
          opt.values.some((val) => val.id === optionValue.id),
        );
        if (option) {
          options[option.name] = optionValue.value;
        }
      }

      setSelectedOptions(options);
    }
  }, [selectedVariant, product.options]);

  // Handle option selection
  const handleOptionChange = (optionName: string, optionValue: string) => {
    const newSelectedOptions = {
      ...selectedOptions,
      [optionName]: optionValue,
    };
    setSelectedOptions(newSelectedOptions);

    // Find the variant that matches the selected options
    const matchingVariant = product.variants.find((variant) => {
      return variant.optionValues?.every((optionValue) => {
        const option = product.options.find((opt) =>
          opt.values.some((val) => val.id === optionValue.id),
        );
        return option && newSelectedOptions[option.name] === optionValue.value;
      });
    });

    if (matchingVariant) {
      onVariantChange(matchingVariant);
    }
  };

  // If no variants or options, don't render anything
  if (!product.variants.length || !product.options.length) {
    return null;
  }

  return (
    <div className="space-y-6">
      <Separator />

      {/* Variant Options */}
      <div className="space-y-4">
        {product.options.map((option) => (
          <div key={option.id} className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900">{option.name}</h3>
              {selectedOptions[option.name] && (
                <span className="text-muted-foreground text-sm">
                  Selected: {selectedOptions[option.name]}
                </span>
              )}
            </div>

            <div className="grid grid-cols-2 gap-2 sm:grid-cols-3 lg:grid-cols-4">
              {option.values.map((value) => {
                const isSelected = selectedOptions[option.name] === value.value;

                // Check if this option value is available in any variant
                const isAvailable = product.variants.some((variant) =>
                  variant.optionValues?.some(
                    (optionValue) => optionValue.id === value.id,
                  ),
                );

                return (
                  <Button
                    key={value.id}
                    variant={isSelected ? 'default' : 'outline'}
                    size="sm"
                    className={`relative justify-start ${
                      !isAvailable ? 'cursor-not-allowed opacity-50' : ''
                    }`}
                    onClick={() =>
                      isAvailable &&
                      handleOptionChange(option.name, value.value)
                    }
                    disabled={!isAvailable}
                  >
                    <span className="truncate">{value.value}</span>
                    {isSelected && (
                      <CheckIcon className="ml-auto h-4 w-4 flex-shrink-0" />
                    )}
                  </Button>
                );
              })}
            </div>
          </div>
        ))}
      </div>

      {/* Variant Information */}
      {selectedVariant && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-gray-900">
              Selected Configuration
            </h3>
            <Badge variant="outline" className="text-xs">
              {selectedVariant.name}
            </Badge>
          </div>

          <div className="space-y-2 rounded-lg bg-gray-50 p-4">
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground text-sm">Price:</span>
              <span className="font-semibold text-lg">
                {formatPrice(selectedVariant.prices?.retailPrice || 0)}
              </span>
            </div>

            {selectedVariant.sku && (
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground text-sm">SKU:</span>
                <span className="font-mono text-sm">{selectedVariant.sku}</span>
              </div>
            )}

            <div className="flex items-center justify-between">
              <span className="text-muted-foreground text-sm">
                Availability:
              </span>
              <Badge
                variant="secondary"
                className="bg-green-100 text-green-800"
              >
                In Stock
              </Badge>
            </div>
          </div>
        </div>
      )}

      {/* All Variants Quick View */}
      {product.variants.length > 1 && (
        <div className="space-y-3">
          <h3 className="font-medium text-gray-900">All Configurations</h3>
          <div className="space-y-2">
            {product.variants.map((variant) => {
              const isCurrentVariant = selectedVariant?.id === variant.id;

              return (
                <button
                  key={variant.id}
                  type="button"
                  className={`w-full rounded-lg border p-3 text-left transition-all ${
                    isCurrentVariant
                      ? 'border-primary bg-primary/5'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                  onClick={() => onVariantChange(variant)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-sm">{variant.name}</p>
                      {variant.sku && (
                        <p className="text-muted-foreground text-xs">
                          {variant.sku}
                        </p>
                      )}
                    </div>
                    <div className="text-end">
                      <p className="font-semibold">
                        {formatPrice(variant.prices?.retailPrice || 0)}
                      </p>
                      <span className="text-xs">In Stock</span>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};
