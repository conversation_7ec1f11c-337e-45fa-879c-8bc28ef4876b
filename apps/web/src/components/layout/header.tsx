'use client';

import { CartDetails } from '@/components/cart/cart-details';
import { siteConfig } from '@/config/site';
import { useCart } from '@/contexts/cart-context';
import { formatPrice } from '@/lib/utils';
import {
  MenuIcon,
  SearchIcon,
  ShoppingCartIcon,
  UserIcon,
} from '@muraadso/icons';
import { Badge, Button, Input, Kbd, Tooltip } from '@muraadso/ui';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

export const Header = () => {
  return (
    <header className="sticky top-0 z-50 w-full border-border border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="font-bold text-2xl text-primary">
              <Image
                width={157}
                height={27}
                src="/images/logo.png"
                alt={siteConfig.name}
              />
            </div>
          </Link>

          {/* Right side actions */}
          <div className="flex items-center space-x-4">
            <Input
              placeholder="Search"
              leading={<SearchIcon className="size-4" />}
              trailing={<Kbd>⌘K</Kbd>}
            />

            <CartDetails />

            <Button variant="ghost" size="icon">
              <UserIcon size={20} />
            </Button>

            {/* Mobile menu button */}
            <Button variant="ghost" size="icon" className="md:hidden">
              <MenuIcon size={20} />
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};
