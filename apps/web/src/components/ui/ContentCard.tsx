import { But<PERSON>, <PERSON> } from '@muraadso/ui';
import React from 'react';

interface ContentCardProps {
  title: string;
  description: string;
  buttonText: string;
  buttonHref: string;
  backgroundColor: string;
  textColor?: string;
  image?: string;
}

export const ContentCard = ({
  title,
  description,
  buttonText,
  buttonHref,
  backgroundColor,
  textColor = 'text-white',
  image,
}: ContentCardProps) => {
  return (
    <Card
      className={`${backgroundColor} group hover:-translate-y-1 cursor-pointer overflow-hidden border-none transition-all duration-200 hover:shadow-lg`}
    >
      <Card.Content className="relative p-8">
        {/* Background decoration */}
        <div className="-translate-y-16 absolute top-0 right-0 h-32 w-32 translate-x-16 rounded-full bg-white/10" />
        <div className="-translate-x-12 absolute bottom-0 left-0 h-24 w-24 translate-y-12 rounded-full bg-white/10" />

        <div className="relative z-10">
          <h3 className={`mb-4 font-bold text-2xl lg:text-3xl ${textColor}`}>
            {title}
          </h3>

          <p className={`mb-6 text-lg ${textColor} opacity-90`}>
            {description}
          </p>

          <Button
            className="bg-white font-semibold text-gray-900 hover:bg-gray-100"
            size="lg"
          >
            {buttonText}
          </Button>
        </div>

        {/* Optional image placeholder */}
        {image && (
          <div className="absolute right-4 bottom-4 text-4xl opacity-20">
            📱
          </div>
        )}
      </Card.Content>
    </Card>
  );
};
