import React from 'react';
import { Card } from '@muraadso/ui';
import type { IconProps } from '@muraadso/icons';

interface CategoryCardProps {
  id: string;
  name: string;
  icon: React.ComponentType<IconProps>;
  description?: string;
  href: string;
}

export const CategoryCard: React.FC<CategoryCardProps> = ({
  name,
  icon: Icon,
  description,
  href
}) => {
  return (
    <Card className="group cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1 text-center">
      <Card.Content className="p-8">
        {/* Icon */}
        <div className="w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center group-hover:bg-primary/20 transition-colors">
          <Icon size={32} className="text-primary" />
        </div>
        
        {/* Category Name */}
        <h3 className="font-bold text-lg text-gray-900 mb-2 group-hover:text-primary transition-colors">
          {name}
        </h3>
        
        {/* Description */}
        {description && (
          <p className="text-sm text-muted-foreground">
            {description}
          </p>
        )}
      </Card.Content>
    </Card>
  );
};
