import React from 'react';
import { Card } from '@muraadso/ui';
import { formatPrice } from '@/lib/utils';
import Image from 'next/image';
import Link from 'next/link';

interface ProductCardProps {
  id: string;
  name: string;
  price: number;
  image: string;
  condition?: string;
}

export const ProductCard: React.FC<ProductCardProps> = ({
  id,
  name,
  price,
  image,
  condition = 'Excellent'
}) => {
  return (
    <Link href={`/products/${id}`}>
      <Card className="group cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1">
      <Card.Content className="p-4">
        {/* Product Image */}
        <div className="aspect-square bg-gray-50 rounded-lg mb-4 flex items-center justify-center overflow-hidden">
          {image ? (
            <Image
              src={image}
              alt={name}
              width={200}
              height={200}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="text-6xl">📱</div>
          )}
        </div>
        
        {/* Product Info */}
        <div className="space-y-2">
          <h3 className="font-semibold text-lg text-gray-900 group-hover:text-primary transition-colors">
            {name}
          </h3>
          
          {condition && (
            <p className="text-sm text-muted-foreground">
              {condition}
            </p>
          )}
          
          <p className="text-xl font-bold text-gray-900">
            {formatPrice(price)}
          </p>
        </div>
      </Card.Content>
    </Card>
    </Link>
  );
};
