'use client';

import { env } from '@/config/env';
import { CartProvider } from '@/contexts/cart-context';
import { ApiClientProvider } from '@muraadso/api';
import { Toaster, TooltipProvider, toast } from '@muraadso/ui';
import { ThemeProvider } from 'next-themes';
import { NuqsAdapter } from 'nuqs/adapters/next/app';
import type React from 'react';

// API Client configuration
const apiClientConfig = {
  baseURL: env.NEXT_PUBLIC_API_URL + '/v1',
  timeout: 30000,
  onError: (error: { message: string }) => {
    toast.error(error.message);
  },
};

const Providers: React.FC<React.PropsWithChildren> = ({ children }) => {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="light"
      enableSystem
      disableTransitionOnChange
    >
      <NuqsAdapter>
        <ApiClientProvider config={apiClientConfig}>
          <CartProvider>
            <TooltipProvider delayDuration={100}>
              {children}
              <Toaster position="top-center" richColors />
            </TooltipProvider>
          </CartProvider>
        </ApiClientProvider>
      </NuqsAdapter>
    </ThemeProvider>
  );
};

export default Providers;
