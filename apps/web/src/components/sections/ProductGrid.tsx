'use client';

import { ProductCard } from '@/components/ui/ProductCard';
import { getImageUrl } from '@/lib/utils';
import { useGetProducts } from '@muraadso/api/hooks';
import { Skeleton } from '@muraadso/ui';
import React from 'react';

const ProductSkeleton = () => (
  <div className="space-y-4">
    <Skeleton className="h-48 w-full rounded-lg" />
    <Skeleton className="h-4 w-3/4" />
    <Skeleton className="h-4 w-1/2" />
    <Skeleton className="h-6 w-1/3" />
  </div>
);

export const ProductGrid = () => {
  const { products, isPending, error } = useGetProducts();

  return (
    <section className="bg-white py-16">
      <div className="container mx-auto px-4">
        <div className="mb-12 text-center">
          <h2 className="mb-4 font-bold text-3xl text-gray-900 lg:text-4xl">
            Featured Products
          </h2>
          <p className="mx-auto max-w-2xl text-lg text-muted-foreground">
            Discover our selection of quality refurbished devices, all tested
            and certified
          </p>
        </div>

        {error && (
          <div className="py-8 text-center">
            <p className="text-red-500">
              Failed to load products. Please try again later.
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {isPending
            ? // Show loading skeletons
              Array.from({ length: 8 }).map((_, index) => (
                <ProductSkeleton key={index} />
              ))
            : products.map((product) => {
                // Get the first image if available
                const imageUrl = product.images?.[0]
                  ? getImageUrl(product.images[0])
                  : '';

                return (
                  <ProductCard
                    key={product.id}
                    id={product.id}
                    name={product.name}
                    price={product.retailPrice || 0}
                    image={imageUrl}
                    condition="Excellent" // Default condition for now
                  />
                );
              })}
        </div>

        {!isPending && products.length === 0 && !error && (
          <div className="py-8 text-center">
            <p className="text-muted-foreground">
              No products available at the moment.
            </p>
          </div>
        )}

        {!isPending && products.length > 0 && (
          <div className="mt-12 text-center">
            <a
              href="/products"
              className="font-semibold text-primary hover:underline"
            >
              View all products →
            </a>
          </div>
        )}
      </div>
    </section>
  );
};
