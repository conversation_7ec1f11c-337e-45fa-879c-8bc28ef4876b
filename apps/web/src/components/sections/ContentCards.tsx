import { ContentCard } from '@/components/ui/ContentCard';
import React from 'react';

const contentCards = [
  {
    title: 'Sell your phone with confidence',
    description:
      'Get an instant quote and free shipping. We make selling your old phone simple and secure.',
    buttonText: 'Start selling',
    buttonHref: '/sell',
    backgroundColor: 'bg-gradient-to-br from-blue-500 to-blue-600',
  },
  {
    title: 'Quality guarantee on every device',
    description:
      'All our refurbished phones come with comprehensive testing and 30-day warranty.',
    buttonText: 'Learn more',
    buttonHref: '/warranty',
    backgroundColor: 'bg-gradient-to-br from-green-500 to-green-600',
  },
];

export const ContentCards = () => {
  return (
    <section className="bg-gray-50 py-16">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
          {contentCards.map((card, index) => (
            <ContentCard
              key={index}
              title={card.title}
              description={card.description}
              buttonText={card.buttonText}
              buttonHref={card.buttonHref}
              backgroundColor={card.backgroundColor}
              image="phone"
            />
          ))}
        </div>
      </div>
    </section>
  );
};
