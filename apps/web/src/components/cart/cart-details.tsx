'use client';

import { useCart } from '@/contexts/cart-context';
import { formatPrice, getImageUrl } from '@/lib/utils';
import {
  MinusIcon,
  PlusIcon,
  ShoppingCartIcon,
  TrashIcon,
} from '@muraadso/icons';
import { Badge, Button, Popover, Separator, Sheet } from '@muraadso/ui';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

export const CartDetails = () => {
  const {
    items,
    totalItems,
    totalPrice,
    updateQuantity,
    removeItem,
    clearCart,
  } = useCart();

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(itemId);
    } else {
      updateQuantity(itemId, newQuantity);
    }
  };

  return (
    <Sheet>
      <Sheet.Trigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <ShoppingCartIcon size={20} />
          {totalItems > 0 && (
            <Badge
              variant="destructive"
              className="absolute top-0 right-0 flex size-4.5 items-center justify-center rounded-full p-0 text-[10px] ring-2 ring-background"
            >
              {totalItems > 99 ? '99+' : totalItems}
            </Badge>
          )}
        </Button>
      </Sheet.Trigger>
      <Sheet.Content className="w-96 p-0">
        <Sheet.Body title={`Cart (${items.length} items)`}>
          {items.length === 0 && (
            <div className="flex size-full flex-col items-center justify-center py-8 text-center">
              <ShoppingCartIcon className="mb-4 h-12 w-12 text-muted-foreground" />
              <p className="mb-2 text-muted-foreground">Your cart is empty</p>
              <p className="text-muted-foreground text-sm">
                Add some products to get started
              </p>
            </div>
          )}

          {items.length > 0 && (
            <div className="flex w-full flex-col divide-y divide-border">
              {items.map((item) => (
                <div key={item.id} className="py-4 last:border-b-0">
                  <div className="flex gap-3">
                    <div className="relative h-16 w-16 flex-shrink-0 overflow-hidden rounded-md border border-border">
                      {item.product.images?.[0] ? (
                        <Image
                          src={getImageUrl(item.product.images[0].path)}
                          alt=""
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="flex h-full w-full items-center justify-center bg-muted">
                          <ShoppingCartIcon className="h-6 w-6 text-muted-foreground" />
                        </div>
                      )}
                    </div>

                    <div className="min-w-0 flex-1 space-y-1">
                      <h4 className="truncate font-medium text-sm">
                        {item.product.name}
                      </h4>
                      <p className="truncate text-muted-foreground text-xs">
                        {item.variant.name}
                      </p>
                      <p className="mt-1 font-semibold text-sm">
                        {formatPrice(
                          item.variant.prices?.retailPrice * item.quantity || 0,
                        )}
                      </p>

                      <div className="mt-2 flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={() =>
                            handleQuantityChange(item.id, item.quantity - 1)
                          }
                        >
                          <MinusIcon className="h-3 w-3" />
                        </Button>
                        <span className="w-8 text-center font-medium text-sm">
                          x{item.quantity}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={() =>
                            handleQuantityChange(item.id, item.quantity + 1)
                          }
                        >
                          <PlusIcon className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="ml-2 h-6 w-6 p-0 text-destructive hover:text-destructive"
                          onClick={() => removeItem(item.id)}
                        >
                          <TrashIcon className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </Sheet.Body>
        <Sheet.Footer>
          <Link href="/checkout">
            <Button disabled={items.length === 0}>Checkout</Button>
          </Link>
        </Sheet.Footer>
      </Sheet.Content>
    </Sheet>
  );
};
