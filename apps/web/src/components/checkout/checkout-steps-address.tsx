import type { ShippingAddress } from '@/types/checkout';
import { Button, Input, Label, Select } from '@muraadso/ui';
import React from 'react';

interface AddressStepProps {
  data: ShippingAddress | undefined;
  errors: Record<string, string>;
  onUpdate: (data: { shippingAddress: ShippingAddress }) => void;
  onNext: () => void;
  onPrevious: () => void;
}

const countries = [
  { value: 'SO', label: 'Somalia' },
  { value: 'ETH', label: 'Ethiopia' },
];

export const CheckoutStepsAddress: React.FC<AddressStepProps> = ({
  data,
  errors,
  onUpdate,
  onNext,
  onPrevious,
}) => {
  const [formData, setFormData] = React.useState<ShippingAddress>({
    address: data?.address || '',
    company: data?.company || '',
    postalCode: data?.postalCode || '',
    city: data?.city || '',
    country: data?.country || '',
    state: data?.state || '',
  });

  const handleInputChange = (field: keyof ShippingAddress, value: string) => {
    const updatedData = { ...formData, [field]: value };
    setFormData(updatedData);
    onUpdate({ shippingAddress: updatedData });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onNext();
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="mb-2 font-semibold text-xl">Shipping Address</h2>
        <p className="text-muted-foreground text-sm">
          Where should we deliver your order?
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="address">
            Address <span className="text-red-500">*</span>
          </Label>
          <Input
            id="address"
            type="text"
            value={formData.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            className={errors.address ? 'border-red-500' : ''}
            placeholder="Enter your street address"
          />
          {errors.address && (
            <p className="mt-1 text-red-500 text-sm">{errors.address}</p>
          )}
        </div>

        <div>
          <Label htmlFor="company">Company</Label>
          <Input
            id="company"
            type="text"
            value={formData.company}
            onChange={(e) => handleInputChange('company', e.target.value)}
            placeholder="Company name (optional)"
          />
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <Label htmlFor="postalCode">
              Postal code <span className="text-red-500">*</span>
            </Label>
            <Input
              id="postalCode"
              type="text"
              value={formData.postalCode}
              onChange={(e) => handleInputChange('postalCode', e.target.value)}
              className={errors.postalCode ? 'border-red-500' : ''}
              placeholder="Enter postal code"
            />
            {errors.postalCode && (
              <p className="mt-1 text-red-500 text-sm">{errors.postalCode}</p>
            )}
          </div>

          <div>
            <Label htmlFor="city">
              City <span className="text-red-500">*</span>
            </Label>
            <Input
              id="city"
              type="text"
              value={formData.city}
              onChange={(e) => handleInputChange('city', e.target.value)}
              className={errors.city ? 'border-red-500' : ''}
              placeholder="Enter city"
            />
            {errors.city && (
              <p className="mt-1 text-red-500 text-sm">{errors.city}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <Label htmlFor="country">
              Country <span className="text-red-500">*</span>
            </Label>
            <Select
              value={formData.country}
              onValueChange={(value) => handleInputChange('country', value)}
            >
              <Select.Trigger
                className={errors.country ? 'border-red-500' : ''}
              >
                <Select.Value placeholder="Select country" />
              </Select.Trigger>
              <Select.Content>
                {countries.map((country) => (
                  <Select.Item key={country.value} value={country.value}>
                    {country.label}
                  </Select.Item>
                ))}
              </Select.Content>
            </Select>
            {errors.country && (
              <p className="mt-1 text-red-500 text-sm">{errors.country}</p>
            )}
          </div>

          <div>
            <Label htmlFor="state">
              State / Province <span className="text-red-500">*</span>
            </Label>
            <Input
              id="state"
              type="text"
              value={formData.state}
              onChange={(e) => handleInputChange('state', e.target.value)}
              className={errors.state ? 'border-red-500' : ''}
              placeholder="Enter state/province"
            />
            {errors.state && (
              <p className="mt-1 text-red-500 text-sm">{errors.state}</p>
            )}
          </div>
        </div>

        <div className="flex gap-4 pt-4">
          <Button type="button" variant="outline" onClick={onPrevious}>
            Back
          </Button>
          <Button type="submit">Continue to payment</Button>
        </div>
      </form>
    </div>
  );
};
