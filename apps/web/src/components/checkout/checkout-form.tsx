'use client';

import { Footer } from '@/components/layout/footer';
import { Header } from '@/components/layout/header';
import { useCheckout } from '@/hooks/useCheckout';
import Link from 'next/link';
import React from 'react';
import { CheckoutCartSummary } from './checkout-cart-summary';
import { CheckoutStepsAddress } from './checkout-steps-address';
import { CheckoutStepsCustomer } from './checkout-steps-customer';
import { CheckoutStepsPayment } from './checkout-steps-payment';
import { CheckoutStepsReview } from './checkout-steps-review';

export const CheckoutForm = () => {
  const {
    state,
    items,
    orderSummary,
    updateData,
    setCurrentStep,
    nextStep,
    previousStep,
    submitOrder,
  } = useCheckout();

  const renderStep = () => {
    switch (state.currentStep) {
      case 'customer':
        return (
          <CheckoutStepsCustomer
            data={state.data.customerInfo}
            errors={state.errors}
            onUpdate={updateData}
            onNext={nextStep}
          />
        );
      case 'address':
        return (
          <CheckoutStepsAddress
            data={state.data.shippingAddress}
            errors={state.errors}
            onUpdate={updateData}
            onNext={nextStep}
            onPrevious={previousStep}
          />
        );
      case 'payment':
        return (
          <CheckoutStepsPayment
            data={state.data.paymentMethod}
            errors={state.errors}
            onUpdate={updateData}
            onNext={nextStep}
            onPrevious={previousStep}
          />
        );
      case 'review':
        return (
          <CheckoutStepsReview
            data={state.data}
            isSubmitting={state.isSubmitting}
            onSubmit={submitOrder}
            onPrevious={previousStep}
            onEdit={setCurrentStep}
          />
        );
      default:
        return null;
    }
  };

  const renderStepIndicator = () => {
    const steps = [
      { key: 'customer', title: 'Customer', completed: false },
      { key: 'shipping', title: 'Delivery', completed: false },
      { key: 'payment', title: 'Payment', completed: false },
      { key: 'review', title: 'Review', completed: false },
    ];

    const currentStepIndex = steps.findIndex(
      (step) =>
        step.key === state.currentStep ||
        (state.currentStep === 'address' && step.key === 'customer'),
    );

    return (
      <div className="flex flex-col space-y-12">
        {steps.map((step, index) => {
          const isActive = index === currentStepIndex;
          const isCompleted = index < currentStepIndex;

          return (
            <div key={step.key} className="flex items-center">
              <div className="flex items-center">
                <div
                  className={`flex h-8 w-8 items-center justify-center rounded-full font-medium text-sm ${
                    isCompleted
                      ? 'bg-green-600 text-white'
                      : isActive
                        ? 'bg-black text-white'
                        : 'bg-gray-200 text-gray-600'
                  }`}
                >
                  {isCompleted ? '✓' : index + 1}
                </div>
                <span
                  className={`ml-2 font-medium text-sm ${
                    isActive ? 'text-black' : 'text-gray-500'
                  }`}
                >
                  {step.title}
                </span>
              </div>
              {index < steps.length - 1 && (
                <div className="mx-4 h-px flex-1 bg-gray-200" />
              )}
            </div>
          );
        })}
      </div>
    );
  };

  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="mb-4 font-bold text-2xl">Your cart is empty</h1>
            <p className="mb-6 text-muted-foreground">
              Add some products to your cart before checking out.
            </p>
            <Link
              href="/products"
              className="inline-flex items-center rounded-md bg-black px-4 py-2 text-white transition-colors hover:bg-gray-800"
            >
              Continue Shopping
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <Header />

      <div className="container mx-auto space-y-12 px-4 py-8">
        <div className="flex flex-col space-y-1">
          <h1 className="font-medium text-xl">Checkout as Guest</h1>
          <p className="text-base text-muted-foreground">
            or <span className="text-foreground underline">Login</span> for fast
            checkout
          </p>
        </div>
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* Main Content */}
          <div className="flex space-x-8 divide-x divide-border lg:col-span-2">
            {renderStepIndicator()}
            <div className="flex-1">{renderStep()}</div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <CheckoutCartSummary items={items} orderSummary={orderSummary} />
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};
