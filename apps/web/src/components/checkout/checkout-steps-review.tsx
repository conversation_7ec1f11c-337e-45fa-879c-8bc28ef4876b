'use client';

import type { CheckoutData } from '@/types/checkout';
import { Button } from '@muraadso/ui';
import Link from 'next/link';
import React, { type FormEvent } from 'react';

interface ReviewStepProps {
  data: Partial<CheckoutData>;
  isSubmitting: boolean;
  onSubmit: () => void;
  onPrevious: () => void;
  onEdit: (step: 'customer' | 'address' | 'payment') => void;
}

export const CheckoutStepsReview = ({
  data,
  isSubmitting,
  onSubmit,
  onPrevious,
  onEdit,
}: ReviewStepProps) => {
  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    onSubmit();
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="mb-2 font-semibold text-xl">Review</h2>
        <p className="text-muted-foreground text-sm">
          Please review your order details before placing your order.
        </p>
      </div>

      <div className="space-y-6">
        {/* Shipping Address */}
        <div className="rounded-lg border border-border p-4">
          <div className="mb-3 flex items-center justify-between">
            <h3 className="font-medium">Shipping Address</h3>
            <Button
              variant="link"
              size="sm"
              onClick={() => onEdit('address')}
              className="h-auto p-0 text-blue-600"
            >
              Edit
            </Button>
          </div>

          {data.shippingAddress && (
            <div className="space-y-1 text-muted-foreground text-sm">
              <p>
                {data.customerInfo?.firstName} {data.customerInfo?.lastName}
              </p>
              <p>{data.shippingAddress.address}</p>
              {data.shippingAddress.company && (
                <p>{data.shippingAddress.company}</p>
              )}
              <p>
                {data.shippingAddress.city}, {data.shippingAddress.state}{' '}
                {data.shippingAddress.postalCode}
              </p>
              <p>{data.shippingAddress.country}</p>
            </div>
          )}
        </div>

        {/* Contact Information */}
        <div className="rounded-lg border border-border p-4">
          <div className="mb-3 flex items-center justify-between">
            <h3 className="font-medium">Contact</h3>
            <Button
              variant="link"
              size="sm"
              onClick={() => onEdit('customer')}
              className="h-auto p-0 text-blue-600"
            >
              Edit
            </Button>
          </div>

          {data.customerInfo && (
            <div className="space-y-1 text-muted-foreground text-sm">
              <p>{data.customerInfo.email}</p>
              <p>{data.customerInfo.phone}</p>
            </div>
          )}
        </div>

        {/* Billing Address */}
        <div className="rounded-lg border border-border p-4">
          <div className="mb-3 flex items-center justify-between">
            <h3 className="font-medium">Billing Address</h3>
            <Button
              variant="link"
              size="sm"
              onClick={() => onEdit('address')}
              className="h-auto p-0 text-blue-600"
            >
              Edit
            </Button>
          </div>

          <p className="text-muted-foreground text-sm">
            Billing- and delivery address are the same.
          </p>
        </div>

        {/* Payment Method */}
        <div className="rounded-lg border border-border p-4">
          <div className="mb-3 flex items-center justify-between">
            <h3 className="font-medium">Payment</h3>
            <Button
              variant="link"
              size="sm"
              onClick={() => onEdit('payment')}
              className="h-auto p-0 text-blue-600"
            >
              Edit
            </Button>
          </div>

          {data.paymentMethod && (
            <div className="text-muted-foreground text-sm">
              <p>
                {data.paymentMethod.type === 'cash_on_delivery'
                  ? 'Cash on Delivery'
                  : 'Manual Payment'}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Terms and Conditions */}
      <div className="rounded-lg bg-gray-50 p-4">
        <p className="text-muted-foreground text-sm">
          By clicking the Place Order button, you confirm that you have read,
          understand and accept our{' '}
          <Link href="#" className="text-blue-600 hover:underline">
            Terms of Use
          </Link>
          ,{' '}
          <Link href="#" className="text-blue-600 hover:underline">
            Terms of Sale
          </Link>{' '}
          and{' '}
          <Link href="#" className="text-blue-600 hover:underline">
            Returns Policy
          </Link>{' '}
          <Link href="#" className="text-blue-600 hover:underline">
            Privacy Policy
          </Link>
          .
        </p>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="flex gap-4 pt-4">
          <Button type="button" variant="outline" onClick={onPrevious}>
            Back
          </Button>
          <Button type="submit" loading={isSubmitting} disabled={isSubmitting}>
            Place order
          </Button>
        </div>
      </form>
    </div>
  );
};
