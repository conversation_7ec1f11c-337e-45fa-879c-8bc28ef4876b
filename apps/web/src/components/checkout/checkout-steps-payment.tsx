import type { PaymentMethod } from '@/types/checkout';
import { CreditCardIcon } from '@muraadso/icons';
import { Button, RadioGroup } from '@muraadso/ui';
import React from 'react';

interface PaymentStepProps {
  data: PaymentMethod | undefined;
  errors: Record<string, string>;
  onUpdate: (data: { paymentMethod: PaymentMethod }) => void;
  onNext: () => void;
  onPrevious: () => void;
}

export const CheckoutStepsPayment: React.FC<PaymentStepProps> = ({
  data,
  errors,
  onUpdate,
  onNext,
  onPrevious,
}) => {
  const [selectedPayment, setSelectedPayment] = React.useState<string>(
    data?.type || '',
  );

  const handlePaymentChange = (value: string) => {
    setSelectedPayment(value);
    onUpdate({
      paymentMethod: {
        type: value as PaymentMethod['type'],
      },
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onNext();
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="mb-2 font-semibold text-xl">Payment</h2>
        <p className="text-muted-foreground text-sm">
          Choose your preferred payment method.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <RadioGroup
            value={selectedPayment}
            onValueChange={handlePaymentChange}
            className="space-y-3"
          >
            <div className="flex items-center space-x-3 rounded-lg border border-ring p-4 transition-colors hover:bg-accent">
              <RadioGroup.Item value="cash_on_delivery" id="cash_on_delivery" />
              <div className="flex flex-1 items-center space-x-3">
                <CreditCardIcon className="h-5 w-5 text-gray-600" />
                <div>
                  <label
                    htmlFor="cash_on_delivery"
                    className="cursor-pointer font-medium"
                  >
                    Cash on Delivery
                  </label>
                  <p className="text-muted-foreground text-sm">
                    Pay when your order is delivered
                  </p>
                </div>
              </div>
            </div>
          </RadioGroup>

          {errors.paymentMethod && (
            <p className="mt-2 text-red-500 text-sm">{errors.paymentMethod}</p>
          )}
        </div>

        <div className="flex gap-4 pt-4">
          <Button type="button" variant="outline" onClick={onPrevious}>
            Back
          </Button>
          <Button type="submit" disabled={!selectedPayment}>
            Continue to review
          </Button>
        </div>
      </form>
    </div>
  );
};
