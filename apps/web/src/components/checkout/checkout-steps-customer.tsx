import type { CustomerInfo } from '@/types/checkout';
import { Button, Input, Label } from '@muraadso/ui';
import React from 'react';

interface CustomerInfoStepProps {
  data: CustomerInfo | undefined;
  errors: Record<string, string>;
  onUpdate: (data: { customerInfo: CustomerInfo }) => void;
  onNext: () => void;
}

export const CheckoutStepsCustomer: React.FC<CustomerInfoStepProps> = ({
  data,
  errors,
  onUpdate,
  onNext,
}) => {
  const [formData, setFormData] = React.useState<CustomerInfo>({
    firstName: data?.firstName || '',
    lastName: data?.lastName || '',
    email: data?.email || '',
    phone: data?.phone || '',
  });

  const handleInputChange = (field: keyof CustomerInfo, value: string) => {
    const updatedData = { ...formData, [field]: value };
    setFormData(updatedData);
    onUpdate({ customerInfo: updatedData });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onNext();
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="mb-2 font-semibold text-xl">Contact Information</h2>
        <p className="text-muted-foreground text-sm">
          Please provide your contact information for order updates.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <Label htmlFor="firstName">
              First name <span className="text-red-500">*</span>
            </Label>
            <Input
              id="firstName"
              type="text"
              value={formData.firstName}
              onChange={(e) => handleInputChange('firstName', e.target.value)}
              className={errors.firstName ? 'border-red-500' : ''}
              placeholder="Enter your first name"
            />
            {errors.firstName && (
              <p className="mt-1 text-red-500 text-sm">{errors.firstName}</p>
            )}
          </div>

          <div>
            <Label htmlFor="lastName">
              Last name <span className="text-red-500">*</span>
            </Label>
            <Input
              id="lastName"
              type="text"
              value={formData.lastName}
              onChange={(e) => handleInputChange('lastName', e.target.value)}
              className={errors.lastName ? 'border-red-500' : ''}
              placeholder="Enter your last name"
            />
            {errors.lastName && (
              <p className="mt-1 text-red-500 text-sm">{errors.lastName}</p>
            )}
          </div>
        </div>

        <div>
          <Label htmlFor="email">
            Email <span className="text-red-500">*</span>
          </Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            className={errors.email ? 'border-red-500' : ''}
            placeholder="Enter your email address"
          />
          {errors.email && (
            <p className="mt-1 text-red-500 text-sm">{errors.email}</p>
          )}
        </div>

        <div>
          <Label htmlFor="phone">
            Phone <span className="text-red-500">*</span>
          </Label>
          <Input
            id="phone"
            type="tel"
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            className={errors.phone ? 'border-red-500' : ''}
            placeholder="Enter your phone number"
          />
          {errors.phone && (
            <p className="mt-1 text-red-500 text-sm">{errors.phone}</p>
          )}
        </div>

        <div className="pt-4">
          <Button type="submit" className="w-full md:w-auto">
            Continue to address
          </Button>
        </div>
      </form>
    </div>
  );
};
