import { formatPrice, getImageUrl } from '@/lib/utils';
import type { CartItem } from '@/types/cart';
import type { OrderSummary } from '@/types/checkout';
import { ShoppingCartIcon } from '@muraadso/icons';
import { Button, Separator } from '@muraadso/ui';
import Image from 'next/image';
import React from 'react';

interface CartSummaryProps {
  items: CartItem[];
  orderSummary: OrderSummary;
  showPromotionCode?: boolean;
}

export const CheckoutCartSummary = ({
  items,
  orderSummary,
  showPromotionCode = true,
}: CartSummaryProps) => {
  return (
    <div className="rounded-lg bg-gray-50 p-6">
      <h2 className="mb-4 font-semibold text-lg">In your Cart</h2>

      {/* Cart Items */}
      <div className="mb-6 space-y-4">
        {items.map((item) => (
          <div key={item.id} className="flex items-center gap-3">
            <div className="relative h-12 w-12 flex-shrink-0 overflow-hidden rounded-md border border-border">
              {item.product.images?.[0] ? (
                <Image
                  src={getImageUrl(item.product.images[0].path)}
                  alt=""
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="flex h-full w-full items-center justify-center bg-muted">
                  <ShoppingCartIcon className="h-4 w-4 text-muted-foreground" />
                </div>
              )}
            </div>

            <div className="min-w-0 flex-1">
              <h4 className="truncate font-medium text-sm">
                {item.product.name}
              </h4>
              <p className="truncate text-muted-foreground text-xs">
                Variant: {item.variant.name}
              </p>
            </div>

            <div className="text-right">
              <p className="text-sm">
                {item.quantity}x{' '}
                {formatPrice(item.variant.prices?.retailPrice || 0)}
              </p>
              <p className="font-medium text-sm">
                {formatPrice(
                  (item.variant.prices?.retailPrice || 0) * item.quantity,
                )}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* Promotion Code */}
      {showPromotionCode && (
        <div className="mb-6">
          <Button variant="link" className="h-auto p-0 text-blue-600 text-sm">
            Add Promotion Code(s)
          </Button>
        </div>
      )}

      <Separator className="mb-4" />

      {/* Order Summary */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>Subtotal (excl. shipping and taxes)</span>
          <span>{formatPrice(orderSummary.subtotal)}</span>
        </div>

        <div className="flex justify-between text-sm">
          <span>Shipping</span>
          <span>{formatPrice(orderSummary.shipping)}</span>
        </div>

        <div className="flex justify-between text-sm">
          <span>Taxes</span>
          <span>{formatPrice(orderSummary.taxes)}</span>
        </div>

        <Separator className="my-3" />

        <div className="flex justify-between font-semibold text-lg">
          <span>Total</span>
          <span>{formatPrice(orderSummary.total)}</span>
        </div>
      </div>
    </div>
  );
};
