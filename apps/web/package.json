{"name": "web", "version": "0.9.2-alpha", "private": true, "scripts": {"dev": "next dev --turbopack -p 7721", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "opennextjs-cloudflare build && wrangler deploy", "preview": "pnpm run build && opennextjs-cloudflare preview", "cf-typegen": "wrangler types --env-interface CloudflareEnv ./cloudflare-env.d.ts"}, "dependencies": {"@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^5.1.1", "@muraadso/api": "workspace:*", "@muraadso/icons": "workspace:*", "@muraadso/models": "workspace:*", "@muraadso/ui": "workspace:*", "@tanstack/react-query": "^5.80.2", "@tanstack/react-table": "^8.21.3", "date-fns": "^4.1.0", "motion": "^12.16.0", "next": "15.3.3", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "recharts": "^3.1.0", "zod": "catalog:", "zustand": "^5.0.5"}, "devDependencies": {"@muraadso/tsconfig": "workspace:*", "@muraadso/ui-preset": "workspace:*", "@opennextjs/cloudflare": "^1.1.0", "@tailwindcss/postcss": "^4", "@tanstack/react-query-devtools": "^5.80.2", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "typescript": "^5", "wrangler": "catalog:"}}