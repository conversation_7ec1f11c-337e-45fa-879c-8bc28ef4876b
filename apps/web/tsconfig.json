{"extends": "@muraadso/tsconfig/next.json", "compilerOptions": {"paths": {"@/app/*": ["./src/app/*"], "@/components/*": ["./src/components/*"], "@/config/*": ["./src/config/*"], "@/contexts/*": ["./src/contexts/*"], "@/hooks/*": ["./src/hooks/*"], "@/lib/*": ["./src/lib/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"]}}, "include": ["**/*.ts", "**/*.tsx", "next-env.d.ts", "next.config.ts", ".next/types/**/*.ts"], "exclude": ["node_modules"]}