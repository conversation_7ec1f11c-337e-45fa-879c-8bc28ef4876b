import type { NavItem, NavMenu, NavigationConfig } from '@/types/navigation';

export class NavigationManager {
  private config: NavigationConfig;
  private pathRegistry: Map<string, string[]>;

  constructor(config: NavigationConfig) {
    this.config = config;
    this.pathRegistry = new Map();
    this.initializePathRegistry();
  }

  private initializePathRegistry(): void {
    for (const menu of this.config.mainMenu) {
      for (const item of menu.items) {
        const pathSegments = item.path.split('/').filter(Boolean);
        const firstSegment = pathSegments[0];
        if (firstSegment) {
          this.pathRegistry.set(
            firstSegment,
            Object.keys(this.config.subMenus).filter((key) =>
              key.startsWith(`/${pathSegments[0]}`),
            ),
          );
        }
      }
    }
  }

  public getMainMenu(): NavMenu[] {
    return this.config.mainMenu;
  }

  public getSubNavItems(path: string): NavItem[] {
    const pathSegment = path.split('/')[1];
    if (!pathSegment) return [];
    const registeredPaths = this.pathRegistry.get(pathSegment);

    if (!registeredPaths) return [];

    for (const regPath of registeredPaths) {
      if (path.startsWith(regPath)) {
        return this.config.subMenus[regPath] || [];
      }
    }

    return [];
  }

  public isPathActive(
    itemPath: string,
    currentPath: string,
    onlyCurrent: boolean,
  ): boolean {
    if (itemPath === '/') return currentPath === '/';
    if (onlyCurrent) return currentPath === itemPath;

    const itemSegments = itemPath.split('/').filter(Boolean);
    const currentSegments = currentPath.split('/').filter(Boolean);

    // Check if the current path starts with the item path
    for (let i = 0; i < itemSegments.length; i++) {
      if (itemSegments[i] !== currentSegments[i]) return false;
    }

    // Check if this path has registered subpaths
    const mainSegment = itemSegments[0];
    if (!mainSegment) return false;
    const registeredPaths = this.pathRegistry.get(mainSegment);

    return Boolean(
      registeredPaths?.some((path) => currentPath.startsWith(path)),
    );
  }

  public getActivePathInfo(currentPath: string): NavItem {
    // Collect all navigation items in a flat array
    const allItems: NavItem[] = [];
    for (const menu of this.config.mainMenu) {
      allItems.push(...menu.items);
    }

    // First try exact match
    const exactMatch = allItems.find((item) => item.path === currentPath);
    if (exactMatch) {
      return exactMatch;
    }

    // Then try prefix match for non-root paths, prioritizing longer matches
    if (currentPath !== '/') {
      const prefixMatches = allItems
        .filter(
          (item) => item.path !== '/' && currentPath.startsWith(item.path),
        )
        .sort((a, b) => b.path.length - a.path.length); // Sort by path length descending

      const firstPrefixMatch = prefixMatches[0];
      if (firstPrefixMatch) {
        return firstPrefixMatch;
      }
    }

    // Default to dashboard
    return (
      this.config.mainMenu[0]?.items[0] ?? {
        title: 'Dashboard',
        description: 'Overview of your business metrics and insights',
        icon: () => null,
        path: '/',
      }
    );
  }
}
