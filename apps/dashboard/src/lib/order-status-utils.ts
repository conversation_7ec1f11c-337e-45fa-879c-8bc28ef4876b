import {
  type Order,
  type OrderStatus,
  type QchStatus,
  type ServiceType,
  orderStatus,
  qchStatus,
} from '@muraadso/models/orders';

export interface StatusOption {
  value: OrderStatus;
  label: string;
  disabled: boolean;
  disabledReason?: string;
}

export interface QchStatusOption {
  value: QchStatus;
  label: string;
  disabled: boolean;
  disabledReason?: string;
}

export const STATUS_LABELS: Record<OrderStatus, string> = {
  pending: 'Pending',
  confirmed: 'Confirm',
  completed: 'Complete Order',
  returned: 'Return',
  canceled: 'Cancel',
};

export const QCH_STATUS_LABELS: Record<QchStatus, string> = {
  none: 'None',
  processing: 'Accept',
  passed: 'Set Passed',
  failed: 'Set Failed',
};

export function shouldShowQchOptions(serviceType: ServiceType): boolean {
  return serviceType !== 'retail';
}

export function canTransitionToStatus(
  order: Order,
  targetStatus: OrderStatus,
  pageContext?: {
    canUpdateOrderStatus: boolean;
    pageType: 'orders' | 'platform' | 'quality-check';
  },
): { allowed: boolean; reason?: string } {
  const { status: currentStatus, qchStatus, serviceType } = order;

  // Can't transition to the same status
  if (currentStatus === targetStatus) {
    return { allowed: false, reason: 'Already in this status' };
  }

  if (currentStatus === 'canceled') {
    return {
      allowed: false,
      reason: 'Already canceled',
    };
  }

  // For non-retail service types, apply quality check logic
  if (serviceType !== 'retail') {
    if (targetStatus === 'completed' || targetStatus === 'returned') {
      // Check quality check status requirements
      if (qchStatus !== 'passed') {
        return {
          allowed: false,
          reason: 'Quality check must be passed first',
        };
      }
    }

    // Platform page specific restrictions
    if (pageContext?.pageType === 'platform') {
      // Cannot complete or return orders if QCH status is not passed
      if (
        (targetStatus === 'completed' || targetStatus === 'returned') &&
        qchStatus !== 'passed'
      ) {
        return {
          allowed: false,
          reason:
            'Quality check must pass before completing or returning order',
        };
      }
    }

    // Additional business logic: prevent certain transitions
    if (currentStatus === 'completed' && targetStatus !== 'returned') {
      return {
        allowed: false,
        reason: 'Completed orders can only be returned',
      };
    }

    if (currentStatus === 'returned' && targetStatus !== 'pending') {
      return {
        allowed: false,
        reason: 'Returned orders can only be reset to pending',
      };
    }
  } else {
    if (
      ['completed', 'canceled', 'returned'].includes(currentStatus) &&
      targetStatus === 'confirmed'
    ) {
      return {
        allowed: false,
        reason: 'Cannot confirm canceled, completed, or returned orders',
      };
    }

    if (currentStatus !== 'confirmed' && targetStatus === 'completed') {
      return {
        allowed: false,
        reason: 'Only confirmed orders can be completed',
      };
    }

    if (currentStatus !== 'completed' && targetStatus === 'returned') {
      return {
        allowed: false,
        reason: 'Only completed orders can be returned',
      };
    }

    if (
      ['completed', 'returned'].includes(currentStatus) &&
      targetStatus === 'canceled'
    ) {
      return {
        allowed: false,
        reason: 'Cannot cancel completed or returned orders',
      };
    }
  }

  return { allowed: true };
}

export function canUpdateQchStatus(
  order: Order,
  targetQchStatus: QchStatus,
): { allowed: boolean; reason?: string } {
  const { qchStatus: currentQchStatus, serviceType } = order;

  // QCH status updates don't apply to retail
  if (serviceType === 'retail') {
    return {
      allowed: false,
      reason: 'Quality check not applicable for retail orders',
    };
  }

  // Can't transition to the same QCH status
  if (currentQchStatus === targetQchStatus) {
    return { allowed: false, reason: 'Already in this quality check status' };
  }

  // Shouldn't update QCH status if already passed or failed
  if (['passed', 'failed'].includes(currentQchStatus)) {
    return { allowed: false, reason: 'Already passed or failed' };
  }

  return { allowed: true };
}

export function getAvailableStatusOptions(
  order: Order,
  pageContext?: {
    canUpdateOrderStatus: boolean;
    pageType: 'orders' | 'platform' | 'quality-check';
  },
): StatusOption[] {
  // If page doesn't allow order status updates, return empty array
  if (pageContext && !pageContext.canUpdateOrderStatus) {
    return [];
  }

  return orderStatus
    .filter((status) => status !== 'pending')
    .map((status) => {
      const validation = canTransitionToStatus(order, status, pageContext);
      return {
        value: status,
        label: STATUS_LABELS[status],
        disabled: !validation.allowed,
        disabledReason: validation.reason,
      };
    });
}

export function getAvailableQchStatusOptions(
  order: Order,
  pageContext?: {
    canUpdateQchStatus: boolean;
    pageType: 'orders' | 'platform' | 'quality-check';
  },
): QchStatusOption[] {
  if (!shouldShowQchOptions(order.serviceType)) {
    return [];
  }

  // If page doesn't allow QCH status updates, return empty array
  if (pageContext && !pageContext.canUpdateQchStatus) {
    return [];
  }

  return qchStatus
    .filter((status) => status !== 'none') // Don't show current QCH status
    .map((qchStatus) => {
      const validation = canUpdateQchStatus(order, qchStatus);
      return {
        value: qchStatus,
        label: QCH_STATUS_LABELS[qchStatus],
        disabled: !validation.allowed,
        disabledReason: validation.reason,
      };
    });
}

export function shouldTriggerFulfillment(targetStatus: OrderStatus): boolean {
  return targetStatus === 'completed';
}

export function getCurrentStatusInfo(order: Order) {
  return {
    status: {
      value: order.status,
      label: STATUS_LABELS[order.status],
    },
    qchStatus: {
      value: order.qchStatus,
      label: QCH_STATUS_LABELS[order.qchStatus],
      visible: shouldShowQchOptions(order.serviceType),
    },
  };
}

// Platform page specific QCH status display labels
export const QCH_PROGRESS_LABELS: Record<QchStatus, string> = {
  none: 'Not Started',
  processing: 'Under Review',
  passed: 'Passed',
  failed: 'Failed',
};

export function getQchProgressInfo(order: Order) {
  return {
    value: order.qchStatus,
    label: QCH_PROGRESS_LABELS[order.qchStatus],
    visible: shouldShowQchOptions(order.serviceType),
    variant: getQchStatusVariant(order.qchStatus),
  };
}

export function getQchStatusVariant(
  qchStatus: QchStatus,
): 'default' | 'secondary' | 'destructive' | 'outline' {
  switch (qchStatus) {
    case 'passed':
      return 'default';
    case 'failed':
      return 'destructive';
    case 'processing':
      return 'secondary';
    default:
      return 'outline';
  }
}
