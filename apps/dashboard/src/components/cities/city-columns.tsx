'use client';

import { useModal } from '@/components/modal';
import { useDeleteCity } from '@muraadso/api/hooks';
import { MoreHorizontalIcon } from '@muraadso/icons';
import type { City } from '@muraadso/models/cities';
import { Button, Checkbox, DropdownMenu } from '@muraadso/ui';
import type { ColumnDef } from '@tanstack/react-table';
import { formatDate } from 'date-fns';

export function useCityColumns(): ColumnDef<City>[] {
  const { mutateAsync: deleteCity } = useDeleteCity();
  const modal = useModal();

  return [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => (
        <span className="font-medium">{row.getValue('name')}</span>
      ),
    },
    {
      accessorKey: 'country',
      header: 'Country',
      cell: ({ row }) => {
        const city = row.original;
        return city.country ? (
          <div className="flex items-center gap-2">
            <span className="text-sm">{city.country.emoji}</span>
            <span>{city.country.name}</span>
          </div>
        ) : (
          <span className="text-muted-foreground">-</span>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      cell: ({ row }) => {
        const date = row.getValue('createdAt') as string;
        return <span>{formatDate(new Date(date), 'MMM dd, yyyy')}</span>;
      },
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      cell: ({ row }) => {
        const date = row.getValue('updatedAt') as string;
        return <span>{formatDate(new Date(date), 'MMM dd, yyyy')}</span>;
      },
    },
    {
      id: 'actions',
      enableHiding: false,
      cell: ({ row }) => {
        const city = row.original;
        return (
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontalIcon className="size-5 text-muted-foreground opacity-0 group-hover:opacity-100" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content align="end">
              <DropdownMenu.Label>Actions</DropdownMenu.Label>
              <DropdownMenu.Item
                onClick={() => modal.open('cities', city)}
              >
                Edit
              </DropdownMenu.Item>
              <DropdownMenu.Item
                onClick={async () => {
                  try {
                    await deleteCity(city.id);
                  } catch (_) {}
                }}
              >
                Delete
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu>
        );
      },
    },
  ];
}
