'use client';

import { DataTable } from '@/components/data-table';
import type { City } from '@muraadso/models/cities';
import { useCityColumns } from './city-columns';

interface CityTableProps {
  cities: City[];
}

export const CityTable = ({ cities }: CityTableProps) => {
  const columns = useCityColumns();

  return (
    <DataTable
      columns={columns}
      data={cities}
      enablePagination={false}
    />
  );
};
