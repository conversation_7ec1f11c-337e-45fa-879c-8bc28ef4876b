import { TrashIcon, UploadIcon } from '@muraadso/icons';
import { cn } from '@muraadso/ui/utils';
import React from 'react';

export type Image = {
  id: string;
  url: string;
  file?: File;
  isExisting?: boolean;
};

interface ImagePickerProps {
  multiple?: boolean;
  value: Image[];
  onChange: (images: Image[]) => void;
  onDeleteExisting?: (imageToDelete: Image) => Promise<boolean> | boolean;
  maxImages?: number;
  maxFileSize?: number;
  acceptedTypes?: string[];
  containClassName?: string;
}

export const ImagePicker: React.FC<ImagePickerProps> = ({
  multiple = false,
  value = [],
  onChange,
  onDeleteExisting,
  maxImages = 24,
  maxFileSize = 1024 * 1024, // 1MB
  acceptedTypes = ['image/png', 'image/jpeg', 'image/gif'],
  containClassName,
}) => {
  const inputId = React.useId();
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleFileChange = React.useCallback(
    async (files: File[]) => {
      if (files.length === 0) return;

      const validFiles: File[] = [];
      for (const file of files) {
        if (file.size > maxFileSize) {
          console.warn(
            `File ${file.name} exceeds maximum size of ${maxFileSize} bytes`,
          );
          continue;
        }
        if (!acceptedTypes.includes(file.type)) {
          console.warn(`File ${file.name} has unsupported type ${file.type}`);
          continue;
        }
        validFiles.push(file);
      }

      if (validFiles.length === 0) return;

      const newImages: Image[] = validFiles.map((file) => ({
        id: crypto.randomUUID(),
        url: URL.createObjectURL(file),
        file: file,
        isExisting: false,
      }));

      let updatedImages: Image[];

      if (multiple) {
        updatedImages = [...value, ...newImages];
        if (updatedImages.length > maxImages) {
          const kept = updatedImages.slice(0, maxImages);
          const removed = updatedImages.slice(maxImages);

          for (const img of removed) {
            if (img.file) {
              URL.revokeObjectURL(img.url);
            }
          }
          updatedImages = kept;
        }
      } else {
        for (const img of value) {
          if (img.file) {
            URL.revokeObjectURL(img.url);
          }
        }
        updatedImages = newImages.slice(0, 1);
      }

      onChange(updatedImages);

      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    },
    [value, maxFileSize, maxImages, acceptedTypes, multiple, onChange],
  );

  const handleInputChange = React.useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(event.target.files ?? []);
      handleFileChange(files);
    },
    [handleFileChange],
  );

  const handleRemoveImage = React.useCallback(
    async (imageToRemove: Image) => {
      // Handle existing image deletion
      if (imageToRemove.isExisting && onDeleteExisting) {
        try {
          const confirmed = await onDeleteExisting(imageToRemove);
          if (!confirmed) return;
        } catch (error) {
          console.error('Failed to delete existing image:', error);
          return;
        }
      } else if (!imageToRemove.isExisting) {
        // Handle new upload deletion
        if (!window.confirm('Are you sure you want to delete this image?')) {
          return;
        }
      }

      // Clean up blob URL if it's a file
      if (imageToRemove.file) {
        URL.revokeObjectURL(imageToRemove.url);
      }

      const updatedImages = value.filter((img) => img.id !== imageToRemove.id);
      onChange(updatedImages);
    },
    [value, onDeleteExisting, onChange],
  );

  const handleDrop = React.useCallback(
    (event: React.DragEvent<HTMLLabelElement>) => {
      event.preventDefault();
      event.stopPropagation();
      event.currentTarget.classList.remove('border-blue-500', 'bg-blue-50');
      const droppedFiles = Array.from(event.dataTransfer.files);
      handleFileChange(droppedFiles);
    },
    [handleFileChange],
  );

  const handleDragOver = React.useCallback(
    (event: React.DragEvent<HTMLLabelElement>) => {
      event.preventDefault();
      event.stopPropagation();
      event.currentTarget.classList.add('border-blue-500', 'bg-blue-50');
    },
    [],
  );

  const handleDragLeave = React.useCallback(
    (event: React.DragEvent<HTMLLabelElement>) => {
      event.preventDefault();
      event.stopPropagation();
      event.currentTarget.classList.remove('border-blue-500', 'bg-blue-50');
    },
    [],
  );

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return Number.parseFloat((bytes / k ** i).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="mx-auto w-full">
      <label
        htmlFor={inputId}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <div
          className={cn(
            'relative w-full cursor-pointer rounded-lg border-[1.5px] border-input border-dashed py-6 text-center transition-colors duration-300 hover:border-primary focus:outline-none',
            containClassName,
          )}
        >
          <div className="flex size-full flex-col items-center justify-center">
            <UploadIcon className="size-6 text-muted-foreground" />
            <p className="mt-3 text-muted-foreground text-sm">
              <span className="font-medium text-primary">Click to upload</span>{' '}
              or drag and drop
            </p>
            <p className="mt-1.5 text-muted-foreground text-xs">
              {acceptedTypes
                .map((type) => type.split('/')[1]?.toUpperCase())
                .join(', ')}{' '}
              up to {formatFileSize(maxFileSize)}
              {multiple && ` (max ${maxImages} images)`}
            </p>
          </div>
          <input
            ref={fileInputRef}
            id={inputId}
            type="file"
            multiple={multiple}
            accept={acceptedTypes.join(', ')}
            onChange={handleInputChange}
            className="absolute inset-0 size-full cursor-pointer opacity-0"
          />
        </div>
      </label>

      {value.length > 0 && (
        <div className="mt-4">
          <h3 className="mb-2 text-muted-foreground text-sm">
            Previews ({value.length}
            {multiple ? `/${maxImages}` : ''}):
          </h3>
          <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
            {value.map((image) => (
              <div key={image.id} className="group relative h-32 w-full">
                <img
                  src={image.url}
                  alt="Preview"
                  className="size-full rounded-md object-cover shadow-md"
                />
                <button
                  type="button"
                  onClick={() => handleRemoveImage(image)}
                  className="absolute top-1 right-1 rounded-full bg-destructive/75 p-1 transition-colors focus:outline-none group-hover:bg-destructive"
                >
                  <TrashIcon className="size-4 text-destructive-foreground" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
