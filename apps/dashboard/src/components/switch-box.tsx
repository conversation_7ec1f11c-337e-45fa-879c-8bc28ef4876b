import { Form, Switch } from '@muraadso/ui';
import React from 'react';
import type { ControllerProps, FieldPath, FieldValues } from 'react-hook-form';

type SwitchBoxProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = {
  title: string;
  description: string;
  onCheckedChange?: (value: boolean) => void;
} & Omit<ControllerProps<TFieldValues, TName>, 'render'>;

export const SwitchBox = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  title,
  description,
  onCheckedChange,
  ...props
}: SwitchBoxProps<TFieldValues, TName>) => {
  return (
    <Form.Field
      {...props}
      render={({ field: { value, onChange, ...field } }) => (
        <Form.Item>
          <div className="relative flex w-full space-x-6 rounded-lg border border-border p-4 shadow-black/5 shadow-sm transition-shadow">
            <Form.Control>
              <Switch
                {...field}
                checked={value}
                onCheckedChange={(e) => {
                  onCheckedChange?.(e);
                  onChange(e);
                }}
                className="mt-1"
              />
            </Form.Control>

            <div className="flex flex-col space-y-1">
              <Form.Label>{title}</Form.Label>
              <Form.Description className="text-sm">
                {description}
              </Form.Description>
            </div>
          </div>
        </Form.Item>
      )}
    />
  );
};
