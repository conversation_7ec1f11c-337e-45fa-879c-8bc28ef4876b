'use client';

import { useModal } from '@/components/modal';
import type { Brand } from '@muraadso/models/brands';
import type { Category } from '@muraadso/models/categories';
import type { Country } from '@muraadso/models/countries';
import type { City } from '@muraadso/models/cities';
import type { District } from '@muraadso/models/districts';
import type { Location } from '@muraadso/models/locations';
import type { Order } from '@muraadso/models/orders';
import type { Product } from '@muraadso/models/products';
import dynamic from 'next/dynamic';
import { Fragment } from 'react';

const CategoriesForm = dynamic(
  () =>
    import('@/components/forms/categories').then((mod) => mod.CategoriesForm),
  { ssr: false },
);

const BrandsForm = dynamic(
  () => import('@/components/forms/brands').then((mod) => mod.BrandsForm),
  { ssr: false },
);

const LocationsForm = dynamic(
  () => import('@/components/forms/locations').then((mod) => mod.LocationsForm),
  { ssr: false },
);

const CountriesForm = dynamic(
  () => import('@/components/forms/countries').then((mod) => mod.CountriesForm),
  { ssr: false },
);

const CitiesForm = dynamic(
  () => import('@/components/forms/cities').then((mod) => mod.CitiesForm),
  { ssr: false },
);

const DistrictsForm = dynamic(
  () => import('@/components/forms/districts').then((mod) => mod.DistrictsForm),
  { ssr: false },
);

const ProductCreateForm = dynamic(
  () =>
    import('@/components/products/product-create/product-create-form').then(
      (mod) => mod.ProductCreateForm,
    ),
  { ssr: false },
);

const ProductEditForm = dynamic(
  () =>
    import('@/components/products/product-edit/product-edit-form').then(
      (mod) => mod.ProductEditForm,
    ),
  { ssr: false },
);

const OrderCreateForm = dynamic(
  () =>
    import('@/components/orders/order-create/order-create-form').then(
      (mod) => mod.OrderCreateForm,
    ),
  { ssr: false },
);

const OrderEditForm = dynamic(
  () =>
    import('@/components/orders/order-edit/order-edit-form').then(
      (mod) => mod.OrderEditForm,
    ),
  { ssr: false },
);

const CustomersCreateForm = dynamic(
  () =>
    import('@/components/customers/customer-create/customer-create-form').then(
      (mod) => mod.CustomerCreateForm,
    ),
  { ssr: false },
);

const SysopsInviteForm = dynamic(
  () =>
    import('@/components/sysops/sysop-invite-form').then(
      (mod) => mod.SysopInviteForm,
    ),
  { ssr: false },
);

export const ManagedModal = () => {
  const { modals, close } = useModal();

  return (
    <>
      {modals.map((modal) => {
        const { id, view, payload } = modal;

        return (
          <Fragment key={id}>
            {view === 'categories' && (
              <CategoriesForm
                open={true}
                onClose={(result) => close(id, result)}
                payload={payload as Category}
              />
            )}

            {view === 'brands' && (
              <BrandsForm
                open={true}
                onClose={(result) => close(id, result)}
                payload={payload as Brand}
              />
            )}

            {view === 'locations' && (
              <LocationsForm
                open={true}
                onClose={(result) => close(id, result)}
                payload={payload as Location}
              />
            )}

            {view === 'countries' && (
              <CountriesForm
                open={true}
                onClose={(result) => close(id, result)}
                payload={payload as Country}
              />
            )}

            {view === 'cities' && (
              <CitiesForm
                open={true}
                onClose={(result) => close(id, result)}
                payload={payload as City}
              />
            )}

            {view === 'districts' && (
              <DistrictsForm
                open={true}
                onClose={(result) => close(id, result)}
                payload={payload as District}
              />
            )}

            {view === 'products' &&
              (!payload ? (
                <ProductCreateForm
                  open={true}
                  onClose={(result) => close(id, result)}
                />
              ) : (
                <ProductEditForm
                  open={true}
                  onClose={(result) => close(id, result)}
                  payload={payload as Product}
                />
              ))}

            {view === 'orders' &&
              (!payload ? (
                <OrderCreateForm open={true} onClose={() => close(id, null)} />
              ) : (
                <OrderEditForm
                  open={true}
                  onClose={() => close(id, null)}
                  order={payload as Order}
                />
              ))}

            {view === 'customers' && (
              <CustomersCreateForm
                open={true}
                onClose={(result) => close(id, result)}
              />
            )}

            {view === 'sysops' && (
              <SysopsInviteForm open={true} onClose={() => close(id, null)} />
            )}
          </Fragment>
        );
      })}
    </>
  );
};
