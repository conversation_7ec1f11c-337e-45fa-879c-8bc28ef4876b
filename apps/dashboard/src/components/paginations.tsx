'use client';

import {
  ChevronLeftDoubleIcon,
  ChevronLeftIcon,
  ChevronRightDoubleIcon,
  ChevronRightIcon,
} from '@muraadso/icons';
import { Button, Select } from '@muraadso/ui';
import { parseAsInteger, useQueryState } from 'nuqs';

type PaginationProps = {
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
};

export const Pagination = ({
  totalPages,
  hasNextPage,
  hasPreviousPage,
}: PaginationProps) => {
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1));
  const [pageSize, setPageSize] = useQueryState(
    'pageSize',
    parseAsInteger.withDefault(10),
  );

  return (
    <div className="flex items-center justify-between space-x-2 py-4">
      <div className="flex items-center space-x-2">
        <p className="text-muted-foreground text-sm">Rows per page</p>
        <Select
          value={String(pageSize)}
          onValueChange={(value) => {
            setPageSize(Number(value));
            setPage(1);
          }}
        >
          <Select.Trigger className="h-9 w-[58px] px-2">
            <Select.Value placeholder="Select" />
          </Select.Trigger>
          <Select.Content className="min-w-20">
            {[10, 20, 30, 50].map((size) => (
              <Select.Item key={size} value={String(size)}>
                {size}
              </Select.Item>
            ))}
          </Select.Content>
        </Select>
      </div>

      <div className="flex items-center space-x-6 lg:space-x-8">
        <div className="flex w-[100px] items-center justify-center text-muted-foreground text-sm">
          Page {page} of {totalPages}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => setPage(1)}
            disabled={!hasPreviousPage}
          >
            <ChevronLeftDoubleIcon className="size-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => setPage(page - 1)}
            disabled={!hasPreviousPage}
          >
            <ChevronLeftIcon className="size-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => setPage(page + 1)}
            disabled={!hasNextPage}
          >
            <ChevronRightIcon className="size-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => setPage(totalPages)}
            disabled={!hasNextPage}
          >
            <ChevronRightDoubleIcon className="size-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};
