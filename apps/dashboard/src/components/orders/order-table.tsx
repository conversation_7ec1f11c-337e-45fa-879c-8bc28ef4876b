'use client';

import { DataTable } from '@/components/data-table';
import { useModal } from '@/components/modal';
import { printOrder } from '@/utils/print-order';
import type { Order } from '@muraadso/models/orders';
import { useOrderColumns } from './order-columns';

interface OrderTableProps {
  orders: Order[];
  showQchStatus?: boolean;
}

export const OrderTable = ({
  orders,
  showQchStatus = false,
}: OrderTableProps) => {
  const { open } = useModal();
  const columns = useOrderColumns(
    (order) => open('orders', order),
    printOrder,
    showQchStatus,
  );

  return <DataTable columns={columns} data={orders} />;
};
