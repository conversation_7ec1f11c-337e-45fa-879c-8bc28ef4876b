import { formatPrice } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  type FulfillOrderPayload,
  useFulfillOrder,
  useGetInventory,
  useGetLocations,
  useUpdateOrderStatus,
} from '@muraadso/api/hooks';
import type { Order } from '@muraadso/models/orders';
import {
  Button,
  Checkbox,
  Dialog,
  Form,
  Input,
  Select,
  Table,
} from '@muraadso/ui';
import type React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod/v4';

const fulfillmentItemSchema = z.object({
  orderItemId: z.string(),
  variantId: z.string(),
  selected: z.boolean(),
  fulfillQuantity: z.number().min(0),
  locationId: z.string().min(1, 'Location is required'),
  maxQuantity: z.number(),
});

const fulfillmentFormSchema = z.object({
  items: z.array(fulfillmentItemSchema),
});

type FulfillmentFormData = z.infer<typeof fulfillmentFormSchema>;

type FulfillmentDialogProps = {
  open: boolean;
  onClose: () => void;
  payload: Order;
};

export const OrderEditFulfillment = ({
  open,
  onClose,
  payload,
}: FulfillmentDialogProps) => {
  const { locations } = useGetLocations();
  const { mutateAsync: fulfillOrder, isPending: isFulfilling } =
    useFulfillOrder();
  const { mutateAsync: updateOrderStatus } = useUpdateOrderStatus();

  const { data: inventoryData } = useGetInventory({
    pageSize: 1000, // Get all inventory items
  });

  const form = useForm<FulfillmentFormData>({
    resolver: zodResolver(fulfillmentFormSchema),
    defaultValues: {
      items: payload.items.map((item) => ({
        orderItemId: item.id,
        variantId: item.variantId || '',
        selected: false,
        fulfillQuantity: 0,
        locationId: '',
        maxQuantity: item.quantity,
      })),
    },
  });

  const watchedItems = form.watch('items');

  // Helper function to get inventory for a specific variant and location
  const getInventoryForVariant = (variantId: string, locationId: string) => {
    if (!inventoryData?.inventory) return 0;

    const inventoryItem = inventoryData.inventory.find(
      (item) => item.variantId === variantId && item.locationId === locationId,
    );
    return inventoryItem?.quantity || 0;
  };

  const handleSelectAll = (checked: boolean) => {
    const updatedItems = watchedItems.map((item) => ({
      ...item,
      selected: checked,
      fulfillQuantity: checked ? item.maxQuantity : 0,
    }));
    form.setValue('items', updatedItems);
  };

  // Handle individual item selection
  const handleItemSelect = (index: number, checked: boolean) => {
    const updatedItems = [...watchedItems];
    const itemToUpdate = updatedItems[index];
    if (itemToUpdate) {
      updatedItems[index] = {
        ...itemToUpdate,
        selected: checked,
        fulfillQuantity: checked ? itemToUpdate.maxQuantity : 0,
      };
    }
    form.setValue('items', updatedItems);
  };

  // Handle quantity change
  const handleQuantityChange = (index: number, quantity: number) => {
    const updatedItems = [...watchedItems];
    const itemToUpdate = updatedItems[index];
    if (itemToUpdate) {
      updatedItems[index] = {
        ...itemToUpdate,
        fulfillQuantity: Math.min(quantity, itemToUpdate.maxQuantity),
      };
    }

    form.setValue('items', updatedItems);
  };

  // Handle location change
  const handleLocationChange = (index: number, locationId: string) => {
    const updatedItems = [...watchedItems];
    const itemToUpdate = updatedItems[index];
    if (itemToUpdate) {
      updatedItems[index] = {
        ...itemToUpdate,
        locationId,
      };
    }

    form.setValue('items', updatedItems);
  };

  const selectedItems = watchedItems.filter(
    (item) => item.selected && item.fulfillQuantity > 0,
  );

  // Helper function to determine if service type should decrement inventory
  const shouldDecrementInventory = (serviceType: string): boolean => {
    return serviceType === 'retail' || serviceType === 'trade-in';
  };

  // Check if all selected items have sufficient inventory (only for retail/trade-in)
  const hasInsufficientInventory = shouldDecrementInventory(payload.serviceType)
    ? selectedItems.some((item) => {
        const availableInventory = getInventoryForVariant(
          item.variantId,
          item.locationId,
        );
        return availableInventory < item.fulfillQuantity;
      })
    : false; // No inventory check needed for consignment/buyback

  const canSubmit =
    selectedItems.length > 0 &&
    selectedItems.every(
      (item) => item.locationId && item.fulfillQuantity > 0,
    ) &&
    !hasInsufficientInventory;

  const onSubmit = async (data: FulfillmentFormData) => {
    const fulfillmentPayload: FulfillOrderPayload = {
      orderId: payload.id,
      items: data.items
        .filter((item) => item.selected && item.fulfillQuantity > 0)
        .map((item) => ({
          orderItemId: item.orderItemId,
          variantId: item.variantId,
          fulfillQuantity: item.fulfillQuantity,
          locationId: item.locationId,
        })),
    };

    try {
      await fulfillOrder(fulfillmentPayload);

      await updateOrderStatus({
        orderId: payload.id,
        status: 'completed',
        comment: '',
      });

      onClose();
    } catch (_) {}
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <Dialog.Content className="w-full min-w-4xl overflow-hidden">
        <Form {...form}>
          <form
            className="flex size-full flex-col"
            onSubmit={form.handleSubmit(onSubmit)}
          >
            <Dialog.Body
              title="Complete Order - Fulfillment"
              description="Select items and locations for fulfillment. After fulfillment, the order status will be updated to completed."
            >
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      checked={selectedItems.length === payload.items.length}
                      onCheckedChange={handleSelectAll}
                    />
                    <span className="font-medium text-sm">Select All</span>
                  </div>
                  <div className="text-muted-foreground text-sm">
                    {selectedItems.length} of {payload.items.length} items
                    selected
                  </div>
                </div>

                <Table>
                  <Table.Header>
                    <Table.Row>
                      <Table.Head className="w-12">Select</Table.Head>
                      <Table.Head>Product</Table.Head>
                      <Table.Head>Quantity</Table.Head>
                      <Table.Head>Location</Table.Head>
                      <Table.Head>Available</Table.Head>
                      <Table.Head>Price</Table.Head>
                    </Table.Row>
                  </Table.Header>
                  <Table.Body>
                    {payload.items.map((orderItem, index) => {
                      const formItem = watchedItems[index];
                      if (!formItem) return null;

                      const availableInventory = getInventoryForVariant(
                        formItem.variantId,
                        formItem.locationId,
                      );
                      const hasInsufficientStock =
                        shouldDecrementInventory(payload.serviceType) &&
                        formItem.selected &&
                        availableInventory < formItem.fulfillQuantity;

                      return (
                        <Table.Row key={orderItem.id}>
                          <Table.Cell>
                            <Checkbox
                              checked={formItem.selected}
                              onCheckedChange={(checked) =>
                                handleItemSelect(index, checked as boolean)
                              }
                            />
                          </Table.Cell>
                          <Table.Cell>
                            <div className="space-y-1">
                              <p className="font-medium text-sm">
                                {orderItem.productSnapshot?.name ||
                                  'Unknown Product'}
                              </p>
                              {orderItem.variantSnapshot?.name && (
                                <p className="text-muted-foreground text-xs">
                                  {orderItem.variantSnapshot.name}
                                </p>
                              )}
                            </div>
                          </Table.Cell>
                          <Table.Cell>
                            <div className="flex items-center space-x-2">
                              <Input
                                type="number"
                                min={0}
                                max={formItem.maxQuantity}
                                value={formItem.fulfillQuantity}
                                onChange={(e) =>
                                  handleQuantityChange(
                                    index,
                                    Number(e.target.value),
                                  )
                                }
                                disabled={!formItem.selected}
                                className="w-20"
                              />
                              <span className="text-muted-foreground text-sm">
                                / {formItem.maxQuantity}
                              </span>
                            </div>
                          </Table.Cell>
                          <Table.Cell>
                            <Select
                              value={formItem.locationId}
                              onValueChange={(value) =>
                                handleLocationChange(index, value)
                              }
                              disabled={!formItem.selected}
                            >
                              <Select.Trigger className="w-40">
                                <Select.Value placeholder="Select location" />
                              </Select.Trigger>
                              <Select.Content>
                                {locations?.map((location) => (
                                  <Select.Item
                                    key={location.id}
                                    value={location.id}
                                  >
                                    {location.name}
                                  </Select.Item>
                                ))}
                              </Select.Content>
                            </Select>
                          </Table.Cell>
                          <Table.Cell>
                            <span
                              className={`text-sm ${
                                hasInsufficientStock
                                  ? 'font-medium text-destructive'
                                  : 'text-muted-foreground'
                              }`}
                            >
                              {formItem.locationId ? availableInventory : '-'}
                            </span>
                          </Table.Cell>
                          <Table.Cell>
                            <span className="font-medium">
                              {formatPrice(
                                orderItem.unitPrice * formItem.fulfillQuantity,
                              )}
                            </span>
                          </Table.Cell>
                        </Table.Row>
                      );
                    })}
                  </Table.Body>
                </Table>
              </div>
            </Dialog.Body>

            <Dialog.Footer>
              {hasInsufficientInventory && (
                <div className="flex-1 text-destructive text-sm">
                  ⚠️ Some items have insufficient inventory at selected locations
                </div>
              )}
              {!shouldDecrementInventory(payload.serviceType) &&
                selectedItems.length > 0 && (
                  <div className="flex-1 text-blue-600 text-sm">
                    ℹ️ Fulfilling this {payload.serviceType} order will add
                    inventory to selected locations
                  </div>
                )}
              <Button
                variant="outline"
                onClick={onClose}
                disabled={isFulfilling}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={!canSubmit || isFulfilling}
                loading={isFulfilling}
              >
                {isFulfilling
                  ? 'Processing...'
                  : `Complete Order (${selectedItems.length} item${selectedItems.length !== 1 ? 's' : ''})`}
              </Button>
            </Dialog.Footer>
          </form>
        </Form>
      </Dialog.Content>
    </Dialog>
  );
};
