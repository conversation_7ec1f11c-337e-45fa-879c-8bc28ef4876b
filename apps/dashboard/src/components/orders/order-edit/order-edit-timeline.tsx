'use client';

import { useGetOrderChanges } from '@muraadso/api/hooks';
import type { OrderChange } from '@muraadso/models/orderChanges';
import type { Order } from '@muraadso/models/orders';
import { Avatar, Badge, ScrollArea } from '@muraadso/ui';
import { formatDate, formatDistanceToNow } from 'date-fns';
import React from 'react';

type OrderEditTimelineProps = {
  order: Order;
};

export const OrderEditTimeline = ({ order }: OrderEditTimelineProps) => {
  const { changes, isPending, pagination } = useGetOrderChanges(order.id, {
    pageSize: 50,
  });

  // Use changes directly from database (includes order creation)
  const allChanges = changes;

  return (
    <div className="flex h-full flex-col">
      {/* Header */}
      <div className="border-border border-b bg-background p-4">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold">Timeline</h3>
          <Badge variant="outline" className="text-xs">
            {pagination.total} events
          </Badge>
        </div>
        <p className="mt-1 text-muted-foreground text-xs">
          Track all changes and updates
        </p>
      </div>

      {/* Timeline Content */}
      <ScrollArea className="flex-1">
        <div className="p-4">
          {isPending ? (
            <div className="flex items-center justify-center py-8">
              <div className="h-6 w-6 animate-spin rounded-full border-primary border-b-2" />
            </div>
          ) : allChanges.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <p className="text-muted-foreground text-sm">No changes yet</p>
            </div>
          ) : (
            <div className="space-y-0">
              {allChanges.map((change, index) => (
                <TimelineItem
                  key={change.id}
                  order={order}
                  change={change}
                  isLast={index === allChanges.length - 1}
                />
              ))}
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Order Lifecycle Summary */}
      <div className="border-border border-t bg-muted/30 p-4">
        <h4 className="mb-3 font-medium text-sm">Order Status</h4>
        <div className="space-y-2">
          <div className="flex justify-between text-xs">
            <span className="text-muted-foreground">Created:</span>
            <span>{formatDate(order.createdAt, 'MMM dd')}</span>
          </div>
          <div className="flex justify-between text-xs">
            <span className="text-muted-foreground">Last modified;</span>
            <span>{formatDate(order.updatedAt, 'MMM dd')}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground text-xs">Status:</span>
            <Badge variant="outline" className="text-xs capitalize">
              {order.status}
            </Badge>
          </div>
          {order.qchStatus !== 'none' && (
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground text-xs">QCH</span>
              <Badge variant="secondary" className="text-xs capitalize">
                {order.qchStatus}
              </Badge>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

type TimelineItemProps = {
  order: Order;
  change: OrderChange;
  isLast: boolean;
};

const TimelineItem = ({ order, change, isLast }: TimelineItemProps) => {
  const getChangeTypeLabel = (changeType: string, comment?: string | null) => {
    // Special case for order creation
    if (changeType === 'other' && comment === 'Order created') {
      return 'Order Placed';
    }

    const labels: Record<string, string> = {
      status_update: 'Status Updated',
      payment_update: 'Payment Updated',
      fulfillment_update: 'Fulfillment Updated',
      amount_update: 'Amount Updated',
      item_added: 'Item Added',
      item_removed: 'Item Removed',
      item_updated: 'Item Updated',
      note_update: 'Note Updated',
      customer_update: 'Customer Updated',
      other: 'Other Change',
    };
    return labels[changeType] || changeType;
  };

  const getChangeTypeColor = (changeType: string) => {
    const colors: Record<string, string> = {
      status_update: 'bg-blue-500',
      payment_update: 'bg-green-500',
      fulfillment_update: 'bg-purple-500',
      amount_update: 'bg-yellow-500',
      item_added: 'bg-emerald-500',
      item_removed: 'bg-red-500',
      item_updated: 'bg-orange-500',
      note_update: 'bg-gray-500',
      customer_update: 'bg-indigo-500',
      other: 'bg-gray-400',
    };
    return colors[changeType] || 'bg-gray-400';
  };

  const formatChangeDetails = () => {
    if (!change.previousValues || !change.newValues) {
      return null;
    }

    const details = [];

    // Handle status changes
    if (change.previousValues.status && change.newValues.status) {
      details.push(
        `${change.previousValues.status} → ${change.newValues.status}`,
      );
    }

    // Handle QCH status changes
    if (change.previousValues.qchStatus && change.newValues.qchStatus) {
      details.push(
        `QCH: ${change.previousValues.qchStatus} → ${change.newValues.qchStatus}`,
      );
    }

    for (const key of Object.keys(change.newValues)) {
      if (key !== 'status' && key !== 'qchStatus') {
        const oldValue = change.previousValues?.[key];
        const newValue = change.newValues?.[key];
        if (oldValue !== newValue) {
          details.push(`${key}: ${oldValue} → ${newValue}`);
        }
      }
    }

    return details;
  };

  const changeDetails = formatChangeDetails();

  return (
    <div className="relative flex space-x-3 pb-6">
      {/* Timeline Line */}
      {!isLast && (
        <div className="absolute top-4 left-1.75 h-full w-0.5 bg-border" />
      )}

      {/* Timeline Dot */}
      <div
        className={`relative z-10 mt-1 flex size-4 items-center justify-center rounded-full ${getChangeTypeColor(change.changeType)} shadow-sm`}
      >
        <div className="size-1.5 rounded-full bg-white" />
      </div>

      {/* Content */}
      <div className="min-w-0 flex-1">
        <div className="space-y-1">
          <p className="font-medium text-sm">
            {getChangeTypeLabel(change.changeType, change?.comment)}
          </p>
          <p className="text-muted-foreground text-xs">
            {formatDistanceToNow(new Date(change.createdAt), {
              addSuffix: true,
            })}
          </p>
        </div>

        {/* Change Details */}
        {changeDetails && changeDetails.length > 0 && (
          <div className="mt-2 space-y-1">
            {changeDetails.map((detail, index) => (
              <p
                key={index}
                className="rounded bg-muted px-2 py-1 text-muted-foreground text-xs"
              >
                {detail}
              </p>
            ))}
          </div>
        )}

        {/* Comment */}
        {change.comment && (
          <div className="mt-2 rounded-md bg-muted p-2">
            <p className="text-xs">{change.comment}</p>
          </div>
        )}

        {/* User Info */}
        <div className="mt-2 flex items-center space-x-2">
          {change.sysop ? (
            <div className="flex items-center space-x-1">
              <p className="text-muted-foreground text-xs">
                {change.sysop.name}
              </p>
              <Badge variant="outline" className="px-1 py-0 text-xs">
                Staff
              </Badge>
            </div>
          ) : (
            <div className="flex items-center space-x-1">
              <p className="text-muted-foreground text-xs">
                {order.customer.name}
              </p>
              <Badge variant="secondary" className="px-1 py-0 text-xs">
                Customer
              </Badge>
            </div>
          )}
        </div>

        {/* Timestamp */}
        <p className="mt-1 text-muted-foreground text-xs">
          {formatDate(change.createdAt, 'MMM dd, h:mm a')}
        </p>
      </div>
    </div>
  );
};
