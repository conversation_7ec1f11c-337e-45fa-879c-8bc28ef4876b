import { OrderPageProvider } from '@/contexts/order-page-context';
import { useGetOrder } from '@muraadso/api/hooks';
import type { Order } from '@muraadso/models/orders';
import { Button, FocusModal } from '@muraadso/ui';
import React from 'react';
import { OrderEditContent } from './order-edit-content';
import { OrderEditTimeline } from './order-edit-timeline';

type OrderEditDialogProps = {
  open: boolean;
  onClose: (order?: Order) => void;
  order: Order;
};

export const OrderEditForm = ({
  open,
  onClose,
  order: initialOrder,
}: OrderEditDialogProps) => {
  // Fetch the latest order data to ensure we have up-to-date information
  const { order: currentOrder, isPending } = useGetOrder(initialOrder.id);

  // Use the fetched order data if available, otherwise fall back to initial order
  const order = currentOrder || initialOrder;

  if (isPending) {
    return (
      <FocusModal open={open} onOpenChange={() => onClose()}>
        <FocusModal.Portal>
          <FocusModal.Content className="overflow-visible">
            <FocusModal.Header>
              <FocusModal.Title />
              <FocusModal.Description />
            </FocusModal.Header>
            <FocusModal.Body className="flex items-center justify-center py-8">
              <div className="h-8 w-8 animate-spin rounded-full border-primary border-b-2" />
            </FocusModal.Body>
          </FocusModal.Content>
        </FocusModal.Portal>
      </FocusModal>
    );
  }

  return (
    <OrderPageProvider>
      <FocusModal open={open} onOpenChange={() => onClose()}>
        <FocusModal.Portal>
          <FocusModal.Content className="overflow-visible">
            <FocusModal.Header className="flex items-center justify-between">
              <FocusModal.Title />
              <FocusModal.Description />
            </FocusModal.Header>

            <FocusModal.Body className="size-full overflow-hidden p-0">
              <div className="flex h-full">
                {/* Main Content */}
                <div className="flex-1 overflow-y-auto">
                  <OrderEditContent order={order} />
                </div>

                {/* Timeline Sidebar */}
                <div className="w-80 overflow-y-auto border-border border-s bg-muted/30">
                  <OrderEditTimeline order={order} />
                </div>
              </div>
            </FocusModal.Body>
          </FocusModal.Content>
        </FocusModal.Portal>
      </FocusModal>
    </OrderPageProvider>
  );
};
