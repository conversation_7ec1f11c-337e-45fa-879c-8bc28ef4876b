import { useOrderPageContext } from '@/contexts/order-page-context';
import {
  getAvailableQchStatusOptions,
  getAvailableStatusOptions,
  shouldShowQchOptions,
} from '@/lib/order-status-utils';
import {
  useUpdateOrderQchStatus,
  useUpdateOrderStatus,
} from '@muraadso/api/hooks';
import { MoreVerticalIcon } from '@muraadso/icons';
import type { Order, OrderStatus, QchStatus } from '@muraadso/models/orders';
import {
  AlertDialog,
  Button,
  DropdownMenu,
  Form,
  Label,
  Textarea,
  toast,
} from '@muraadso/ui';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { OrderEditFulfillment } from './order-edit-fulfillment';

interface OrderEditStatusProps {
  order: Order;
}

interface StatusUpdateForm {
  comment?: string;
}

export const OrderEditStatus = ({ order }: OrderEditStatusProps) => {
  const pageContext = useOrderPageContext();
  const [isUpdating, setIsUpdating] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [showFulfillmentDialog, setShowFulfillmentDialog] = useState(false);
  const [pendingUpdate, setPendingUpdate] = useState<{
    type: 'status' | 'qch';
    value: OrderStatus | QchStatus;
  } | null>(null);

  const { mutateAsync: updateOrderStatus } = useUpdateOrderStatus();
  const { mutateAsync: updateOrderQchStatus } = useUpdateOrderQchStatus();
  // const { mutateAsync: cancelOrder } = useCancelOrder();

  const statusOptions = getAvailableStatusOptions(order, pageContext);
  const qchStatusOptions = getAvailableQchStatusOptions(order, pageContext);
  const showQchOptions = shouldShowQchOptions(order.serviceType);

  const form = useForm<StatusUpdateForm>({
    defaultValues: {
      comment: '',
    },
  });

  // Don't render if no options are available
  if (statusOptions.length === 0 && qchStatusOptions.length === 0) {
    return null;
  }

  const handleStatusUpdate = async (
    type: 'status' | 'qch',
    value: OrderStatus | QchStatus,
  ) => {
    // Check if this is a completion request - show fulfillment dialog directly
    if (type === 'status' && value === 'completed') {
      setShowFulfillmentDialog(true);
      return;
    }

    // Check if this is a critical status change that needs confirmation
    const needsConfirmation =
      (type === 'status' && (value === 'canceled' || value === 'returned')) ||
      (type === 'qch' && value === 'failed');

    if (needsConfirmation) {
      setPendingUpdate({ type, value });
      setShowConfirmDialog(true);
      return;
    }

    await executeStatusUpdate(type, value, '');
  };

  const executeStatusUpdate = async (
    type: 'status' | 'qch',
    value: OrderStatus | QchStatus,
    comment: string,
  ) => {
    setIsUpdating(true);
    try {
      if (type === 'status') {
        await updateOrderStatus({
          orderId: order.id,
          status: value as OrderStatus,
          comment,
        });
      } else {
        await updateOrderQchStatus({
          orderId: order.id,
          qchStatus: value as QchStatus,
        });
      }
    } catch (error) {
      toast.error('Failed to update status. Please try again.');
      console.error('Status update error:', error);
    } finally {
      setIsUpdating(false);
      setShowConfirmDialog(false);
      setPendingUpdate(null);
      form.reset();
    }
  };

  const handleConfirmUpdate = async (data: StatusUpdateForm) => {
    if (!pendingUpdate) return;

    await executeStatusUpdate(
      pendingUpdate.type,
      pendingUpdate.value,
      data.comment || '',
    );
  };

  // const handleCustomerCancellation = async () => {
  //   try {
  //     setIsUpdating(true);
  //     await cancelOrder({
  //       orderId: order.id,
  //       reason: 'Customer requested cancellation',
  //     });
  //   } catch (error) {
  //     toast.error('Failed to cancel order. Please try again.');
  //     console.error('Customer cancellation error:', error);
  //   } finally {
  //     setIsUpdating(false);
  //   }
  // };

  const getStatusLabel = (
    type: 'status' | 'qch',
    value: OrderStatus | QchStatus,
  ) => {
    if (type === 'status') {
      return statusOptions.find((opt) => opt.value === value)?.label || value;
    }
    return qchStatusOptions.find((opt) => opt.value === value)?.label || value;
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenu.Trigger asChild>
          <Button variant="ghost" size="sm" disabled={isUpdating}>
            <MoreVerticalIcon className="size-4" />
          </Button>
        </DropdownMenu.Trigger>
        <DropdownMenu.Content align="end" className="w-56">
          {statusOptions.length > 0 && (
            <>
              <DropdownMenu.Label>Update Status</DropdownMenu.Label>
              <DropdownMenu.Separator />
              {statusOptions.map((option) => (
                <DropdownMenu.Item
                  key={option.value}
                  disabled={option.disabled || isUpdating}
                  onClick={() => handleStatusUpdate('status', option.value)}
                  className="flex flex-col items-start"
                >
                  <span>{option.label}</span>
                </DropdownMenu.Item>
              ))}
            </>
          )}

          {showQchOptions && qchStatusOptions.length > 0 && (
            <>
              <DropdownMenu.Label>QCH Status</DropdownMenu.Label>
              {qchStatusOptions.map((option) => (
                <DropdownMenu.Item
                  key={option.value}
                  disabled={option.disabled || isUpdating}
                  onClick={() => handleStatusUpdate('qch', option.value)}
                  className="flex flex-col items-start"
                >
                  <span>{option.label}</span>
                </DropdownMenu.Item>
              ))}
            </>
          )}

          {/*/!* Customer Cancellation (for testing) *!/*/}
          {/*{order.status !== 'canceled' && order.status !== 'completed' && (*/}
          {/*  <>*/}
          {/*    <DropdownMenu.Separator />*/}
          {/*    <DropdownMenu.Label>Customer Actions</DropdownMenu.Label>*/}
          {/*    <DropdownMenu.Item*/}
          {/*      onClick={() => handleCustomerCancellation()}*/}
          {/*      className="text-destructive"*/}
          {/*    >*/}
          {/*      Cancel as Customer*/}
          {/*    </DropdownMenu.Item>*/}
          {/*  </>*/}
          {/*)}*/}
        </DropdownMenu.Content>
      </DropdownMenu>

      {/* Confirmation Dialog */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialog.Content>
          <AlertDialog.Header>
            <AlertDialog.Title>Confirm Status Update</AlertDialog.Title>
            <AlertDialog.Description>
              {pendingUpdate && (
                <>
                  Are you sure you want to update the{' '}
                  {pendingUpdate.type === 'status'
                    ? 'order status'
                    : 'QCH status'}{' '}
                  to{' '}
                  <strong>
                    {getStatusLabel(pendingUpdate.type, pendingUpdate.value)}
                  </strong>
                  ? This action cannot be undone.
                </>
              )}
            </AlertDialog.Description>
          </AlertDialog.Header>

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleConfirmUpdate)}
              className="space-y-4"
            >
              <div className="space-y-2">
                <Form.Label htmlFor="comment" optional>
                  Comment
                </Form.Label>
                <Form.Field
                  control={form.control}
                  name="comment"
                  render={({ field }) => (
                    <Form.Item>
                      <Form.Control>
                        <Textarea
                          {...field}
                          placeholder="Add a comment about this status change..."
                          rows={3}
                        />
                      </Form.Control>
                    </Form.Item>
                  )}
                />
              </div>

              <AlertDialog.Footer>
                <AlertDialog.Cancel disabled={isUpdating}>
                  Cancel
                </AlertDialog.Cancel>
                <Button type="submit" disabled={isUpdating}>
                  {isUpdating ? 'Updating...' : 'Confirm Update'}
                </Button>
              </AlertDialog.Footer>
            </form>
          </Form>
        </AlertDialog.Content>
      </AlertDialog>

      {/* Fulfillment Dialog */}
      <OrderEditFulfillment
        open={showFulfillmentDialog}
        onClose={() => setShowFulfillmentDialog(false)}
        payload={order}
      />
    </>
  );
};
