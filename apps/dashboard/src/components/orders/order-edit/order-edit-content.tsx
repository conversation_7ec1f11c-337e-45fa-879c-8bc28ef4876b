import { useOrderPageContext } from '@/contexts/order-page-context';
import {
  getCurrentStatusInfo,
  getQchProgressInfo,
} from '@/lib/order-status-utils';
import { getImageUrl } from '@/lib/utils';
import type { Order, OrderItem } from '@muraadso/models/orders';
import type { Product } from '@muraadso/models/products';
import type { Variant } from '@muraadso/models/variants';
import { Avatar, Badge, Card, Separator } from '@muraadso/ui';
import { formatDate } from 'date-fns';
import React from 'react';
import { OrderEditStatus } from './order-edit-status';

type OrderEditContentProps = {
  order: Order;
};

export const OrderEditContent = ({ order }: OrderEditContentProps) => {
  const pageContext = useOrderPageContext();
  const statusInfo = getCurrentStatusInfo(order);
  const qchProgressInfo = getQchProgressInfo(order);

  const calculateSubtotal = () => {
    return order.items.reduce(
      (sum, item) => sum + item.unitPrice * item.quantity,
      0,
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const subtotal = calculateSubtotal();
  const commission = subtotal * (order.commissionRate / 100);
  const total = order.totalAmount;

  return (
    <div className="mx-auto max-w-4xl space-y-6 p-6">
      {/* Order Header */}
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          <h2 className="font-bold text-2xl">#{order.trackingId}</h2>
          <p className="text-muted-foreground text-sm">
            {formatDate(order.createdAt, 'MMM dd, yyyy')} at{' '}
            {formatDate(order.createdAt, 'h:mm a')}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="capitalize">
            {statusInfo.status.label}
          </Badge>
          {pageContext.showQchStatus && qchProgressInfo.visible && (
            <Badge
              variant={
                pageContext.showQchProgress
                  ? qchProgressInfo.variant
                  : 'secondary'
              }
              className="capitalize"
            >
              QCH:{' '}
              {pageContext.showQchProgress
                ? qchProgressInfo.label
                : statusInfo.qchStatus.label}
            </Badge>
          )}
          <OrderEditStatus order={order} />
        </div>
      </div>

      {/* Customer Information */}
      <Card className="p-6">
        <div className="flex items-start space-x-4">
          <Avatar className="size-16">
            <Avatar.Image
              src={order.customer.image || undefined}
              alt={order.customer.name}
            />
            <Avatar.Fallback className="text-lg">
              {order.customer.name
                .split(' ')
                .map((n) => n[0])
                .join('')
                .toUpperCase()}
            </Avatar.Fallback>
          </Avatar>
          <div className="flex-1 space-y-3">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <p className="text-muted-foreground text-sm">Name</p>
                <p className="font-medium">{order.customer.name}</p>
              </div>
              <div>
                <p className="text-muted-foreground text-sm">Phone</p>
                <p className="font-medium">{order.customer.phone || 'N/A'}</p>
              </div>
              <div>
                <p className="text-muted-foreground text-sm">Email</p>
                <p className="font-medium">{order.customer.email || 'N/A'}</p>
              </div>
              <div>
                <p className="text-muted-foreground text-sm">National ID</p>
                <p className="font-medium">
                  {order.customer.nationalId || 'N/A'}
                </p>
              </div>
            </div>
            <div>
              <p className="text-muted-foreground text-sm">Address</p>
              <p className="font-medium">{order.address}</p>
            </div>
          </div>
        </div>
      </Card>

      {/* Order Items */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-lg">Order Items</h3>
          <Badge variant="outline">{order.items.length} item(s)</Badge>
        </div>
        <div className="space-y-4">
          {order.items.map((item) => (
            <OrderItemRow
              key={item.id}
              item={item}
              serviceType={order.serviceType}
            />
          ))}
        </div>
      </Card>

      {/* Order Summary */}
      <Card className="p-6">
        <h3 className="font-semibold text-lg">Order Summary</h3>
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Subtotal</span>
            <span>{formatCurrency(subtotal)}</span>
          </div>

          {order.commissionRate > 0 && (
            <div className="flex justify-between">
              <span className="text-muted-foreground">
                Commission ({order.commissionRate}%)
              </span>
              <span>{formatCurrency(commission)}</span>
            </div>
          )}

          <Separator />

          <div className="flex justify-between font-semibold text-lg">
            <span>Total</span>
            <span>{formatCurrency(total)}</span>
          </div>

          {/*<div className="flex items-center justify-between pt-2">*/}
          {/*  <Badge variant="secondary" className="capitalize">*/}
          {/*    {order.serviceType} Service*/}
          {/*  </Badge>*/}
          {/*  {(order.serviceType === 'buyback' ||*/}
          {/*    order.serviceType === 'trade-in' ||*/}
          {/*    order.serviceType === 'consignment') && (*/}
          {/*    <Badge variant={order.isPaid ? 'default' : 'secondary'}>*/}
          {/*      {order.isPaid ? 'Paid' : 'Unpaid'}*/}
          {/*    </Badge>*/}
          {/*  )}*/}
          {/*</div>*/}
        </div>
      </Card>

      {/* Order Notes */}
      {/*<Card className="p-6">*/}
      {/*  <h3 className="mb-4 font-semibold text-lg">Order Notes</h3>*/}
      {/*  <Form.Field*/}
      {/*    control={form.control}*/}
      {/*    name="note"*/}
      {/*    render={({ field }) => (*/}
      {/*      <Form.Item>*/}
      {/*        <Form.Control>*/}
      {/*          <Textarea*/}
      {/*            placeholder="Add notes about this order..."*/}
      {/*            className="min-h-[100px]"*/}
      {/*            {...field}*/}
      {/*          />*/}
      {/*        </Form.Control>*/}
      {/*        <Form.Message />*/}
      {/*      </Form.Item>*/}
      {/*    )}*/}
      {/*  />*/}
      {/*</Card>*/}
    </div>
  );
};

type OrderItemRowProps = {
  item: OrderItem;
  serviceType: string;
};

const OrderItemRow = ({ item }: OrderItemRowProps) => {
  const product = item.productSnapshot as Product;
  const variant = item.variantSnapshot as Variant;

  const image = variant?.images?.[0] || product.images?.[0];

  const totalPrice = item.unitPrice * item.quantity;
  const isLaptop = item.productSnapshot?.category?.slug === 'laptops';
  const hasMetadata = item.metadata && Object.keys(item.metadata).length > 0;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <div className="flex items-start space-x-4 rounded-lg border border-border bg-card p-4">
      {/* Product Image */}
      <div className="flex-shrink-0">
        <img
          src={
            image?.path ? getImageUrl(image?.path) : '/images/placeholder.svg'
          }
          alt=""
          className="size-16 rounded-lg border border-border object-cover"
        />
      </div>

      {/* Product Details */}
      <div className="min-w-0 flex-1 space-y-2">
        <div>
          <h4 className="font-medium text-foreground">{product?.name}</h4>
          {variant?.name && (
            <p className="text-muted-foreground text-sm">{variant?.name}</p>
          )}
        </div>

        {/* Metadata */}
        {hasMetadata && (
          <div className="flex flex-wrap gap-2">
            {isLaptop && item.metadata?.serialNumber && (
              <Badge variant="secondary" className="text-xs">
                Serial: {item.metadata.serialNumber}
              </Badge>
            )}
            {!isLaptop && item.metadata?.imei && (
              <Badge variant="secondary" className="text-xs">
                IMEI: {item.metadata.imei}
              </Badge>
            )}
          </div>
        )}
      </div>

      {/* Pricing */}
      <div className="flex-shrink-0 text-right">
        <div className="space-y-1">
          <p className="font-medium">{formatCurrency(totalPrice)}</p>
          <p className="text-muted-foreground text-sm">x{item.quantity} qty</p>
        </div>
      </div>
    </div>
  );
};
