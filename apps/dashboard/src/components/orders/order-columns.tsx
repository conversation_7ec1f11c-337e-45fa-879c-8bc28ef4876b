'use client';

import {
  QCH_PROGRESS_LABELS,
  getCurrentStatusInfo,
  getQchProgressInfo,
} from '@/lib/order-status-utils';
import { getImageUrl } from '@/lib/utils';
import { useGetCurrentSysop } from '@muraadso/api/hooks';
import { MoreVerticalIcon, PlusIcon } from '@muraadso/icons';
import type { Order } from '@muraadso/models/orders';
import { Avatar, Badge, Button, Checkbox, DropdownMenu } from '@muraadso/ui';
import type { ColumnDef } from '@tanstack/react-table';
import { formatDate } from 'date-fns';
import { useMemo } from 'react';

export const useOrderColumns = (
  onView: (order: Order) => void,
  onPrint: (order: Order) => void,
  showQchStatus?: boolean,
): ColumnDef<Order>[] => {
  const { currentSysop } = useGetCurrentSysop();
  const isSuperAdmin = currentSysop?.isSuperAdmin || false;

  return useMemo(() => {
    const columns: ColumnDef<Order>[] = [
      {
        id: 'select',
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && 'indeterminate')
            }
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
        ),
      },
      {
        header: 'ID',
        cell: ({ row }) => (
          <div className="uppercase">#{row.original.trackingId ?? '-'}</div>
        ),
      },
      {
        accessorKey: 'date',
        header: 'Date',
        cell: ({ row }) => (
          <div className="text-muted-foreground">
            {formatDate(row.original.createdAt, 'dd MMM, hh:mm') ?? '-'}
          </div>
        ),
      },

      {
        header: 'Status',
        cell: ({ row }) => {
          return (
            <Badge variant="outline" className="capitalize">
              {row.original.status}
            </Badge>
          );
        },
      },
      {
        accessorKey: 'customer',
        header: 'Customer',
        cell: ({ row }) => (
          <div className="flex items-center space-x-1 capitalize">
            <Avatar className="rounded-none">
              <Avatar.Image
                className="mt-1 size-6 rounded-md object-cover"
                src={
                  getImageUrl(row.original.customer.image) ||
                  '/images/avatar.svg'
                }
                alt=""
              />
            </Avatar>
            <div className="flex w-full flex-col">
              <span>{row.original.customer.name ?? '-'}</span>
              <span className="text-muted-foreground text-sm">
                {row.original.customer.phone ?? '-'}
              </span>
            </div>
          </div>
        ),
      },
      {
        id: 'location',
        header: 'Location',
        cell: ({ row }) => (
          <div className="text-sm">
            {row.original.location ? (
              <span className="text-muted-foreground">
                {row.original.location.name}
              </span>
            ) : (
              <span className="text-muted-foreground italic">No location</span>
            )}
          </div>
        ),
      },
      {
        id: 'total',
        header: 'Total',
        cell: ({ row }) => (
          <div className="">
            ${row?.original?.totalAmount?.toFixed(2) ?? '-'}
          </div>
        ),
      },
      {
        id: 'actions',
        enableHiding: false,
        cell: ({ row }) => {
          const order = row.original;

          return (
            <DropdownMenu>
              <DropdownMenu.Trigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreVerticalIcon className="size-5 text-muted-foreground" />
                </Button>
              </DropdownMenu.Trigger>
              <DropdownMenu.Content align="end">
                <DropdownMenu.Item
                  onClick={() => onView(order)}
                  className="flex items-center gap-2"
                >
                  View
                </DropdownMenu.Item>
                <DropdownMenu.Item
                  onClick={() => onPrint(order)}
                  className="flex items-center gap-2"
                >
                  Print
                </DropdownMenu.Item>
              </DropdownMenu.Content>
            </DropdownMenu>
          );
        },
      },
    ];

    if (showQchStatus) {
      columns.splice(3, 0, {
        header: 'Q. Checking',
        cell: ({ row }) => (
          <Badge variant="outline" className="capitalize">
            {getQchProgressInfo(row.original).label}
          </Badge>
        ),
      });
    }

    if (!isSuperAdmin) {
      const locationColumnIndex = columns.findIndex(
        (col) => col.id === 'location',
      );
      if (locationColumnIndex !== -1) {
        columns.splice(locationColumnIndex, 1);
      }
    }

    return columns;
  }, [showQchStatus, isSuperAdmin, onView, onPrint]);
};
