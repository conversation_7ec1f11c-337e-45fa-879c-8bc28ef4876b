'use client';

import { useOrderPageContext } from '@/contexts/order-page-context';
import { useGetCurrentSysop, useGetLocations } from '@muraadso/api/hooks';
import { CalendarIcon, FilterIcon } from '@muraadso/icons';
import { orderStatus } from '@muraadso/models/orders';
import { Button, Calendar, Label, Popover, Select } from '@muraadso/ui';
import { format, subDays } from 'date-fns';
import { useQueryState } from 'nuqs';
import { useState } from 'react';

export const OrderFilters = () => {
  const [status, setStatus] = useQueryState('status');
  const [startDate, setStartDate] = useQueryState('startDate');
  const [endDate, setEndDate] = useQueryState('endDate');
  const [locationId, setLocationId] = useQueryState('locationId');
  const [serviceType, setServiceType] = useQueryState('serviceType');

  // Get current user and locations data
  const { currentSysop } = useGetCurrentSysop();
  const { locations } = useGetLocations();
  const pageContext = useOrderPageContext();

  // Local state for date picker
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to?: Date | undefined;
  }>({
    from: startDate ? new Date(startDate) : undefined,
    to: endDate ? new Date(endDate) : undefined,
  });

  const isSuperAdmin = currentSysop?.isSuperAdmin || false;
  const isPlatformPage = pageContext.pageType === 'platform';

  // Service type options for platform page (excluding retail)
  const serviceTypeOptions = [
    { value: 'all', label: 'All Services' },
    { value: 'consignment', label: 'U iibin' },
    { value: 'buyback', label: 'Ka iibsad' },
    { value: 'trade-in', label: 'Isku bedel' },
  ];

  const handleDateRangeSelect = (range: {
    from: Date | undefined;
    to?: Date | undefined;
  }) => {
    setDateRange(range);
    if (range?.from) {
      setStartDate(format(range.from, 'yyyy-MM-dd'));
    }
    if (range?.to) {
      setEndDate(format(range.to, 'yyyy-MM-dd'));
    }
  };

  // Reset all filters
  const resetFilters = () => {
    setStatus(null);
    setStartDate(null);
    setEndDate(null);
    setLocationId(null);
    setServiceType(null);
    setDateRange({ from: undefined, to: undefined });
  };

  return (
    <Popover>
      <Popover.Trigger asChild>
        <Button variant="outline" className="gap-2">
          <FilterIcon className="h-4 w-4" />
          Filters
          {(status || startDate || endDate || locationId || serviceType) && (
            <span className="ml-1 rounded-full bg-primary px-2 py-0.5 text-primary-foreground text-xs">
              {
                [status, startDate, endDate, locationId, serviceType].filter(
                  Boolean,
                ).length
              }
            </span>
          )}
        </Button>
      </Popover.Trigger>
      <Popover.Content className="w-72" side="right">
        <div className="space-y-4">
          <h4 className="font-medium">Filter Orders</h4>

          {/* Status Filter */}
          <div className="space-y-2">
            <Label className="block" htmlFor="status">
              Status
            </Label>
            <Select
              onValueChange={(value) =>
                setStatus(value === 'all' ? null : value)
              }
              value={status || 'all'}
            >
              <Select.Trigger id="status">
                <Select.Value placeholder="All Status" />
              </Select.Trigger>
              <Select.Content>
                <Select.Item value="all">All Status</Select.Item>
                {orderStatus.map((statusOption) => (
                  <Select.Item key={statusOption} value={statusOption}>
                    {statusOption.charAt(0).toUpperCase() +
                      statusOption.slice(1)}
                  </Select.Item>
                ))}
              </Select.Content>
            </Select>
          </div>

          {/* Service Type Filter - Only show for platform page */}
          {isPlatformPage && (
            <div className="space-y-2">
              <Label className="block" htmlFor="serviceType">
                Service Type
              </Label>
              <Select
                onValueChange={(value) =>
                  setServiceType(value === 'all' ? null : value)
                }
                value={serviceType || 'all'}
              >
                <Select.Trigger id="serviceType">
                  <Select.Value placeholder="All Services" />
                </Select.Trigger>
                <Select.Content>
                  {serviceTypeOptions.map((option) => (
                    <Select.Item key={option.value} value={option.value}>
                      {option.label}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select>
            </div>
          )}

          {/* Location Filter - Only show for superadmins */}
          {isSuperAdmin && (
            <div className="space-y-2">
              <Label className="block" htmlFor="location">
                Location
              </Label>
              <Select
                onValueChange={(value) =>
                  setLocationId(value === 'all' ? null : value)
                }
                value={locationId || 'all'}
              >
                <Select.Trigger id="location">
                  <Select.Value placeholder="All Locations" />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="all">All Locations</Select.Item>
                  {locations?.map((location) => (
                    <Select.Item key={location.id} value={location.id}>
                      {location.name} - {location.city}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select>
            </div>
          )}

          {/* Date Range Filter */}
          <div className="space-y-2">
            <Label className="block">Date Range</Label>

            {/* Custom Date Range Picker */}
            <div className="space-y-2">
              <Popover>
                <Popover.Trigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange.from ? (
                      dateRange.to ? (
                        <>
                          {format(dateRange.from, 'LLL dd, y')} -{' '}
                          {format(dateRange.to, 'LLL dd, y')}
                        </>
                      ) : (
                        format(dateRange.from, 'LLL dd, y')
                      )
                    ) : (
                      'Pick a date range'
                    )}
                  </Button>
                </Popover.Trigger>
                <Popover.Content className="w-auto p-0" align="start">
                  <Calendar
                    mode="range"
                    required
                    buttonVariant="outline"
                    defaultMonth={dateRange.from}
                    selected={dateRange}
                    onSelect={handleDateRangeSelect}
                    numberOfMonths={2}
                  />
                </Popover.Content>
              </Popover>
            </div>
          </div>

          <Button variant="outline" onClick={resetFilters} className="w-full">
            Reset All Filters
          </Button>
        </div>
      </Popover.Content>
    </Popover>
  );
};
