'use client';

import {
  useGetOrders,
  usePagination,
  useUpdateOrder,
} from '@muraadso/api/hooks';
import type { OrderStatus, ServiceType } from '@muraadso/models/orders';
import { useQueryState } from 'nuqs';
import { useOrderPageContext } from '@/contexts/order-page-context';

export const useOrders = ({ serviceType }: { serviceType?: ServiceType[] }) => {
  const { q, page, pageSize } = usePagination();
  const [status] = useQueryState('status');
  const [startDate] = useQueryState('startDate');
  const [endDate] = useQueryState('endDate');
  const [sortOrder] = useQueryState('sortOrder', { defaultValue: 'desc' });
  const [locationId] = useQueryState('locationId');
  const [serviceTypeFilter] = useQueryState('serviceType');
  const pageContext = useOrderPageContext();

  // Determine which service types to use
  const getServiceTypes = (): ServiceType[] => {
    // If serviceType is explicitly provided (like in orders or quality-check pages), use it
    if (serviceType) {
      return serviceType;
    }

    // For platform page, use query state or default to non-retail types
    if (pageContext.pageType === 'platform') {
      if (serviceTypeFilter && serviceTypeFilter !== 'all') {
        return [serviceTypeFilter as ServiceType];
      }
      // Default to all non-retail service types for platform page
      return ['consignment', 'buyback', 'trade-in'];
    }

    // Fallback to retail for other pages
    return ['retail'];
  };

  const finalServiceTypes = getServiceTypes();

  const { isPending, error, orders, pagination } = useGetOrders({
    page,
    pageSize,
    q: q || undefined,
    status: status || undefined,
    startDate: startDate || undefined,
    endDate: endDate || undefined,
    sortOrder: sortOrder as 'asc' | 'desc',
    serviceType: finalServiceTypes,
    locationId: locationId || undefined,
  });

  return {
    isPending,
    error,
    orders: orders || [],
    pagination,
  };
};

export const useUpdateOrderStatus = () => {
  const { mutateAsync: updateOrder } = useUpdateOrder();

  const handleStatusChange = async (
    orderId: string,
    newStatus: OrderStatus,
    comment?: string,
  ) => {
    try {
      await updateOrder({
        id: orderId,
        status: newStatus,
        note: comment,
      });
    } catch (error) {
      console.error('Failed to update order status:', error);
    }
  };

  return handleStatusChange;
};
