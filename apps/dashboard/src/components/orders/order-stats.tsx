'use client';

import { useOrderPageContext } from '@/contexts/order-page-context';
import { useGetOrderStats } from '@muraadso/api/hooks';
import type { ServiceType } from '@muraadso/models/orders';
import { Skeleton } from '@muraadso/ui';
import { useQueryState } from 'nuqs';

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};

const formatPercentage = (percentage: number) => {
  const sign = percentage >= 0 ? '+' : '';
  return `${sign}${percentage.toFixed(1)}%`;
};

export const OrderStats = ({ serviceType }: { serviceType?: ServiceType[] }) => {
  const pageContext = useOrderPageContext();
  const [serviceTypeFilter] = useQueryState('serviceType');

  // Determine which service types to use (same logic as useOrders)
  const getServiceTypes = (): ServiceType[] => {
    if (serviceType) {
      return serviceType;
    }

    if (pageContext.pageType === 'platform') {
      if (serviceTypeFilter && serviceTypeFilter !== 'all') {
        return [serviceTypeFilter as ServiceType];
      }
      return ['consignment', 'buyback', 'trade-in'];
    }

    return ['retail'];
  };

  // Determine stats type based on page context
  const getStatsType = () => {
    switch (pageContext.pageType) {
      case 'quality-check':
        return 'quality-check';
      case 'platform':
        return 'platform';
      default:
        return 'monetary';
    }
  };

  const finalServiceTypes = getServiceTypes();
  const { stats, isPending, error } = useGetOrderStats({
    serviceType: finalServiceTypes,
    statsType: getStatsType(),
  });

  if (isPending) {
    return (
      <div className="mb-8 grid w-full grid-cols-4 gap-8 divide-x divide-border border-border border-y border-dashed py-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="flex w-full flex-col space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-8 w-16" />
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="mb-8 grid w-full grid-cols-4 gap-8 divide-x divide-border border-border border-y border-dashed py-6">
        <div className="col-span-4 text-center text-destructive text-sm">
          Failed to load order stats
        </div>
      </div>
    );
  }

  const renderStats = () => {
    const statsType = getStatsType();

    if (statsType === 'quality-check') {
      return (
        <>
          <div className="flex w-full flex-col space-y-2">
            <p className="font-medium text-muted-foreground text-sm">
              Total Orders
            </p>
            <div className="flex items-center space-x-2">
              <span className="font-medium text-2xl">
                {stats.totalQchOrders?.toLocaleString() || '0'}
              </span>
              <span className="text-muted-foreground text-xs">in process</span>
            </div>
          </div>

          <div className="flex w-full flex-col space-y-2">
            <p className="font-medium text-muted-foreground text-sm">
              Passed This Week
            </p>
            <div className="flex items-center space-x-2">
              <span className="font-medium text-2xl">
                {stats.passedThisWeek?.toLocaleString() || '0'}
              </span>
              <span className="text-green-600 text-xs">passed</span>
            </div>
          </div>

          <div className="flex w-full flex-col space-y-2">
            <p className="font-medium text-muted-foreground text-sm">
              QCH Pass Rate
            </p>
            <div className="flex items-center space-x-2">
              <span className="font-medium text-2xl">
                {stats.qchPassRate?.toFixed(1) || '0'}%
              </span>
              <span className="text-muted-foreground text-xs">this week</span>
            </div>
          </div>

          <div className="flex w-full flex-col space-y-2">
            <p className="font-medium text-muted-foreground text-sm">
              Pending Review
            </p>
            <div className="flex items-center space-x-2">
              <span className="font-medium text-2xl">
                {stats.pendingQch?.toLocaleString() || '0'}
              </span>
              <span className="text-muted-foreground text-xs">
                {(stats.pendingQch || 0) > 0
                  ? 'Requires review'
                  : 'All reviewed'}
              </span>
            </div>
          </div>
        </>
      );
    }

    if (statsType === 'platform') {
      return (
        <>
          <div className="flex w-full flex-col space-y-2">
            <p className="font-medium text-muted-foreground text-sm">
              Total Orders
            </p>
            <div className="flex items-center space-x-2">
              <span className="font-medium text-2xl">
                {stats.totalPlatformOrders?.toLocaleString() || '0'}
              </span>
              <span className="text-muted-foreground text-xs">all time</span>
            </div>
          </div>

          <div className="flex w-full flex-col space-y-2">
            <p className="font-medium text-muted-foreground text-sm">
              Awaiting Processing
            </p>
            <div className="flex items-center space-x-2">
              <span className="font-medium text-2xl">
                {stats.awaitingProcessing?.toLocaleString() || '0'}
              </span>
              <span className="text-muted-foreground text-xs">pending</span>
            </div>
          </div>

          <div className="flex w-full flex-col space-y-2">
            <p className="font-medium text-muted-foreground text-sm">
              Completed This Week
            </p>
            <div className="flex items-center space-x-2">
              <span className="font-medium text-2xl">
                {stats.completedThisWeek?.toLocaleString() || '0'}
              </span>
              <span className="text-green-600 text-xs">completed</span>
            </div>
          </div>

          <div className="flex w-full flex-col space-y-2">
            <p className="font-medium text-muted-foreground text-sm">
              Service Types
            </p>
            <div className="flex items-center space-x-2">
              <span className="font-medium text-2xl">
                {stats.serviceTypeBreakdown?.length || '0'}
              </span>
              <span className="text-muted-foreground text-xs">
                active types
              </span>
            </div>
          </div>
        </>
      );
    }

    // Default monetary stats for orders page
    return (
      <>
        <div className="flex w-full flex-col space-y-2">
          <p className="font-medium text-muted-foreground text-sm">
            Total Orders
          </p>
          <div className="flex items-center space-x-2">
            <span className="font-medium text-2xl">
              {stats.totalOrders?.toLocaleString() || '0'}
            </span>
            <span className="text-green-600 text-xs">
              +{stats.ordersThisWeek || 0}
            </span>
            <span className="text-muted-foreground text-xs">this week</span>
          </div>
        </div>

        <div className="flex w-full flex-col space-y-2">
          <p className="font-medium text-muted-foreground text-sm">
            Total revenue
          </p>
          <div className="flex items-center space-x-2">
            <span className="font-medium text-2xl">
              {formatCurrency(stats.totalRevenue || 0)}
            </span>
            <span
              className={`text-xs ${(stats.revenuePercentageChange || 0) >= 0 ? 'text-green-600' : 'text-destructive'}`}
            >
              {formatPercentage(stats.revenuePercentageChange || 0)}
            </span>
            <span className="text-muted-foreground text-xs">this week</span>
          </div>
        </div>

        <div className="flex w-full flex-col space-y-2">
          <p className="font-medium text-muted-foreground text-sm">
            Average Order Value
          </p>
          <div className="flex items-center space-x-2">
            <span className="font-medium text-2xl">
              {formatCurrency(stats.averageOrderValue || 0)}
            </span>
            <span
              className={`text-xs ${(stats.aovPercentageChange || 0) >= 0 ? 'text-green-600' : 'text-destructive'}`}
            >
              {formatPercentage(stats.aovPercentageChange || 0)}
            </span>
            <span className="text-muted-foreground text-xs">this week</span>
          </div>
        </div>

        <div className="flex w-full flex-col space-y-2">
          <p className="font-medium text-muted-foreground text-sm">
            Pending Orders
          </p>
          <div className="flex items-center space-x-2">
            <span className="font-medium text-2xl">
              {stats.pendingOrders?.toLocaleString() || '0'}
            </span>
            <span className="text-muted-foreground text-xs">
              {(stats.pendingOrders || 0) > 0
                ? 'Requires attention'
                : 'All processed'}
            </span>
          </div>
        </div>
      </>
    );
  };

  return (
    <div className="mb-8 grid w-full grid-cols-4 gap-8 divide-x divide-border border-border border-y border-dashed py-6">
      {renderStats()}
    </div>
  );
};
