'use client';

import { useModal } from '@/components/modal';
import { PlusIcon, UploadIcon } from '@muraadso/icons';
import { Button } from '@muraadso/ui';

export const OrderActions = () => {
  const { open } = useModal();

  const handleExport = () => {
    // TODO: Implement bulk export functionality
    console.log('Export functionality to be implemented');
  };

  return (
    <div className="flex flex-1 items-center justify-end space-x-4">
      <Button variant="outline" onClick={handleExport}>
        <UploadIcon className="size-4 text-muted-foreground" />
        <span className="ml-2">Export</span>
      </Button>

      <Button onClick={() => open('orders')}>
        <PlusIcon className="size-4" />
        <span className="ml-2">New Order</span>
      </Button>
    </div>
  );
};
