import { type Image, ImagePicker } from '@/components/image-picker';
import { OrderCreateDetailsAddressSection } from '@/components/orders/order-create/order-create-details/order-create-details-address-section';
import { useDebounce } from '@/hooks/debounce';
import { getImageUrl } from '@/lib/utils';
import { useGetCustomers, useUploadFile } from '@muraadso/api/hooks';
import { Form, Input } from '@muraadso/ui';
import React, { useState, useEffect } from 'react';
import type { UseFormReturn } from 'react-hook-form';
import type { OrderFormData } from '../order-create-schema';

type OrderCreateDetailsCustomerSectionProps = {
  form: UseFormReturn<OrderFormData>;
};

export const OrderCreateDetailsCustomerSection = ({
  form,
}: OrderCreateDetailsCustomerSectionProps) => {
  const [query, setQuery] = useState('');
  const debouncedSearch = useDebounce(query, 500);
  const { customers, isPending } = useGetCustomers({
    q: debouncedSearch,
    pageSize: 10,
  });

  const [image, setImage] = useState<Image[]>([]);
  const { mutateAsync: uploadFile } = useUploadFile();

  // Watch for existing image value from form
  const existingImageKey = form.watch('customer.image');

  // Initialize image state with existing image if present
  useEffect(() => {
    if (existingImageKey && image.length === 0) {
      setImage([
        {
          id: crypto.randomUUID(),
          url: getImageUrl(existingImageKey),
          isExisting: true,
        },
      ]);
    }
  }, [existingImageKey, image.length]);

  // Handle image changes
  const handleImageChange = async (images: Image[]) => {
    setImage(images);

    if (images.length === 0) {
      form.setValue('customer.image', '');
      return;
    }

    const newImage = images[0];
    if (newImage?.file) {
      try {
        const result = await uploadFile(newImage.file);
        form.setValue('customer.image', result.key);
      } catch (error) {
        console.error('Failed to upload customer image:', error);
        // Remove the failed image from state
        setImage([]);
        form.setValue('customer.image', '');
      }
    }
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 items-start gap-5">
        <div className="col-span-2 flex flex-col space-y-2.5">
          <Form.Label className="leading-none" optional>
            Image
          </Form.Label>
          <ImagePicker
            maxImages={1}
            value={image}
            onChange={handleImageChange}
          />
        </div>

        <div className="flex items-end">
          <Form.Field
            control={form.control}
            name="customer.name"
            render={({ field }) => (
              <Form.Item className="flex-1">
                <div className="flex w-full items-end justify-between">
                  <Form.Label>
                    Name <span className="text-destructive">*</span>
                  </Form.Label>
                  <Form.Message />
                </div>
                <Form.Control>
                  <Input placeholder="Warfaa Geedi" {...field} />
                </Form.Control>
              </Form.Item>
            )}
          />
        </div>

        <Form.Field
          control={form.control}
          name="customer.phone"
          render={({ field }) => (
            <Form.Item>
              <div className="flex w-full items-end justify-between">
                <Form.Label>
                  Phone <span className="text-destructive">*</span>
                </Form.Label>
                <Form.Message />
              </div>
              <Form.Control>
                <Input placeholder="+252 63 xxxx xxx" {...field} />
              </Form.Control>
            </Form.Item>
          )}
        />

        <Form.Field
          control={form.control}
          name="customer.email"
          render={({ field }) => (
            <Form.Item>
              <div className="flex w-full items-end justify-between">
                <Form.Label optional>
                  Email <span className="text-destructive">*</span>
                </Form.Label>
                <Form.Message />
              </div>
              <Form.Control>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  {...field}
                  value={field.value || ''}
                />
              </Form.Control>
            </Form.Item>
          )}
        />

        <Form.Field
          control={form.control}
          name="customer.nationalId"
          render={({ field }) => (
            <Form.Item>
              <div className="flex w-full items-end justify-between">
                <Form.Label optional>
                  National ID <span className="text-destructive">*</span>
                </Form.Label>
                <Form.Message />
              </div>
              <Form.Control>
                <Input
                  placeholder="1234-5678-9012"
                  {...field}
                  value={field.value || ''}
                />
              </Form.Control>
            </Form.Item>
          )}
        />
      </div>
      <OrderCreateDetailsAddressSection form={form} />
    </div>
  );
};
