import { useGetCurrentSysop, useGetLocations } from '@muraadso/api/hooks';
import { serviceTypes } from '@muraadso/models/orders';
import { Form, Input, Label, RadioGroup, Select } from '@muraadso/ui';
import { cn } from '@muraadso/ui/utils';
import React, { useEffect } from 'react';
import type { UseFormReturn } from 'react-hook-form';
import type { OrderFormData } from '../order-create-schema';

type OrderCreateDetailsServiceSectionProps = {
  form: UseFormReturn<OrderFormData>;
};

const serviceTypesWithLabels = serviceTypes.map((type) => ({
  value: type,
  label: {
    retail: 'Retail',
    consignment: 'U iibin',
    buyback: 'Ka iibsad',
    'trade-in': 'Isku bedel',
  }[type],
}));

export const OrderCreateDetailsServiceSection = ({
  form,
}: OrderCreateDetailsServiceSectionProps) => {
  const watchedServiceType = form.watch('serviceType');
  const { locations, isPending: locationsLoading } = useGetLocations();
  const { currentSysop, isPending: sysopLoading } = useGetCurrentSysop();

  const isSuperAdmin = currentSysop?.isSuperAdmin || false;
  const userLocationId = currentSysop?.locationId;

  useEffect(() => {
    if (watchedServiceType !== 'consignment') {
      form.setValue('commissionRate', 0);
      form.clearErrors('commissionRate');
    }
  }, [watchedServiceType, form.setValue, form.clearErrors]);

  // Set default location for non-superadmin sysops
  useEffect(() => {
    if (!isSuperAdmin && userLocationId && !form.getValues('locationId')) {
      form.setValue('locationId', userLocationId);
    }
  }, [isSuperAdmin, userLocationId, form]);

  return (
    <div className="flex w-full flex-col space-y-4">
      <Form.Field
        control={form.control}
        name="serviceType"
        render={({ field }) => (
          <Form.Item>
            <Form.Label>
              Service Type <span className="text-destructive">*</span>
            </Form.Label>
            <Form.Control>
              <RadioGroup
                value={field.value}
                onValueChange={field.onChange}
                className="flex flex-wrap gap-6 py-2"
              >
                {serviceTypesWithLabels.map(({ value, label }) => (
                  <Label
                    key={value}
                    htmlFor={value}
                    className={cn(
                      'flex flex-1 cursor-pointer flex-col items-center space-y-2 rounded-lg py-2.5 ring ring-input',
                      {
                        'ring-primary': value === field.value,
                      },
                    )}
                  >
                    <RadioGroup.Item id={value} value={value} />
                    <span>{label}</span>
                  </Label>
                ))}
              </RadioGroup>
            </Form.Control>
            <Form.Message />
          </Form.Item>
        )}
      />

      <div
        className={cn('grid w-full grid-cols-1 gap-5', {
          'grid-cols-2': watchedServiceType === 'consignment',
        })}
      >
        <Form.Field
          control={form.control}
          name="locationId"
          render={({ field }) => (
            <Form.Item>
              <Form.Label>
                Location <span className="text-destructive">*</span>
              </Form.Label>
              <Form.Control>
                <Select
                  value={field.value}
                  onValueChange={field.onChange}
                  disabled={!isSuperAdmin || locationsLoading || sysopLoading}
                >
                  <Select.Trigger>
                    <Select.Value
                      placeholder={
                        !isSuperAdmin
                          ? currentSysop?.location?.name || 'Loading...'
                          : 'Select location'
                      }
                    />
                  </Select.Trigger>
                  <Select.Content>
                    {locations?.map((location) => (
                      <Select.Item key={location.id} value={location.id}>
                        {location.name}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>
              </Form.Control>
              <Form.Message />
              {!isSuperAdmin && (
                <p className="mt-1 text-muted-foreground text-sm">
                  Location is based on your current showroom
                </p>
              )}
            </Form.Item>
          )}
        />

        {watchedServiceType === 'consignment' && (
          <Form.Field
            control={form.control}
            name="commissionRate"
            render={({ field }) => (
              <Form.Item>
                <Form.Label>
                  Commission rate <span className="text-destructive">*</span>
                </Form.Label>
                <Form.Control>
                  <Input
                    type="number"
                    leading={<span className="ps-0.5 text-sm">$</span>}
                    placeholder="Enter commission rate"
                    className="ps-8"
                    {...field}
                    onChange={(e) =>
                      field.onChange(
                        e.target.value === ''
                          ? undefined
                          : Number(e.target.value),
                      )
                    }
                  />
                </Form.Control>
                <Form.Message />
              </Form.Item>
            )}
          />
        )}
      </div>
    </div>
  );
};
