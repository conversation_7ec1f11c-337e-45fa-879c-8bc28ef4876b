import { AddressFormFields } from '@/components/forms/address-form-fields';
import React, { useState, useEffect } from 'react';
import type { UseFormReturn } from 'react-hook-form';
import type { OrderFormData } from '../order-create-schema';

type OrderCreateDetailsAddressSectionProps = {
  form: UseFormReturn<OrderFormData>;
};

export const OrderCreateDetailsAddressSection = ({
  form,
}: OrderCreateDetailsAddressSectionProps) => {
  const [selectedCountry, setSelectedCountry] = useState<string | undefined>();
  const [selectedCity, setSelectedCity] = useState<string | undefined>();

  const watchedStructuredAddress = form.watch('structuredAddress');

  // Update local state when form values change
  useEffect(() => {
    if (watchedStructuredAddress?.countryId !== selectedCountry) {
      setSelectedCountry(watchedStructuredAddress?.countryId);
    }
    if (watchedStructuredAddress?.cityId !== selectedCity) {
      setSelectedCity(watchedStructuredAddress?.cityId);
    }
  }, [watchedStructuredAddress, selectedCountry, selectedCity]);

  const handleCountryChange = (countryId: string | undefined) => {
    setSelectedCountry(countryId);
    // Clear city when country changes
    if (countryId !== watchedStructuredAddress?.countryId) {
      setSelectedCity(undefined);
      form.setValue('structuredAddress.cityId', '');
      form.setValue('structuredAddress.districtId', '');
    }
  };

  const handleCityChange = (cityId: string | undefined) => {
    setSelectedCity(cityId);
    // Clear district when city changes
    if (cityId !== watchedStructuredAddress?.cityId) {
      form.setValue('structuredAddress.districtId', '');
    }
  };

  return (
    <div className="space-y-4">
      <AddressFormFields
        control={form.control}
        fieldPrefix="structuredAddress"
        countryValue={selectedCountry}
        cityValue={selectedCity}
        onCountryChange={handleCountryChange}
        onCityChange={handleCityChange}
      />
    </div>
  );
};
