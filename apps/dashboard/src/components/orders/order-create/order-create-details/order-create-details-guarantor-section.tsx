import { type Image, ImagePicker } from '@/components/image-picker';
import { CitySelector } from '@/components/selectors/city-selector';
import { CountrySelector } from '@/components/selectors/country-selector';
import { DistrictSelector } from '@/components/selectors/district-selector';
import { getImageUrl } from '@/lib/utils';
import { useUploadFile } from '@muraadso/api/hooks';
import { Form, Input, Textarea } from '@muraadso/ui';
import React, { useState, useEffect } from 'react';
import type { UseFormReturn } from 'react-hook-form';
import type { OrderFormData } from '../order-create-schema';

type OrderCreateDetailsGuarantorSectionProps = {
  form: UseFormReturn<OrderFormData>;
};

export const OrderCreateDetailsGuarantorSection = ({
  form,
}: OrderCreateDetailsGuarantorSectionProps) => {
  const [image, setImage] = useState<Image[]>([]);
  const [selectedCountry, setSelectedCountry] = useState<string | undefined>();
  const [selectedCity, setSelectedCity] = useState<string | undefined>();
  const { mutateAsync: uploadFile } = useUploadFile();

  const watchedGuarantorStructuredAddress = form.watch(
    'guarantor.structuredAddress',
  );

  // Watch for existing image value from form
  const existingImageKey = form.watch('guarantor.image');

  // Initialize image state with existing image if present
  useEffect(() => {
    if (existingImageKey && image.length === 0) {
      setImage([
        {
          id: crypto.randomUUID(),
          url: getImageUrl(existingImageKey),
          isExisting: true,
        },
      ]);
    }
  }, [existingImageKey, image.length]);

  // Handle image changes
  const handleImageChange = async (images: Image[]) => {
    setImage(images);

    if (images.length === 0) {
      form.setValue('guarantor.image', '');
      return;
    }

    const newImage = images[0];
    if (newImage?.file) {
      try {
        const result = await uploadFile(newImage.file);
        form.setValue('guarantor.image', result.key);
      } catch (error) {
        console.error('Failed to upload guarantor image:', error);
        // Remove the failed image from state
        setImage([]);
        form.setValue('guarantor.image', '');
      }
    }
  };

  // Update local state when form values change
  useEffect(() => {
    if (watchedGuarantorStructuredAddress?.countryId !== selectedCountry) {
      setSelectedCountry(watchedGuarantorStructuredAddress?.countryId);
    }
    if (watchedGuarantorStructuredAddress?.cityId !== selectedCity) {
      setSelectedCity(watchedGuarantorStructuredAddress?.cityId);
    }
  }, [watchedGuarantorStructuredAddress, selectedCountry, selectedCity]);

  const handleCountryChange = (countryId: string | undefined) => {
    setSelectedCountry(countryId);
    // Clear city when country changes
    if (countryId !== watchedGuarantorStructuredAddress?.countryId) {
      setSelectedCity(undefined);
      form.setValue('guarantor.structuredAddress.cityId', '');
      form.setValue('guarantor.structuredAddress.districtId', '');
    }
  };

  const handleCityChange = (cityId: string | undefined) => {
    setSelectedCity(cityId);
    // Clear district when city changes
    if (cityId !== watchedGuarantorStructuredAddress?.cityId) {
      form.setValue('guarantor.structuredAddress.districtId', '');
    }
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-5">
        <div className="col-span-2 flex flex-col space-y-2.5">
          <Form.Label className="leading-none" optional>
            Image
          </Form.Label>
          <ImagePicker
            maxImages={1}
            value={image}
            onChange={handleImageChange}
          />
        </div>

        <Form.Field
          control={form.control}
          name="guarantor.name"
          render={({ field }) => (
            <Form.Item className="flex-1">
              <Form.Label>
                Name <span className="text-destructive">*</span>
              </Form.Label>
              <Form.Control>
                <Input placeholder="Warfaa Geedi" {...field} />
              </Form.Control>
              <Form.Message />
            </Form.Item>
          )}
        />

        <Form.Field
          control={form.control}
          name="guarantor.phone"
          render={({ field }) => (
            <Form.Item>
              <Form.Label>
                Phone <span className="text-destructive">*</span>
              </Form.Label>
              <Form.Control>
                <Input placeholder="+252 63 xxxx xxx" {...field} />
              </Form.Control>
              <Form.Message />
            </Form.Item>
          )}
        />

        <Form.Field
          control={form.control}
          name="guarantor.email"
          render={({ field }) => (
            <Form.Item>
              <Form.Label optional>Email</Form.Label>
              <Form.Control>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  {...field}
                  value={field.value || ''}
                />
              </Form.Control>
              <Form.Message />
            </Form.Item>
          )}
        />

        <Form.Field
          control={form.control}
          name="guarantor.nationalId"
          render={({ field }) => (
            <Form.Item>
              <Form.Label optional>National ID</Form.Label>
              <Form.Control>
                <Input
                  type="text"
                  placeholder="1234-5678-9012"
                  {...field}
                  value={field.value || ''}
                />
              </Form.Control>
              <Form.Message />
            </Form.Item>
          )}
        />

        <div className="col-span-2">
          <h5 className="mb-4 font-medium text-sm">Address (Optional)</h5>
          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <Form.Field
                control={form.control}
                name="guarantor.structuredAddress.countryId"
                render={({ field }) => (
                  <Form.Item>
                    <div className="flex w-full items-end justify-between">
                      <Form.Label optional>Country</Form.Label>
                      <Form.Message />
                    </div>
                    <Form.Control>
                      <CountrySelector
                        value={field.value}
                        onValueChange={(value) => {
                          field.onChange(value);
                          handleCountryChange(value);
                        }}
                        placeholder="Select country..."
                      />
                    </Form.Control>
                  </Form.Item>
                )}
              />

              <Form.Field
                control={form.control}
                name="guarantor.structuredAddress.cityId"
                render={({ field }) => (
                  <Form.Item>
                    <div className="flex w-full items-end justify-between">
                      <Form.Label optional>City</Form.Label>
                      <Form.Message />
                    </div>
                    <Form.Control>
                      <CitySelector
                        countryId={selectedCountry || ''}
                        value={field.value}
                        onValueChange={(value) => {
                          field.onChange(value);
                          handleCityChange(value);
                        }}
                        disabled={!selectedCountry}
                        placeholder="Select city..."
                      />
                    </Form.Control>
                  </Form.Item>
                )}
              />
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <Form.Field
                control={form.control}
                name="guarantor.structuredAddress.districtId"
                render={({ field }) => (
                  <Form.Item>
                    <div className="flex w-full items-end justify-between">
                      <Form.Label optional>District</Form.Label>
                      <Form.Message />
                    </div>
                    <Form.Control>
                      <DistrictSelector
                        cityId={selectedCity}
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={!selectedCity}
                        placeholder="Select district..."
                      />
                    </Form.Control>
                  </Form.Item>
                )}
              />

              <Form.Field
                control={form.control}
                name="guarantor.structuredAddress.street"
                render={({ field }) => (
                  <Form.Item>
                    <div className="flex w-full items-end justify-between">
                      <Form.Label optional>Street Address</Form.Label>
                      <Form.Message />
                    </div>
                    <Form.Control>
                      <Input
                        placeholder="Enter street address"
                        {...field}
                        value={field.value || ''}
                      />
                    </Form.Control>
                  </Form.Item>
                )}
              />
            </div>

            <Form.Field
              control={form.control}
              name="guarantor.structuredAddress.additionalInfo"
              render={({ field }) => (
                <Form.Item>
                  <div className="flex w-full items-end justify-between">
                    <Form.Label optional>Additional Information</Form.Label>
                    <Form.Message />
                  </div>
                  <Form.Control>
                    <Textarea
                      rows={3}
                      placeholder="Landmarks, directions, or other details"
                      {...field}
                      value={field.value || ''}
                    />
                  </Form.Control>
                </Form.Item>
              )}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
