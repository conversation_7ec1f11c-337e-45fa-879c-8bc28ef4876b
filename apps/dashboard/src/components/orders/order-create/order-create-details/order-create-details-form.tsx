import { Separator } from '@muraadso/ui';
import React from 'react';
import type { UseFormReturn } from 'react-hook-form';
import type { OrderFormData } from '../order-create-schema';
import { OrderCreateDetailsCustomerSection } from './order-create-details-customer-section';
import { OrderCreateDetailsGuarantorSection } from './order-create-details-guarantor-section';
import { OrderCreateDetailsServiceSection } from './order-create-details-service-section';

type OrderCreateCustomerFormProps = {
  form: UseFormReturn<OrderFormData>;
};

export const OrderCreateDetailsForm = ({
  form,
}: OrderCreateCustomerFormProps) => {
  const watchedService = form.watch('serviceType');

  return (
    <div className="flex flex-col items-center p-16 pb-64">
      <div className="flex w-full max-w-[720px] flex-col space-y-8">
        <div className="flex flex-col space-y-6">
          <h4 className="font-medium text-lg">General</h4>
          <OrderCreateDetailsServiceSection form={form} />
        </div>

        <Separator />

        <div className="flex flex-col space-y-6">
          <h4 className="font-medium text-lg">Customer</h4>
          <OrderCreateDetailsCustomerSection form={form} />
        </div>

        {watchedService !== 'retail' && (
          <>
            <Separator />

            <div className="flex flex-col space-y-6">
              <h4 className="font-medium text-lg">Guarantor</h4>
              <OrderCreateDetailsGuarantorSection form={form} />
            </div>
          </>
        )}
      </div>
    </div>
  );
};
