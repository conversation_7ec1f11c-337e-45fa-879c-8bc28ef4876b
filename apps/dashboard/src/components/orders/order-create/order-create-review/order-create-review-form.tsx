import { formatPrice, getImageUrl } from '@/lib/utils';
import { formatStructuredAddress } from '@/utils/address-formatter';
import {
  useGetCities,
  useGetCountries,
  useGetDistricts,
} from '@muraadso/api/hooks';
import { CheckIcon, ImageIcon } from '@muraadso/icons';
import type { Product } from '@muraadso/models/products';
import type { Variant } from '@muraadso/models/variants';
import { Avatar, Button, Card, Form, Separator, Textarea } from '@muraadso/ui';
import React from 'react';
import type { UseFormReturn } from 'react-hook-form';
import type { OrderFormData } from '../order-create-schema';

type OrderCreateReviewFormProps = {
  form: UseFormReturn<OrderFormData>;
};

export const OrderCreateReviewForm = ({ form }: OrderCreateReviewFormProps) => {
  const formData = form.getValues();
  const { countries } = useGetCountries();
  const { cities } = useGetCities();
  const { districts } = useGetDistricts();

  const totalAmount =
    formData.items?.reduce(
      (sum, item) => sum + (item.unitPrice || 0) * (item.quantity || 0),
      0,
    ) || 0;

  // Format addresses for display
  const customerAddress = formData.structuredAddress
    ? formatStructuredAddress(formData.structuredAddress, {
        countries,
        cities,
        districts,
      })
    : formData.address || '';

  const guarantorAddress = formData.guarantor?.structuredAddress
    ? formatStructuredAddress(formData.guarantor.structuredAddress, {
        countries,
        cities,
        districts,
      })
    : '';

  return (
    <div className="flex flex-col items-center p-16 pb-64">
      <div className="flex w-full max-w-[720px] flex-col space-y-8">
        <div className="flex flex-col space-y-1">
          <h4 className="font-medium text-lg">Review Order</h4>
          <p className="text-muted-foreground text-sm">
            Please review all details before creating the order.
          </p>
        </div>

        {/* Service Information */}
        <Card>
          <Card.Header className="flex flex-row items-center justify-between border-border border-b">
            <Card.Title>Details</Card.Title>
          </Card.Header>
          <Card.Content className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="font-medium text-sm">Service Type</p>
                <p className="flex items-center space-x-3 py-2 text-muted-foreground text-sm">
                  <span className="italic">
                    {getServiceTypeLabel(formData.serviceType)}
                  </span>
                </p>
              </div>
              <div>
                <p className="font-medium text-sm">Total Amount</p>
                <p className="font-semibold text-lg text-primary">
                  {formatPrice(totalAmount)}
                </p>
              </div>
              <div>
                <p className="font-medium text-sm">Total Items</p>
                <p className="text-muted-foreground text-sm">
                  {formData.items?.length || 0} item(s)
                </p>
              </div>
              {formData.serviceType === 'consignment' &&
                formData.commissionRate && (
                  <div>
                    <p className="font-medium text-sm">Commission Rate</p>
                    <p className="text-muted-foreground text-sm">
                      ${formData.commissionRate}
                    </p>
                  </div>
                )}
            </div>
          </Card.Content>
        </Card>

        {/* Customer Information */}
        <Card>
          <Card.Header className="flex flex-row items-center justify-between border-border border-b">
            <Card.Title>Customer Information</Card.Title>
          </Card.Header>
          <Card.Content className="flex space-x-8 space-y-4 px-6">
            <div className="size-24">
              <Avatar className="size-full rounded-sm">
                <Avatar.Image
                  src={getImageUrl(formData.customer?.image) || undefined}
                />
                <Avatar.Fallback>
                  <ImageIcon className="size-6" />
                </Avatar.Fallback>
              </Avatar>
            </div>

            <div className="flex flex-col items-start space-y-3">
              <div className="flex w-full">
                <p className="w-20 font-medium text-sm">Name:</p>
                <p className="text-muted-foreground text-sm">
                  {formData.customer?.name || 'Not provided'}
                </p>
              </div>

              <div className="flex w-full">
                <p className="w-20 font-medium text-sm">Phone:</p>
                <p className="text-muted-foreground text-sm">
                  {formData.customer?.phone || 'Not provided'}
                </p>
              </div>
              <div className="flex w-full">
                <p className="w-20 font-medium text-sm">Email:</p>
                <p className="text-muted-foreground text-sm">
                  {formData.customer?.email || 'Not provided'}
                </p>
              </div>
              {customerAddress && (
                <div className="flex w-full">
                  <p className="w-20 font-medium text-sm">Address:</p>
                  <p className="text-muted-foreground text-sm">
                    {customerAddress}
                  </p>
                </div>
              )}
            </div>
          </Card.Content>
        </Card>

        {/* Guarantor Information (if applicable) */}
        {formData.serviceType !== 'retail' && formData.guarantor && (
          <Card>
            <Card.Header className="flex flex-row items-center justify-between border-border border-b">
              <Card.Title>Guarantor Information</Card.Title>
            </Card.Header>
            <Card.Content className="space-y-4">
              {formData.guarantor.image && (
                <div>
                  <p className="mb-2 font-medium text-sm">Image</p>
                  <Avatar className="size-16">
                    <Avatar.Image src={getImageUrl(formData.guarantor.image)} />
                    <Avatar.Fallback>
                      <ImageIcon className="size-6" />
                    </Avatar.Fallback>
                  </Avatar>
                </div>
              )}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="font-medium text-sm">Name</p>
                  <p className="text-muted-foreground text-sm">
                    {formData.guarantor.name || 'Not provided'}
                  </p>
                </div>
                <div>
                  <p className="font-medium text-sm">Phone</p>
                  <p className="text-muted-foreground text-sm">
                    {formData.guarantor.phone || 'Not provided'}
                  </p>
                </div>
                <div>
                  <p className="font-medium text-sm">Email</p>
                  <p className="text-muted-foreground text-sm">
                    {formData.guarantor.email || 'Not provided'}
                  </p>
                </div>
                <div>
                  <p className="font-medium text-sm">National ID</p>
                  <p className="text-muted-foreground text-sm">
                    {formData.guarantor.nationalId || 'Not provided'}
                  </p>
                </div>
              </div>
              {guarantorAddress && (
                <div>
                  <p className="font-medium text-sm">Address</p>
                  <p className="text-muted-foreground text-sm">
                    {guarantorAddress}
                  </p>
                </div>
              )}
            </Card.Content>
          </Card>
        )}

        {/* Order Items */}
        <Card>
          <Card.Header className="flex flex-row items-center justify-between border-border border-b">
            <Card.Title>Order Items ⌈{formData.items?.length || 0}⌉</Card.Title>
          </Card.Header>
          <Card.Content className="space-y-4">
            {!formData.items || formData.items.length === 0 ? (
              <div className="py-8 text-center text-muted-foreground">
                No items selected
              </div>
            ) : (
              <>
                <div className="space-y-3">
                  {formData.items.map((item, index) => {
                    const product = item.productSnapshot as Product;
                    const variant = item.variantSnapshot as Variant;

                    const image = variant?.images[0] || product.images[0];

                    const isLaptop = product?.category?.slug === 'laptops';
                    const identifier = isLaptop
                      ? item.metadata?.serialNumber
                      : item.metadata?.imei;

                    return (
                      <div
                        key={index}
                        className="flex items-center space-x-4 rounded-lg border border-border p-3"
                      >
                        <Avatar className="size-12 rounded-sm">
                          <Avatar.Image
                            src={
                              image?.path ? getImageUrl(image?.path) : undefined
                            }
                            alt=""
                          />
                          <Avatar.Fallback className="rounded-sm">
                            <ImageIcon className="size-5 text-muted-foreground" />
                          </Avatar.Fallback>
                        </Avatar>

                        <div className="flex-1">
                          <h4 className="font-medium text-sm">
                            {product?.brand?.name} {item.productSnapshot?.name}
                          </h4>
                          <p className="text-muted-foreground text-xs">
                            {item.variantSnapshot?.name}
                          </p>
                          {formData.serviceType !== 'retail' && identifier && (
                            <p className="text-muted-foreground text-xs">
                              {isLaptop ? 'Serial:' : 'IMEI:'} {identifier}
                            </p>
                          )}
                        </div>

                        <div className="text-right">
                          <p className="font-medium text-sm">
                            {item.quantity} × {formatPrice(item.unitPrice)}
                          </p>
                          <p className="text-muted-foreground text-xs">
                            = {formatPrice(item.unitPrice * item.quantity)}
                          </p>
                        </div>
                      </div>
                    );
                  })}
                </div>
                <Separator />
                <div className="flex justify-between font-medium text-lg">
                  <span>Total Amount:</span>
                  <span>{formatPrice(totalAmount)}</span>
                </div>
              </>
            )}
          </Card.Content>
        </Card>

        {/* Order Notes */}
        <Card>
          <Card.Header>
            <Card.Title>Order Notes</Card.Title>
          </Card.Header>
          <Card.Content>
            <Form.Field
              control={form.control}
              name="note"
              render={({ field }) => (
                <Form.Item>
                  <Form.Control>
                    <Textarea
                      placeholder="Add any additional notes for this order..."
                      className="min-h-[100px]"
                      {...field}
                      value={field.value || ''}
                    />
                  </Form.Control>
                  <Form.Message />
                </Form.Item>
              )}
            />
          </Card.Content>
        </Card>
      </div>
    </div>
  );
};

const getServiceTypeLabel = (serviceType: string) => {
  const labels = {
    retail: 'Retail',
    consignment: 'U iibin',
    buyback: 'Ka iibsad',
    'trade-in': 'Isku bedel',
  };
  return labels[serviceType as keyof typeof labels] || serviceType;
};
