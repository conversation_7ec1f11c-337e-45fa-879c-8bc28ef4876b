import { formatPrice, getImageUrl } from '@/lib/utils';
import { useGetProducts } from '@muraadso/api/hooks';
import {
  ImageIcon,
  MinusIcon,
  PlusIcon,
  SearchIcon,
  ShoppingCartIcon,
  TrashIcon,
} from '@muraadso/icons';
import type { CreateOrderItem } from '@muraadso/models/orders';
import type { Product } from '@muraadso/models/products';
import type { Variant } from '@muraadso/models/variants';
import {
  Avatar,
  Button,
  Card,
  Dialog,
  Input,
  Select,
  Table,
} from '@muraadso/ui';
import React, { useState } from 'react';
import type { UseFormReturn } from 'react-hook-form';
import type { OrderFormData } from '../order-create-schema';

type OrderCreateProductsFormProps = {
  form: UseFormReturn<OrderFormData>;
};

export const OrderCreateProductsForm = ({
  form,
}: OrderCreateProductsFormProps) => {
  const [isProductDialogOpen, setIsProductDialogOpen] = useState(false);

  const items = form.watch('items') || [];
  const serviceType = form.watch('serviceType');

  const addProduct = (product: Product, variant: Variant) => {
    const priceKey = {
      retail: 'retailPrice',
      consignment: 'consignmentPrice',
      buyback: 'buybackPrice',
      'trade-in': 'tradeInPrice',
    }[serviceType] as keyof typeof variant.prices;

    const unitPrice = variant.prices[priceKey] || 0;

    const existingItemIndex = items.findIndex(
      (item) => item.productId === product.id && item.variantId === variant.id,
    );

    if (existingItemIndex >= 0) {
      const updatedItems = [...items];
      const existingItem = updatedItems[existingItemIndex];
      if (existingItem) {
        updatedItems[existingItemIndex] = {
          ...existingItem,
          quantity: existingItem.quantity + 1,
        };
        form.setValue('items', updatedItems);
      }
    } else {
      const newItem: CreateOrderItem = {
        productId: product.id,
        variantId: variant.id,
        quantity: 1,
        unitPrice,
        productSnapshot: product,
        variantSnapshot: variant,
      };
      form.setValue('items', [...items, newItem]);
    }

    setIsProductDialogOpen(false);
  };

  const updateItemQuantity = (index: number, quantity: number) => {
    if (quantity <= 0) {
      removeItem(index);
      return;
    }

    const updatedItems = [...items];
    const updatedItem = updatedItems[index];
    if (updatedItem) {
      updatedItems[index] = { ...updatedItem, quantity };
      form.setValue('items', updatedItems);
    }
  };

  const updateItemPrice = (index: number, unitPrice: number) => {
    const updatedItems = [...items];
    const updatedItem = updatedItems[index];
    if (updatedItem) {
      updatedItems[index] = { ...updatedItem, unitPrice };
      form.setValue('items', updatedItems);
    }
  };

  const updateItemMetadata = (
    index: number,
    value: string,
    isLaptop = false,
  ) => {
    const updatedItems = [...items];
    const updatedItem = updatedItems[index];
    if (updatedItem) {
      // Normalize value: remove spaces, dashes, and keep only alphanumeric for serial numbers
      const normalizedValue = isLaptop
        ? value
            .replace(/[\s-]/g, '')
            .toUpperCase() // Serial numbers can have letters
        : value.replace(/[\s-]/g, ''); // IMEI only digits

      const metadataKey = isLaptop ? 'serialNumber' : 'imei';

      updatedItems[index] = {
        ...updatedItem,
        metadata: {
          ...updatedItem.metadata,
          [metadataKey]: normalizedValue,
        },
      };
      form.setValue('items', updatedItems);
    }
  };

  const validateIdentifier = (value: string, isLaptop: boolean): boolean => {
    if (isLaptop) {
      // Serial number validation: 6-20 alphanumeric characters
      const normalizedValue = value.replace(/[\s-]/g, '');
      return /^[A-Z0-9]{6,20}$/i.test(normalizedValue);
    }
    // IMEI validation: exactly 15 digits
    const normalizedValue = value.replace(/[\s-]/g, '');
    return /^\d{15}$/.test(normalizedValue);
  };

  const removeItem = (index: number) => {
    const updatedItems = items.filter((_, i) => i !== index);
    form.setValue('items', updatedItems);
  };

  const totalAmount = items.reduce(
    (sum, item) => sum + (item.unitPrice || 0) * (item.quantity || 0),
    0,
  );

  return (
    <div className="flex flex-col items-center p-16 pb-64">
      <div className="flex w-full max-w-[820px] flex-col space-y-8">
        <div className="flex flex-col space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <h4 className="font-medium text-lg">Order Items</h4>
              <p className="text-muted-foreground text-sm">
                Add items to the order{' '}
              </p>
            </div>
            <Button
              type="button"
              onClick={() => setIsProductDialogOpen(true)}
              className="flex items-center gap-2"
            >
              <PlusIcon className="size-4" />
              Add Products
            </Button>
          </div>
        </div>

        {items.length === 0 ? (
          <Card className="border-dashed">
            <Card.Content className="flex flex-col items-center justify-center py-4">
              <ShoppingCartIcon className="mb-4 size-10 text-muted-foreground" />
              <h3 className="mb-0.5 font-medium text-lg">
                No products selected
              </h3>
              <p className="mb-4 text-center text-muted-foreground text-sm">
                Add products to this order to get started
              </p>
              <Button
                type="button"
                onClick={() => setIsProductDialogOpen(true)}
                className="flex items-center gap-2"
              >
                <PlusIcon className="size-4" />
                Add Products
              </Button>
            </Card.Content>
          </Card>
        ) : (
          <Card className="rounded-lg p-0">
            <Card.Content className="p-0">
              <Table>
                <Table.Header>
                  <Table.Row>
                    <Table.Head className="w-[300px]">Product</Table.Head>
                    {serviceType !== 'retail' && (
                      <Table.Head className="w-[140px]">IMEI/Serial</Table.Head>
                    )}
                    <Table.Head className="w-[120px]">Quantity</Table.Head>
                    <Table.Head className="w-[120px]">Unit Price</Table.Head>
                    <Table.Head className="w-[120px]">Total</Table.Head>
                    <Table.Head className="w-[50px]" />
                  </Table.Row>
                </Table.Header>
                <Table.Body>
                  {items.map((item, index) => (
                    <OrderItemRow
                      key={`${item.productId}-${item.variantId}`}
                      item={item}
                      index={index}
                      serviceType={serviceType}
                      onUpdateQuantity={updateItemQuantity}
                      onUpdatePrice={updateItemPrice}
                      onUpdateMetadata={updateItemMetadata}
                      onRemove={removeItem}
                      validateIdentifier={validateIdentifier}
                    />
                  ))}
                </Table.Body>
                <Table.Footer>
                  <Table.Row>
                    <Table.Cell className="text-right font-medium">
                      Total Amount:
                    </Table.Cell>
                    {serviceType !== 'retail' && <Table.Cell />}
                    <Table.Cell />
                    <Table.Cell />
                    <Table.Cell className="font-bold text-lg">
                      {formatPrice(totalAmount)}
                    </Table.Cell>
                    <Table.Cell />
                  </Table.Row>
                </Table.Footer>
              </Table>
            </Card.Content>
          </Card>
        )}

        <ProductSelectionDialog
          open={isProductDialogOpen}
          onClose={() => setIsProductDialogOpen(false)}
          onAddProduct={addProduct}
          serviceType={serviceType}
        />
      </div>
    </div>
  );
};

type OrderItemRowProps = {
  item: CreateOrderItem;
  index: number;
  serviceType: string;
  onUpdateQuantity: (index: number, quantity: number) => void;
  onUpdatePrice: (index: number, price: number) => void;
  onUpdateMetadata: (index: number, value: string, isLaptop?: boolean) => void;
  onRemove: (index: number) => void;
  validateIdentifier: (value: string, isLaptop: boolean) => boolean;
};

const OrderItemRow = ({
  item,
  index,
  serviceType,
  onUpdateQuantity,
  onUpdatePrice,
  onUpdateMetadata,
  onRemove,
  validateIdentifier,
}: OrderItemRowProps) => {
  const product = item.productSnapshot as Product;
  const variant = item.variantSnapshot as Variant;

  const image = variant?.images[0] || product.images[0];

  const totalPrice = (item.unitPrice || 0) * (item.quantity || 0);

  // Check if product is a laptop
  const isLaptop = product?.category?.slug === 'laptops';
  const currentValue = isLaptop
    ? item.metadata?.serialNumber || ''
    : item.metadata?.imei || '';

  const [identifierInput, setIdentifierInput] = React.useState(currentValue);
  const [identifierError, setIdentifierError] = React.useState('');

  React.useEffect(() => {
    const newValue = isLaptop
      ? item.metadata?.serialNumber || ''
      : item.metadata?.imei || '';
    setIdentifierInput(newValue);
  }, [item.metadata, isLaptop]);

  const handleIdentifierChange = (value: string) => {
    setIdentifierInput(value);

    if (identifierError) {
      setIdentifierError('');
    }

    if (value.trim()) {
      const maxLength = isLaptop ? 20 : 17; // 20 for serial, 17 for IMEI with spaces/dashes
      if (value.length <= maxLength) {
        onUpdateMetadata(index, value, isLaptop);

        // Validate on complete input
        const normalizedValue = value.replace(/[\s-]/g, '');
        const minLength = isLaptop ? 6 : 15;
        if (
          normalizedValue.length >= minLength &&
          !validateIdentifier(value, isLaptop)
        ) {
          setIdentifierError(
            isLaptop ? 'Invalid serial number format' : 'Invalid IMEI format',
          );
        }
      }
    } else {
      onUpdateMetadata(index, '', isLaptop);
    }
  };

  const handleIdentifierBlur = () => {
    if (
      identifierInput.trim() &&
      !validateIdentifier(identifierInput, isLaptop)
    ) {
      setIdentifierError(
        isLaptop
          ? 'Serial number must be 6-20 alphanumeric characters'
          : 'IMEI must be exactly 15 digits',
      );
    }
  };

  return (
    <Table.Row>
      <Table.Cell>
        <div className="flex items-center space-x-3">
          <Avatar className="size-12 rounded-sm">
            <Avatar.Image
              src={image?.path ? getImageUrl(image.path) : undefined}
              alt=""
            />
            <Avatar.Fallback className="rounded-sm">
              <ImageIcon className="size-5 text-muted-foreground" />
            </Avatar.Fallback>
          </Avatar>
          <div className="flex flex-col space-y-1">
            <p className="font-medium text-sm">{product?.name}</p>
            <p className="text-muted-foreground text-xs">{variant?.name}</p>
          </div>
        </div>
      </Table.Cell>
      {serviceType !== 'retail' && (
        <Table.Cell>
          <div className="space-y-1">
            <Input
              type="text"
              value={identifierInput}
              onChange={(e) => handleIdentifierChange(e.target.value)}
              onBlur={handleIdentifierBlur}
              placeholder={isLaptop ? 'Serial Number' : 'IMEI'}
              className={`w-32 ${identifierError ? 'border-red-500' : ''}`}
              maxLength={isLaptop ? 20 : 17} // 20 for serial, 17 for IMEI with spaces/dashes
            />
            {identifierError && (
              <p className="text-red-500 text-xs">{identifierError}</p>
            )}
          </div>
        </Table.Cell>
      )}
      <Table.Cell>
        <div className="flex items-center space-x-1">
          <Button
            type="button"
            size="icon"
            variant="ghost"
            className="size-8"
            onClick={() => onUpdateQuantity(index, (item.quantity || 0) - 1)}
          >
            <MinusIcon className="size-3" />
          </Button>
          <span>{item.quantity || 0}</span>
          <Button
            type="button"
            size="icon"
            variant="ghost"
            className="size-8"
            onClick={() => onUpdateQuantity(index, (item.quantity || 0) + 1)}
          >
            <PlusIcon className="size-3" />
          </Button>
        </div>
      </Table.Cell>
      <Table.Cell>
        <Input
          type="number"
          value={item.unitPrice || 0}
          onChange={(e) =>
            onUpdatePrice(index, Number.parseFloat(e.target.value) || 0)
          }
          className="w-[5.5rem]"
          min="0"
          step="0.01"
        />
      </Table.Cell>
      <Table.Cell className="font-medium">{formatPrice(totalPrice)}</Table.Cell>
      <Table.Cell>
        <Button
          type="button"
          size="icon"
          variant="ghost"
          className="size-8 text-destructive hover:text-destructive"
          onClick={() => onRemove(index)}
        >
          <TrashIcon className="size-3" />
        </Button>
      </Table.Cell>
    </Table.Row>
  );
};

type ProductSelectionDialogProps = {
  open: boolean;
  onClose: () => void;
  onAddProduct: (product: Product, variant: Variant) => void;
  serviceType: string;
};

const ProductSelectionDialog = ({
  open,
  onClose,
  onAddProduct,
  serviceType,
}: ProductSelectionDialogProps) => {
  const [searchQuery, setSearchQuery] = useState('');

  const { products, isPending } = useGetProducts({
    q: searchQuery,
    pageSize: 20,
  });

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <Dialog.Content className="h-[calc(100%-4rem)] min-w-6xl">
        <Dialog.Body
          title="Select Products"
          description="Choose products to add to this order"
        >
          <div className="flex size-full flex-col space-y-4">
            <div className="relative">
              <SearchIcon className="-translate-y-1/2 absolute top-1/2 left-3 size-4 text-muted-foreground" />
              <Input
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="overflow-y-auto">
              {isPending ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-muted-foreground">
                    Loading products...
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-1 gap-3 sm:grid-cols-3">
                  {products.map((product) => (
                    <ProductSelectionCard
                      key={product.id}
                      product={product}
                      serviceType={serviceType}
                      onAddProduct={onAddProduct}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        </Dialog.Body>
      </Dialog.Content>
    </Dialog>
  );
};

type ProductSelectionCardProps = {
  product: Product;
  serviceType: string;
  onAddProduct: (product: Product, variant: Variant) => void;
};

const ProductSelectionCard = ({
  product,
  serviceType,
  onAddProduct,
}: ProductSelectionCardProps) => {
  const [selectedVariantId, setSelectedVariantId] = useState(
    product.variants[0]?.id || '',
  );

  const selectedVariant = product.variants.find(
    (v) => v.id === selectedVariantId,
  );

  const image = selectedVariant?.images[0] || product.images[0];

  const priceKey = {
    retail: 'retailPrice',
    consignment: 'consignmentPrice',
    buyback: 'buybackPrice',
    'trade-in': 'tradeInPrice',
  }[serviceType];

  //@ts-expect-error skip it
  const unitPrice = selectedVariant?.prices[priceKey] || 0;

  const handleAdd = () => {
    if (selectedVariant) {
      onAddProduct(product, selectedVariant);
    }
  };

  return (
    <Card className="transition-shadow hover:shadow-md">
      <Card.Content className="p-0 px-4">
        <div className="flex items-start space-x-3">
          <Avatar className="size-16 rounded-sm">
            <Avatar.Image
              src={image?.path ? getImageUrl(image?.path) : undefined}
              alt=""
            />
            <Avatar.Fallback className="rounded-sm">
              <ImageIcon className="size-6 text-muted-foreground" />
            </Avatar.Fallback>
          </Avatar>

          <div className="flex-1 space-y-2">
            <div>
              <h4 className="font-medium text-sm">
                {product.brand.name} {product.name}
              </h4>
              <p className="text-muted-foreground text-xs">
                {product.category.name}
              </p>
            </div>

            {product.variants.length > 1 && (
              <Select
                value={selectedVariantId}
                onValueChange={setSelectedVariantId}
              >
                <Select.Trigger className="h-8 text-xs">
                  <Select.Value />
                </Select.Trigger>
                <Select.Content>
                  {product.variants.map((variant) => (
                    <Select.Item key={variant.id} value={variant.id}>
                      {variant.name}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select>
            )}

            <div className="flex items-center justify-between">
              <span className="font-medium text-sm">
                {formatPrice(unitPrice)}
              </span>
              <Button
                type="button"
                size="sm"
                onClick={handleAdd}
                disabled={!selectedVariant}
              >
                Add
              </Button>
            </div>
          </div>
        </div>
      </Card.Content>
    </Card>
  );
};
