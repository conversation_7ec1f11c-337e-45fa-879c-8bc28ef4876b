import { createOrderSchema } from '@muraadso/models/orders';
import z from 'zod/v4';

const structuredAddressSchema = z.object({
  countryId: z.string().nonempty('Please select a country'),
  cityId: z.string().nonempty('Please select a city'),
  districtId: z.string().nonempty('Please select a district'),
  street: z.string().nonempty('Street address is required'),
  additionalInfo: z.string().optional(),
});

// Guarantor structured address schema - all fields optional
const guarantorStructuredAddressSchema = z.object({
  countryId: z.string().optional(),
  cityId: z.string().optional(),
  districtId: z.string().optional(),
  street: z.string().optional(),
  additionalInfo: z.string().optional(),
});

const guarantorWithStructuredAddressSchema = z.object({
  name: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().optional(),
  nationalId: z.string().optional(),
  image: z.string().optional(),
  structuredAddress: guarantorStructuredAddressSchema.optional(),
});

export const orderFormSchema = createOrderSchema.extend({
  structuredAddress: structuredAddressSchema,
  guarantor: guarantorWithStructuredAddressSchema.optional(),
  locationId: z.uuid().optional(),
});

// Dynamic schema for validation based on user role
export const createOrderFormSchema = (isSuperAdmin: boolean) => {
  return orderFormSchema.extend({
    locationId: isSuperAdmin
      ? z.uuid().optional()
      : z.uuid({ message: 'Location is required' }),
  });
};

export type OrderFormData = z.infer<ReturnType<typeof createOrderFormSchema>>;
export type StructuredAddress = z.infer<typeof structuredAddressSchema>;
export type GuarantorStructuredAddress = z.infer<
  typeof guarantorStructuredAddressSchema
>;

export const defaultStructuredAddress: StructuredAddress = {
  countryId: '',
  cityId: '',
  districtId: '',
  street: '',
  additionalInfo: '',
};

export const defaultGuarantorStructuredAddress: GuarantorStructuredAddress = {
  countryId: '',
  cityId: '',
  districtId: '',
  street: '',
  additionalInfo: '',
};
