import { formatStructuredAddress } from '@/utils/address-formatter';
import { printOrder } from '@/utils/print-order';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  useCreateOrder,
  useGetCities,
  useGetCountries,
  useGetCurrentSysop,
  useGetDistricts,
} from '@muraadso/api/hooks';
import type { CreateOrder, Order } from '@muraadso/models/orders';
import {
  Button,
  FocusModal,
  Form,
  ProgressTabs,
  type ProgressTabsStatus,
  VisuallyHidden,
  toast,
} from '@muraadso/ui';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { OrderCreateDetailsForm } from './order-create-details/order-create-details-form';
import { OrderCreateProductsForm } from './order-create-products/order-create-products-form';
import { OrderCreateReviewForm } from './order-create-review/order-create-review-form';
import {
  type OrderFormData,
  createOrderFormSchema,
  defaultGuarantorStructuredAddress,
  defaultStructuredAddress,
} from './order-create-schema';

enum Tab {
  DETAILS = 'customer',
  PRODUCTS = 'products',
  REVIEW = 'review',
}
type TabState = Record<Tab, ProgressTabsStatus>;

export const OrderCreateForm = ({
  open,
  onClose,
}: {
  open: boolean;
  onClose: (order?: Order) => void;
}) => {
  const [tab, setTab] = useState<Tab>(Tab.DETAILS);
  const [tabState, setTabState] = useState<TabState>({
    [Tab.DETAILS]: 'in-progress',
    [Tab.PRODUCTS]: 'not-started',
    [Tab.REVIEW]: 'not-started',
  });
  const { mutateAsync: createOrder, isPending: isCreating } = useCreateOrder();
  const { countries } = useGetCountries();
  const { cities } = useGetCities();
  const { districts } = useGetDistricts();
  const { currentSysop } = useGetCurrentSysop();

  const isSuperAdmin = currentSysop?.isSuperAdmin || false;

  const form = useForm<OrderFormData>({
    mode: 'onChange',
    resolver: zodResolver(createOrderFormSchema(isSuperAdmin)),
    defaultValues: {
      items: [],
      serviceType: 'retail',
      locationId: !isSuperAdmin ? currentSysop?.locationId || '' : undefined,
      customer: {
        name: '',
        phone: '',
        email: '',
        image: '',
      },
      address: '',
      structuredAddress: defaultStructuredAddress,
      guarantor: {
        name: '',
        phone: '',
        email: '',
        nationalId: '',
        image: '',
        structuredAddress: defaultGuarantorStructuredAddress,
      },
      note: '',
    },
  });

  const onSubmit = async (data: OrderFormData) => {
    try {
      const transformedData: CreateOrder = {
        ...data,
        address: data.structuredAddress
          ? formatStructuredAddress(data.structuredAddress, {
              countries,
              cities,
              districts,
            })
          : data.address || '',
        guarantor: data.guarantor
          ? {
              ...data.guarantor,
              address: data.guarantor.structuredAddress
                ? formatStructuredAddress(data.guarantor.structuredAddress, {
                    countries,
                    cities,
                    districts,
                  })
                : '',
            }
          : undefined,
      };

      if (transformedData.guarantor) {
        const {
          structuredAddress: guarantorStructuredAddress,
          ...guarantorApiData
        } = transformedData.guarantor;
        transformedData.guarantor = guarantorApiData;
      }

      const result = await createOrder(transformedData);

      // Show success message with PDF option
      toast.success('Order placed successfully!', {
        action: {
          label: 'Print Receipt',
          onClick: () => printOrder(result),
        },
      });

      onClose(result);
    } catch (_) {}
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    const currentState = { ...tabState };
    if (tab === Tab.DETAILS) {
      currentState[Tab.DETAILS] = 'in-progress';
    }
    if (tab === Tab.PRODUCTS) {
      currentState[Tab.DETAILS] = 'completed';
      currentState[Tab.PRODUCTS] = 'in-progress';
    }
    if (tab === Tab.REVIEW) {
      currentState[Tab.DETAILS] = 'completed';
      currentState[Tab.PRODUCTS] = 'completed';
      currentState[Tab.REVIEW] = 'completed';
    }

    setTabState({ ...currentState });
  }, [tab]);

  return (
    <FocusModal open={open} onOpenChange={() => onClose()}>
      <FocusModal.Portal>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FocusModal.Content className="overflow-visible">
              <ProgressTabs
                value={tab}
                onValueChange={async (val) => {
                  let valid: boolean;
                  if (tab === Tab.DETAILS) {
                    // const fieldsToValidate: (keyof OrderFormData)[] = ;
                    valid = await form.trigger([
                      'customer',
                      'serviceType',
                      'guarantor',
                      'address',
                      'structuredAddress',
                      'locationId',
                    ]);
                  } else if (tab === Tab.PRODUCTS) {
                    valid = await form.trigger('items');
                  } else {
                    valid = await form.trigger();
                  }

                  if (valid) {
                    setTab(val as Tab);
                    setTabState({
                      ...tabState,
                      [val as Tab]: 'in-progress',
                    });
                  }
                }}
                className="flex h-full flex-col overflow-hidden"
              >
                <FocusModal.Header>
                  <VisuallyHidden>
                    <FocusModal.Title>New Order</FocusModal.Title>
                    <FocusModal.Description />
                  </VisuallyHidden>
                  <div className="-my-2 w-full border-border border-l">
                    <ProgressTabs.List className="flex w-full items-center justify-start">
                      <ProgressTabs.Trigger
                        value={Tab.DETAILS}
                        status={tabState[Tab.DETAILS]}
                        className="max-w-[200px] truncate"
                      >
                        Details
                      </ProgressTabs.Trigger>
                      <ProgressTabs.Trigger
                        value={Tab.PRODUCTS}
                        status={tabState[Tab.PRODUCTS]}
                        className="max-w-[200px] truncate"
                      >
                        Products
                      </ProgressTabs.Trigger>
                      <ProgressTabs.Trigger
                        value={Tab.REVIEW}
                        status={tabState[Tab.REVIEW]}
                        className="max-w-[200px] truncate"
                      >
                        Review
                      </ProgressTabs.Trigger>
                    </ProgressTabs.List>
                  </div>
                </FocusModal.Header>
                <FocusModal.Body className="size-full overflow-hidden">
                  <ProgressTabs.Content
                    value={Tab.DETAILS}
                    className="size-full overflow-y-auto"
                  >
                    <OrderCreateDetailsForm form={form} />
                  </ProgressTabs.Content>

                  <ProgressTabs.Content
                    value={Tab.PRODUCTS}
                    className="size-full overflow-y-auto"
                  >
                    <OrderCreateProductsForm form={form} />
                  </ProgressTabs.Content>

                  <ProgressTabs.Content
                    value={Tab.REVIEW}
                    className="size-full overflow-y-auto"
                  >
                    <OrderCreateReviewForm form={form} />
                  </ProgressTabs.Content>
                </FocusModal.Body>
                <FocusModal.Footer>
                  <div className="flex w-full justify-between">
                    <Button
                      type="button"
                      onClick={() => onClose()}
                      variant="outline"
                    >
                      Cancel
                    </Button>

                    <div className="flex space-x-2">
                      {tab !== Tab.DETAILS && (
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => {
                            const tabOrder = [
                              Tab.DETAILS,
                              Tab.PRODUCTS,
                              Tab.REVIEW,
                            ];
                            const currentIndex = tabOrder.indexOf(tab);
                            if (currentIndex > 0) {
                              setTab(tabOrder[currentIndex - 1]!);
                            }
                          }}
                        >
                          Back
                        </Button>
                      )}

                      <PrimaryButton
                        tab={tab}
                        loading={isCreating}
                        onNext={async () => {
                          const valid = await form.trigger([
                            'customer',
                            'structuredAddress',
                            'locationId',
                          ]);
                          if (!valid) return;
                          if (tab === 'customer') {
                            setTab(Tab.PRODUCTS);
                            setTabState({
                              ...tabState,
                              [Tab.PRODUCTS]: 'in-progress',
                            });
                          }
                          if (tab === 'products') {
                            setTab(Tab.REVIEW);
                            setTabState({
                              ...tabState,
                              [Tab.REVIEW]: 'in-progress',
                            });
                          }
                        }}
                      />
                    </div>
                  </div>
                </FocusModal.Footer>
              </ProgressTabs>
            </FocusModal.Content>
          </form>
        </Form>
      </FocusModal.Portal>
    </FocusModal>
  );
};

const PrimaryButton = ({
  tab,
  loading,
  onNext,
}: { tab: Tab; loading: boolean; onNext: () => void }) => {
  if (tab === Tab.REVIEW) {
    return (
      <Button key="submit-button" loading={loading} type="submit">
        Place Order
      </Button>
    );
  }

  return (
    <Button type="button" onClick={onNext}>
      Continue
    </Button>
  );
};
