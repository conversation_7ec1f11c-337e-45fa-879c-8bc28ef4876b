'use client';

import { Pagination } from '@/components/paginations';
import { useOrderPageContext } from '@/contexts/order-page-context';
import type { ServiceType } from '@muraadso/models/orders';
import { OrderActions } from './order-actions';
import { OrderFilters } from './order-filters';
import { OrderSearch } from './order-search';
import { OrderSort } from './order-sort';
import { OrderStats } from './order-stats';
import { OrderTable } from './order-table';
import { useOrders } from './use-orders';

export const OrdersPage = ({ serviceType }: { serviceType?: ServiceType[] }) => {
  const { isPending, orders, pagination } = useOrders({ serviceType });
  const pageContext = useOrderPageContext();

  return (
    <div className="flex w-full flex-col px-12 py-2">
      <OrderStats serviceType={serviceType} />

      <div className="mb-6 space-y-4">
        {/* Search and Actions Row */}
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex flex-1 items-center gap-4">
            <OrderSearch />
            <OrderFilters />
            <OrderSort />
          </div>
          <div className="flex items-center gap-2">
            <OrderActions />
          </div>
        </div>
      </div>

      {isPending && (
        <div className="flex items-center justify-center py-8">
          <div className="text-muted-foreground">Loading orders...</div>
        </div>
      )}

      {orders.length > 0 && (
        <>
          <OrderTable
            orders={orders}
            showQchStatus={pageContext.showQchStatus}
          />

          {pagination && (
            <Pagination
              totalPages={pagination.totalPages}
              hasNextPage={pagination.hasNextPage}
              hasPreviousPage={pagination.hasPreviousPage}
            />
          )}
        </>
      )}
    </div>
  );
};
