'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useAuth } from '@muraadso/api/hooks';
import { LockIcon, MailIcon } from '@muraadso/icons';
import { type SignIn, signInSchema } from '@muraadso/models/auth';
import { Button, Checkbox, Form, Input, Label, Separator } from '@muraadso/ui';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React from 'react';
import { useForm } from 'react-hook-form';

export const SignInForm = () => {
  const { signIn } = useAuth();
  const { push } = useRouter();

  const form = useForm<SignIn>({
    mode: 'onTouched',
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = async (payload: SignIn) => {
    try {
      await signIn.mutateAsync(payload);
      push('/');
    } catch (e) {
      form.setFocus('email');
      form.setError('email', {
        type: 'manual',
        // @ts-expect-error skip
        message: e?.message,
      });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <Form.Field
          name="email"
          control={form.control}
          render={({ field }) => (
            <Form.Item>
              <Form.Label htmlFor="email" className="mb-2.5 block">
                Email <span className="text-destructive">*</span>
              </Form.Label>
              <Form.Control>
                <Input
                  id="email"
                  type="email"
                  autoComplete="email"
                  leading={<MailIcon size={16} />}
                  readOnly={form.formState.isSubmitting}
                  placeholder="<EMAIL>"
                  className="mt-1"
                  {...field}
                />
              </Form.Control>
              <Form.Message className="font-normal" />
            </Form.Item>
          )}
        />

        <Form.Field
          name="password"
          control={form.control}
          render={({ field }) => (
            <Form.Item className="mt-4">
              <Form.Label htmlFor="password" className="mb-2.5 block">
                Password <span className="text-destructive">*</span>
              </Form.Label>

              <Form.Control>
                <Input
                  id="password"
                  type="password"
                  autoComplete="current-password"
                  leading={<LockIcon size={16} />}
                  readOnly={form.formState.isSubmitting}
                  placeholder="••••••••••"
                  className="mt-1"
                  {...field}
                />
              </Form.Control>
              <Form.Message className="font-normal" />
            </Form.Item>
          )}
        />

        <div className="mt-3 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Checkbox disabled={form.formState.isSubmitting} id="2" />
            <Label htmlFor="2" className="font-normal text-sm">
              Remember me
            </Label>
          </div>
          <Link href="#">
            <Button
              type="button"
              disabled={form.formState.isSubmitting}
              variant="link"
              className="px-0 underline"
            >
              Forgot password?
            </Button>
          </Link>
        </div>

        <Button
          type="submit"
          className="mt-4 w-full"
          loading={form.formState.isSubmitting}
        >
          Login
        </Button>

        <div className="mt-4 flex items-center gap-2">
          <Separator className="flex-1" />
          <div className="px-4 text-muted-foreground text-sm">Or</div>
          <Separator className="flex-1" />
        </div>

        <Button
          type="button"
          disabled
          variant="secondary"
          className="mt-4 w-full"
        >
          Login with Passkey
        </Button>
      </form>
    </Form>
  );
};
