'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { CheckIcon, CloseIcon, LockIcon, MailIcon } from '@muraadso/icons';
import { Button, Form, Input, OtpInput } from '@muraadso/ui';
import { AnimatePresence, motion } from 'motion/react';
import Link from 'next/link';
import type React from 'react';
import { useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const emailSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
});

const verificationCodeSchema = z.object({
  code: z.string().regex(/^\d{8}$/, {
    message: 'OTP must be an 8-digit number',
  }),
});

const passwordSchema = z
  .object({
    password: z
      .string()
      .min(8, 'At least 8 characters')
      .regex(/[0-9]/, 'At least 1 number')
      .regex(/[a-z]/, 'At least 1 lowercase letter')
      .regex(/[A-Z]/, 'At least 1 uppercase letter'),
    confirm: z.string(),
  })
  .refine((data) => data.password === data.confirm, {
    message: "Passwords don't match",
    path: ['confirm'],
  });

type Step = 'email' | 'code' | 'password' | 'success';

export const ResetPasswordForm = () => {
  const [step, setStep] = useState<Step>('email');
  const [loading, setLoading] = useState(false);

  const handleEmailSubmit = (email: string) => {
    // const { data } = await http.post<EmailSchema>('/reset-password', { email });
    // data.email;
    setStep('code');
  };

  const handleOTPVerification = (otp: string) => {
    setStep('password');
  };

  const handleChangePassword = (password: string) => {
    setStep('success');
  };

  return (
    <div className="relative flex h-full">
      <AnimatePresence mode="wait">
        {step === 'email' && (
          <motion.div
            exit={{ opacity: 0, x: -100 }}
            transition={{ duration: 0.25 }}
            className="absolute w-full"
          >
            <EmailForm loading={loading} onNext={handleEmailSubmit} />
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence mode="wait">
        {step === 'code' && (
          <motion.div
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -100 }}
            transition={{ duration: 0.25, delay: 0.15 }}
            className="absolute w-full"
          >
            <VerificationCodeForm
              loading={loading}
              onNext={handleOTPVerification}
            />
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence mode="wait">
        {step === 'password' && (
          <motion.div
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -100 }}
            transition={{ duration: 0.25, delay: 0.15 }}
            className="absolute w-full"
          >
            <PasswordForm loading={loading} onNext={handleChangePassword} />
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence mode="wait">
        {step === 'success' && (
          <motion.div
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -100 }}
            transition={{ duration: 0.25, delay: 0.15 }}
            className="absolute w-full"
          >
            {/*<div className="flex"></div>*/}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

const EmailForm: React.FC<{
  loading: boolean;
  onNext: (email: string) => void;
}> = ({ loading, onNext }) => {
  const form = useForm<z.infer<typeof emailSchema>>({
    mode: 'onTouched',
    resolver: zodResolver(emailSchema),
    defaultValues: {
      email: '<EMAIL>',
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(({ email }) => onNext(email))}>
        <Form.Field
          name="email"
          control={form.control}
          render={({ field }) => (
            <Form.Item>
              <Form.Label htmlFor="email" className="mb-2.5 block">
                Email <span className="text-destructive">*</span>
              </Form.Label>
              <Form.Control>
                <Input
                  id="email"
                  type="email"
                  disabled={loading}
                  autoComplete="email"
                  leading={<MailIcon size={16} />}
                  readOnly={form.formState.isSubmitting}
                  placeholder="<EMAIL>"
                  className="mt-1"
                  {...field}
                />
              </Form.Control>
              <Form.Message className="font-normal" />
              <Form.Description className="flex items-center gap-2 font-normal">
                Please enter the email with which you&apos;ve registered.
              </Form.Description>
            </Form.Item>
          )}
        />

        <Button
          type="submit"
          className="mt-5 w-full"
          disabled={loading}
          loading={loading}
        >
          Next
        </Button>

        <div className="mt-4 flex flex-col text-center text-muted-foreground text-sm">
          <span>Don’t have access anymore?</span>

          <Link href="/" className="text-center">
            <Button variant="link">Contact Support</Button>
          </Link>
        </div>
      </form>
    </Form>
  );
};

const VerificationCodeForm: React.FC<{
  loading: boolean;
  onNext: (email: string) => void;
}> = ({ loading, onNext }) => {
  const form = useForm<z.infer<typeof verificationCodeSchema>>({
    mode: 'onTouched',
    resolver: zodResolver(verificationCodeSchema),
    defaultValues: {
      code: '12345678',
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(({ code }) => onNext(code))}>
        <Form.Field
          name="code"
          control={form.control}
          render={({ field }) => (
            <Form.Item>
              <Form.Label className="mb-2.5 block">
                Verification Code <span className="text-destructive">*</span>
              </Form.Label>
              <Form.Control>
                <OtpInput maxLength={8} {...field}>
                  <OtpInput.Group>
                    <OtpInput.Slot index={0} />
                    <OtpInput.Slot index={1} />
                    <OtpInput.Slot index={2} />
                    <OtpInput.Slot index={3} />
                  </OtpInput.Group>
                  <OtpInput.Separator />
                  <OtpInput.Group>
                    <OtpInput.Slot index={4} />
                    <OtpInput.Slot index={5} />
                    <OtpInput.Slot index={6} />
                    <OtpInput.Slot index={7} />
                  </OtpInput.Group>
                </OtpInput>
              </Form.Control>
              <Form.Message className="font-normal" />
              <Form.Description className="flex items-center gap-2 font-normal">
                <span>
                  We&apos;ve sent you an 8-digit code. Please check your email
                  and enter it here. <b>Code expires in 10 minutes.</b>
                </span>
              </Form.Description>
            </Form.Item>
          )}
        />

        <Button
          type="submit"
          className="mt-5 w-full"
          disabled={loading}
          loading={loading}
        >
          Verify Code
        </Button>

        <Button variant="link" className="mt-2 w-full">
          Go back
        </Button>
      </form>
    </Form>
  );
};

const PasswordForm: React.FC<{
  loading: boolean;
  onNext: (password: string) => void;
}> = ({ loading, onNext }) => {
  const form = useForm<z.infer<typeof passwordSchema>>({
    mode: 'onTouched',
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      password: 'hjhjhjhjhA1',
      confirm: 'hjhjhjhjhA1',
    },
  });

  const checkStrength = (pass: string) => {
    const requirements = [
      { regex: /.{8,}/, text: 'At least 8 characters' },
      { regex: /[0-9]/, text: 'At least 1 number' },
      { regex: /[a-z]/, text: 'At least 1 lowercase letter' },
      { regex: /[A-Z]/, text: 'At least 1 uppercase letter' },
    ];

    return requirements.map((req) => ({
      met: req.regex.test(pass),
      text: req.text,
    }));
  };

  const strength = checkStrength(form.watch('password'));

  const strengthScore = useMemo(() => {
    return strength.filter((req) => req.met).length;
  }, [strength]);

  const getStrengthColor = (score: number) => {
    if (score === 0) return 'bg-border';
    if (score <= 1) return 'bg-red-500';
    if (score <= 2) return 'bg-orange-500';
    if (score === 3) return 'bg-amber-500';
    return 'bg-emerald-500';
  };

  const getStrengthText = (score: number) => {
    if (score === 0) return 'Choose password';
    if (score <= 2) return 'Weak password';
    if (score === 3) return 'Medium password';
    return 'Strong password';
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(({ password }) => onNext(password))}
        className="grid w-full"
      >
        <Form.Field
          name="password"
          control={form.control}
          render={({ field }) => (
            <Form.Item>
              <Form.Label htmlFor="passwrod" className="mb-2.5 block">
                New Password <span className="text-destructive">*</span>
              </Form.Label>
              <Form.Control>
                <Input
                  id="passwrod"
                  placeholder="••••••••••"
                  className="w-full"
                  type="password"
                  leading={<LockIcon size={16} />}
                  disabled={loading}
                  {...field}
                />
              </Form.Control>
              <Form.Message className="font-normal" />
            </Form.Item>
          )}
        />

        <Form.Field
          name="confirm"
          control={form.control}
          render={({ field }) => (
            <Form.Item className="mt-4">
              <Form.Label htmlFor="confirm" className="mb-2.5 block">
                Confirm Password <span className="text-destructive">*</span>
              </Form.Label>
              <Form.Control>
                <Input
                  id="confirm"
                  placeholder="••••••••••"
                  className="w-full"
                  type="password"
                  leading={<LockIcon size={16} />}
                  disabled={loading}
                  {...field}
                />
              </Form.Control>
              <Form.Message className="font-normal" />
            </Form.Item>
          )}
        />

        <div>
          <div
            className="my-4 h-1 w-full overflow-hidden rounded-full bg-border"
            aria-valuenow={strengthScore}
            aria-valuemin={0}
            aria-valuemax={4}
            aria-label="Password strength"
          >
            <div
              className={`h-full ${getStrengthColor(strengthScore)} transition-all duration-500 ease-out`}
              style={{ width: `${(strengthScore / 4) * 100}%` }}
            />
          </div>

          <p className="mb-2 font-medium text-foreground text-sm">
            {getStrengthText(strengthScore)}. Must contain:
          </p>

          <ul className="space-y-1.5" aria-label="Password requirements">
            {strength.map((req, index) => (
              <li key={`key-${index * 2}`} className="flex items-center gap-2">
                {req.met ? (
                  <CheckIcon
                    size={16}
                    className="text-emerald-500"
                    aria-hidden="true"
                  />
                ) : (
                  <CloseIcon
                    size={16}
                    className="text-muted-foreground/80"
                    aria-hidden="true"
                  />
                )}
                <span
                  className={`text-xs ${req.met ? 'text-emerald-600' : 'text-muted-foreground'}`}
                >
                  {req.text}
                  <span className="sr-only">
                    {req.met ? ' - Requirement met' : ' - Requirement not met'}
                  </span>
                </span>
              </li>
            ))}
          </ul>
        </div>

        <Button
          type="submit"
          className="mt-8 w-full"
          disabled={loading}
          loading={loading}
        >
          Change Password
        </Button>

        {/*<div className="flex flex-col text-center text-muted-foreground text-sm">*/}
        {/*  <span>Don’t have access anymore?</span>*/}

        {/*  <Link href="/" className="text-center">*/}
        {/*    <Button variant="link">Contact Support</Button>*/}
        {/*  </Link>*/}
        {/*</div>*/}
      </form>
    </Form>
  );
};
