import { CloseIcon } from '@muraadso/icons';
import { Badge } from '@muraadso/ui';
import { cn } from '@muraadso/ui/utils';
import { AnimatePresence, motion } from 'motion/react';
import {
  type FocusEvent,
  type KeyboardEvent,
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';

type ChipInputProps = {
  value: string[];
  onChange: (value: string[]) => void;
  onBlur?: () => void;
  name: string;
  disabled?: boolean;
  allowDuplicates?: boolean;
  showRemove?: boolean;
  variant?: 'base' | 'contrast';
  placeholder?: string;
  className?: string;
};

export const ChipInput = ({
  value,
  onChange,
  onBlur,
  disabled,
  name,
  showRemove = true,
  allowDuplicates = false,
  placeholder,
  className,
}: ChipInputProps) => {
  const innerRef = useRef<HTMLInputElement>(null);

  const isControlledRef = useRef(typeof value !== 'undefined');
  const isControlled = isControlledRef.current;

  const [uncontrolledValue, setUncontrolledValue] = useState<string[]>([]);

  const [duplicateIndex, setDuplicateIndex] = useState<number | null>(null);

  const chips = isControlled ? (value as string[]) : uncontrolledValue;

  const handleAddChip = (chip: string) => {
    const cleanValue = chip.trim();

    if (!cleanValue) {
      return;
    }

    if (!allowDuplicates && chips.includes(cleanValue)) {
      setDuplicateIndex(chips.indexOf(cleanValue));

      setTimeout(() => {
        setDuplicateIndex(null);
      }, 300);

      return;
    }

    onChange?.([...chips, cleanValue]);

    if (!isControlled) {
      setUncontrolledValue([...chips, cleanValue]);
    }
  };

  const handleRemoveChip = (chip: string) => {
    onChange?.(chips.filter((v) => v !== chip));

    if (!isControlled) {
      setUncontrolledValue(chips.filter((v) => v !== chip));
    }
  };

  const handleBlur = (e: FocusEvent<HTMLInputElement>) => {
    onBlur?.();

    if (e.target.value) {
      handleAddChip(e.target.value);
      e.target.value = '';
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();

      if (!innerRef.current?.value) {
        return;
      }

      handleAddChip(innerRef.current?.value ?? '');
      innerRef.current.value = '';
      innerRef.current?.focus();
    }

    if (e.key === 'Backspace' && innerRef.current?.value === '') {
      const chip = chips[chips.length - 1];
      if (chip) handleRemoveChip(chip);
    }
  };

  const shake = {
    x: [0, -2, 2, -2, 2, 0],
    transition: { duration: 0.3 },
  };

  return (
    <div
      className={cn(
        'flex min-h-9 w-full flex-wrap items-center gap-y-1 rounded-lg border border-input bg-transparent px-3 py-1 text-foreground text-sm shadow-black/5 shadow-sm transition-shadow placeholder:text-muted-foreground',
        'focus-within:border-ring focus-within:ring-[3px] focus-within:ring-ring/20',
        'disabled:cursor-not-allowed disabled:opacity-50',
        'aria-[invalid=true]:!text-destructive aria-[invalid=true]:border-destructive/80 aria-[invalid=true]:focus-visible:border-destructive/80 aria-[invalid=true]:focus-visible:ring-destructive/20',
        className,
      )}
      onClick={() => innerRef.current?.focus()}
    >
      {chips.map((v, index) => {
        return (
          <AnimatePresence key={`${v}-${index}`}>
            <Badge
              variant="secondary"
              className={cn('-ms-1 me-3 h-6 gap-x-0.5 px-1.5 text-xs', {
                'pe-1': showRemove,
                'shadow-borders-focus': index === duplicateIndex,
              })}
              asChild
            >
              <motion.div
                animate={index === duplicateIndex ? shake : undefined}
              >
                {v}
                {showRemove && (
                  <button
                    type="button"
                    onClick={() => handleRemoveChip(v)}
                    className="outline-none"
                  >
                    <CloseIcon className="size-3.5 text-muted-foreground" />
                  </button>
                )}
              </motion.div>
            </Badge>
          </AnimatePresence>
        );
      })}
      <input
        className="flex-1 bg-transparent outline-none"
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        disabled={disabled}
        name={name}
        ref={innerRef}
        placeholder={chips.length === 0 ? placeholder : undefined}
        autoComplete="off"
      />
    </div>
  );
};

ChipInput.displayName = 'ChipInput';
