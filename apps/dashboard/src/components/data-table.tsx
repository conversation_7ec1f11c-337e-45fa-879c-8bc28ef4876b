'use client';

import {
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { useMemo, useState } from 'react';

import { Button, Input, Table } from '@muraadso/ui';
import { cn } from '@muraadso/ui/utils';

interface DataTableProps<TData, TValue = unknown> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  enableSorting?: boolean;
  enableFiltering?: boolean;
  enablePagination?: boolean;
  enableColumnVisibility?: boolean;
  enableRowSelection?: boolean;
  pageSize?: number;
  pageSizeOptions?: number[];
  globalFilter?: string;
  onGlobalFilterChange?: (value: string) => void;
  loading?: boolean;
  emptyMessage?: string;
  loadingMessage?: string;
  onRowSelectionChange?: (selectedRows: TData[]) => void;
  className?: string;
  onRowClick?: (row: TData) => void;
}

export const DataTable = <TData, TValue = unknown>({
  columns,
  data,
  enableSorting = false,
  enableFiltering = false,
  enablePagination = false,
  enableColumnVisibility = false,
  enableRowSelection = false,
  pageSize = 10,
  pageSizeOptions = [10, 20, 30, 40, 50],
  globalFilter,
  onGlobalFilterChange,
  loading = false,
  emptyMessage = 'No results found.',
  loadingMessage = 'Loading...',
  className = '',
  onRowClick,
}: DataTableProps<TData, TValue>) => {
  // State management with proper typing
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});
  const [internalGlobalFilter, setInternalGlobalFilter] = useState<string>('');

  const currentGlobalFilter = globalFilter ?? internalGlobalFilter;
  const handleGlobalFilterChange =
    onGlobalFilterChange ?? setInternalGlobalFilter;

  // Memoized table configuration for performance
  const tableConfig = useMemo(
    () => ({
      data,
      columns,
      onSortingChange: enableSorting ? setSorting : undefined,
      onColumnFiltersChange: enableFiltering ? setColumnFilters : undefined,
      onColumnVisibilityChange: enableColumnVisibility
        ? setColumnVisibility
        : undefined,
      onRowSelectionChange: enableRowSelection ? setRowSelection : undefined,
      onGlobalFilterChange: enableFiltering
        ? handleGlobalFilterChange
        : undefined,
      getCoreRowModel: getCoreRowModel(),
      ...(enableSorting && { getSortedRowModel: getSortedRowModel() }),
      ...(enableFiltering && { getFilteredRowModel: getFilteredRowModel() }),
      ...(enablePagination && {
        getPaginationRowModel: getPaginationRowModel(),
      }),
      state: {
        ...(enableSorting && { sorting }),
        ...(enableFiltering && {
          columnFilters,
          globalFilter: currentGlobalFilter,
        }),
        ...(enableColumnVisibility && { columnVisibility }),
        ...(enableRowSelection && { rowSelection }),
      },
      ...(enablePagination && {
        initialState: {
          pagination: {
            pageSize,
          },
        },
      }),
    }),
    [
      data,
      columns,
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      currentGlobalFilter,
      enableSorting,
      enableFiltering,
      enablePagination,
      enableColumnVisibility,
      enableRowSelection,
      pageSize,
      handleGlobalFilterChange,
    ],
  );

  const table = useReactTable(tableConfig);

  // Handle row selection changes with type safety
  // const selectedRows = useMemo(() => {
  //   if (!enableRowSelection || !onRowSelectionChange) return;
  //
  //   const selectedRowData = table.getFilteredSelectedRowModel().rows.map(row => row.original);
  //   onRowSelectionChange(selectedRowData);
  // }, [enableRowSelection, onRowSelectionChange, table]);

  // Render loading state
  if (loading) {
    return (
      <div className={`flex h-24 items-center justify-center ${className}`}>
        <span>{loadingMessage}</span>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {enableFiltering && (
        <div className="flex items-center">
          <Input
            placeholder="Search all columns..."
            value={currentGlobalFilter}
            onChange={(event) => handleGlobalFilterChange(event.target.value)}
            className="max-w-sm"
          />
        </div>
      )}

      <Table>
        <Table.Header>
          {table.getHeaderGroups().map((headerGroup) => (
            <Table.Row key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <Table.Head
                  key={header.id}
                  className={
                    enableSorting && header.column.getCanSort()
                      ? 'cursor-pointer select-none'
                      : ''
                  }
                  onClick={
                    enableSorting
                      ? header.column.getToggleSortingHandler()
                      : undefined
                  }
                >
                  {header.isPlaceholder ? null : (
                    <div className="flex items-center space-x-1">
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                      {enableSorting && header.column.getCanSort() && (
                        <span className="ml-1">
                          {header.column.getIsSorted() === 'asc' && '↑'}
                          {header.column.getIsSorted() === 'desc' && '↓'}
                          {!header.column.getIsSorted() && '↕'}
                        </span>
                      )}
                    </div>
                  )}
                </Table.Head>
              ))}
            </Table.Row>
          ))}
        </Table.Header>
        <Table.Body>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <Table.Row
                key={row.id}
                data-state={
                  enableRowSelection && row.getIsSelected()
                    ? 'selected'
                    : undefined
                }
                className={cn(
                  'cursor-pointer',
                  enableRowSelection && row.getIsSelected() && 'bg-muted/50',
                )}
                onClick={() => onRowClick?.(row.original)}
              >
                {row.getVisibleCells().map((cell) => (
                  <Table.Cell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </Table.Cell>
                ))}
              </Table.Row>
            ))
          ) : (
            <Table.Row>
              <Table.Cell className="h-24 text-center text-muted-foreground">
                {emptyMessage}
              </Table.Cell>
            </Table.Row>
          )}
        </Table.Body>
      </Table>

      {/* Pagination */}
      {enablePagination && (
        <div className="flex items-center justify-between space-x-2 py-4">
          <div className="flex items-center space-x-2">
            <p className="font-medium text-sm">Rows per page</p>
            <select
              value={table.getState().pagination.pageSize}
              onChange={(e) => {
                table.setPageSize(Number(e.target.value));
              }}
              className="h-8 w-[70px] rounded border border-input bg-background px-2 text-sm"
            >
              {pageSizeOptions.map((pageSize) => (
                <option key={pageSize} value={pageSize}>
                  {pageSize}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-center space-x-6 lg:space-x-8">
            <div className="flex w-[100px] items-center justify-center font-medium text-sm">
              Page {table.getState().pagination.pageIndex + 1} of{' '}
              {table.getPageCount()}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => table.setPageIndex(0)}
                disabled={!table.getCanPreviousPage()}
              >
                {'<<'}
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                {'<'}
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                {'>'}
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                disabled={!table.getCanNextPage()}
              >
                {'>>'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Row Selection Info */}
      {enableRowSelection && (
        <div className="flex-1 text-muted-foreground text-sm">
          {table.getFilteredSelectedRowModel().rows.length} of{' '}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
      )}
    </div>
  );
};
