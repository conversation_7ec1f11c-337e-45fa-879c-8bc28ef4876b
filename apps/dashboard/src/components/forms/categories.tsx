import { type Image, ImagePicker } from '@/components/image-picker';
import { slugify } from '@/lib/slug';
import { getImageUrl } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  useCreateCategory,
  useDeleteCategoryImage,
  useUpdateCategory,
  useUploadCategoryImage,
} from '@muraadso/api/hooks';
import {
  type Category,
  type CreateCategory,
  type UpdateCategory,
  createCategorySchema,
  updateCategorySchema,
} from '@muraadso/models/categories';
import { Button, Dialog, Form, Input, Label, Textarea } from '@muraadso/ui';
import React, { memo, useState } from 'react';
import { useForm } from 'react-hook-form';

export const CategoriesForm = memo(
  ({
    open,
    onClose,
    payload,
  }: {
    open: boolean;
    onClose: (category?: Category) => void;
    payload?: Category;
  }) => {
    const { mutateAsync: create, isPending: isCreating } = useCreateCategory();
    const { mutateAsync: update, isPending: isUpdating } = useUpdateCategory();
    const { mutateAsync: uploadImage, isPending: isUploading } =
      useUploadCategoryImage();

    const { mutateAsync: deleteImage, isPending: isDeletingImage } =
      useDeleteCategoryImage();

    const isLoading =
      isCreating || isUpdating || isUploading || isDeletingImage;

    const [images, setImages] = useState<Image[]>(() => {
      if (payload?.image) {
        return [
          {
            id: crypto.randomUUID(),
            url: getImageUrl(payload.image),
            isExisting: true,
          },
        ];
      }
      return [];
    });

    const form = useForm<CreateCategory | UpdateCategory>({
      resolver: zodResolver(
        payload ? updateCategorySchema : createCategorySchema,
      ),
      defaultValues: {
        id: payload?.id ?? undefined,
        name: payload?.name ?? '',
        slug: payload?.slug ?? '',
        description: payload?.description ?? '',
        rank: payload?.rank ?? 1,
      },
    });

    const handleDeleteImage = async (image: Image) => {
      if (!image?.id) return false;
      const confirmed = window.confirm(
        'Are you sure you want to delete this category image?',
      );
      if (!confirmed) return false;

      try {
        await deleteImage(payload!.id);
        return true;
      } catch (error) {
        console.error('Failed to delete category image:', error);
        return false;
      }
    };

    const onSubmit = async (data: CreateCategory | UpdateCategory) => {
      const isUpdate = !!payload;

      try {
        const result = isUpdate
          ? await update(data as UpdateCategory)
          : await create(data as CreateCategory);

        const newImage = images.find((img) => img.file && !img.isExisting);
        if (newImage?.file) {
          await uploadImage({ id: result.id, image: newImage?.file });
        }

        form.reset();
        onClose(result);
      } catch (_) {}
    };

    return (
      <Dialog open={open} onOpenChange={() => onClose()}>
        <Dialog.Content className="min-w-md">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <Dialog.Body title={`${payload ? 'Edit' : 'New'} Category`}>
                <div className="grid w-full grid-cols-2 items-start gap-x-6">
                  <Form.Field
                    name="name"
                    control={form.control}
                    render={({ field: { onChange, ...rest } }) => (
                      <Form.Item>
                        <Form.Label htmlFor="name" className="leading-none">
                          Name <span className="text-destructive">*</span>
                        </Form.Label>
                        <Form.Control>
                          <Input
                            id="name"
                            type="text"
                            placeholder="Mobile Phones"
                            onChange={(e) => {
                              onChange(e.target.value);
                              form.setValue('slug', slugify(e.target.value));
                              form.trigger('slug');
                            }}
                            disabled={isLoading}
                            {...rest}
                          />
                        </Form.Control>
                        <Form.Message className="font-normal" />
                      </Form.Item>
                    )}
                  />

                  <Form.Field
                    name="slug"
                    control={form.control}
                    render={({ field }) => (
                      <Form.Item>
                        <Form.Label
                          tooltip={
                            <span>
                              A slug is a human-readable ID that must be <br />
                              unique. It&apos;s often used in URLs.
                            </span>
                          }
                          htmlFor="slug"
                          className="leading-none"
                        >
                          <span>Slug </span>
                          <span className="text-destructive">*</span>
                        </Form.Label>
                        <Form.Control>
                          <Input
                            id="slug"
                            type="text"
                            placeholder="/mobile-phones"
                            disabled={isLoading}
                            {...field}
                          />
                        </Form.Control>
                        <Form.Message className="font-normal" />
                      </Form.Item>
                    )}
                  />
                </div>
                <Form.Field
                  name="description"
                  control={form.control}
                  render={({ field }) => (
                    <Form.Item className="mt-5">
                      <Form.Label
                        optional
                        htmlFor="description"
                        className="leading-none"
                      >
                        Description
                      </Form.Label>
                      <Form.Control>
                        <Textarea
                          id="description"
                          rows={3}
                          className="resize-none"
                          disabled={isLoading}
                          {...field}
                          value={field.value ?? ''}
                        />
                      </Form.Control>
                      <Form.Message className="font-normal" />
                    </Form.Item>
                  )}
                />

                <Form.Field
                  name="rank"
                  control={form.control}
                  render={({ field }) => (
                    <Form.Item className="mt-5">
                      <Form.Label htmlFor="rank" className="leading-none">
                        <span>Display order </span>
                        <span className="text-destructive">*</span>
                      </Form.Label>
                      <Form.Control>
                        <Input
                          id="rank"
                          type="number"
                          placeholder="Display Order"
                          disabled={isLoading}
                          {...field}
                          value={field.value ?? ''}
                        />
                      </Form.Control>
                      <Form.Message className="font-normal" />
                      <Form.Description>
                        Sorting: Lower numbers have higher priority
                      </Form.Description>
                    </Form.Item>
                  )}
                />

                <div className="mt-5 grid gap-2.5">
                  <Label className="leading-none">Image</Label>
                  <ImagePicker
                    value={images}
                    onChange={setImages}
                    onDeleteExisting={handleDeleteImage}
                  />
                </div>
              </Dialog.Body>
              <Dialog.Footer>
                <Button
                  type="button"
                  disabled={isLoading}
                  onClick={() => onClose()}
                  variant="outline"
                >
                  Cancel
                </Button>

                <Button type="submit" loading={isLoading}>
                  Save changes
                </Button>
              </Dialog.Footer>
            </form>
          </Form>
        </Dialog.Content>
      </Dialog>
    );
  },
);
CategoriesForm.displayName = 'CategoriesForm';
