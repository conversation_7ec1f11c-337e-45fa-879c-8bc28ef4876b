import { CitySelector } from '@/components/selectors/city-selector';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCreateDistrict, useUpdateDistrict } from '@muraadso/api/hooks';
import {
  type CreateDistrict,
  type District,
  type UpdateDistrict,
  createDistrictSchema,
  updateDistrictSchema,
} from '@muraadso/models/districts';
import { Button, Dialog, Form, Input } from '@muraadso/ui';
import type React from 'react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';

export const DistrictsForm: React.FC<{
  open: boolean;
  onClose: (district?: District) => void;
  payload?: District;
}> = ({ open, onClose, payload }) => {
  const { mutateAsync: create, isPending: isCreating } = useCreateDistrict();
  const { mutateAsync: update, isPending: isUpdating } = useUpdateDistrict();

  const isLoading = isCreating || isUpdating;

  const form = useForm<CreateDistrict | UpdateDistrict>({
    resolver: zodResolver(
      payload ? updateDistrictSchema : createDistrictSchema,
    ),
    defaultValues: {
      id: payload?.id ?? undefined,
      name: payload?.name ?? '',
      cityId: payload?.cityId ?? '',
    },
  });

  const onSubmit = async (data: CreateDistrict | UpdateDistrict) => {
    try {
      const result = payload
        ? await update(data as UpdateDistrict)
        : await create(data as CreateDistrict);
      onClose(result);
    } catch (error) {
      console.error('Error saving district:', error);
    }
  };

  useEffect(() => {
    if (payload) {
      form.reset({
        id: payload.id,
        name: payload.name,
        cityId: payload.cityId,
      });
    } else {
      form.reset({
        name: '',
        cityId: '',
      });
    }
  }, [payload, form]);

  return (
    <Dialog open={open} onOpenChange={() => onClose()}>
      <Dialog.Content className="min-w-md">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <Dialog.Body title={`${payload ? 'Edit' : 'New'} District`}>
              <Form.Field
                name="name"
                control={form.control}
                render={({ field }) => (
                  <Form.Item>
                    <Form.Label htmlFor="name" className="leading-none">
                      Name <span className="text-destructive">*</span>
                    </Form.Label>
                    <Form.Control>
                      <Input
                        id="name"
                        type="text"
                        placeholder="District Name"
                        disabled={isLoading}
                        {...field}
                      />
                    </Form.Control>
                    <Form.Message className="font-normal" />
                  </Form.Item>
                )}
              />

              <Form.Field
                name="cityId"
                control={form.control}
                render={({ field }) => (
                  <Form.Item className="mt-5">
                    <Form.Label htmlFor="cityId" className="leading-none">
                      City <span className="text-destructive">*</span>
                    </Form.Label>
                    <Form.Control>
                      <CitySelector
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isLoading}
                        placeholder="Select city..."
                      />
                    </Form.Control>
                    <Form.Message className="font-normal" />
                  </Form.Item>
                )}
              />
            </Dialog.Body>

            <Dialog.Footer>
              <Button
                type="button"
                variant="outline"
                onClick={() => onClose()}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Saving...' : payload ? 'Update' : 'Create'}
              </Button>
            </Dialog.Footer>
          </form>
        </Form>
      </Dialog.Content>
    </Dialog>
  );
};
