import { CountrySelector } from '@/components/selectors/country-selector';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCreateCity, useUpdateCity } from '@muraadso/api/hooks';
import {
  type City,
  type CreateCity,
  type UpdateCity,
  createCitySchema,
  updateCitySchema,
} from '@muraadso/models/cities';
import { Button, Dialog, Form, Input } from '@muraadso/ui';
import type React from 'react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';

export const CitiesForm: React.FC<{
  open: boolean;
  onClose: (city?: City) => void;
  payload?: City;
}> = ({ open, onClose, payload }) => {
  const { mutateAsync: create, isPending: isCreating } = useCreateCity();
  const { mutateAsync: update, isPending: isUpdating } = useUpdateCity();

  const isLoading = isCreating || isUpdating;

  const form = useForm<CreateCity | UpdateCity>({
    resolver: zodResolver(payload ? updateCitySchema : createCitySchema),
    defaultValues: {
      id: payload?.id ?? undefined,
      name: payload?.name ?? '',
      countryId: payload?.countryId ?? '',
    },
  });

  const onSubmit = async (data: CreateCity | UpdateCity) => {
    try {
      const result = payload
        ? await update(data as UpdateCity)
        : await create(data as CreateCity);
      onClose(result);
    } catch (error) {
      console.error('Error saving city:', error);
    }
  };

  useEffect(() => {
    if (payload) {
      form.reset({
        id: payload.id,
        name: payload.name,
        countryId: payload.countryId,
      });
    } else {
      form.reset({
        name: '',
        countryId: '',
      });
    }
  }, [payload, form]);

  return (
    <Dialog open={open} onOpenChange={() => onClose()}>
      <Dialog.Content className="min-w-md">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <Dialog.Body title={`${payload ? 'Edit' : 'New'} City`}>
              <Form.Field
                name="name"
                control={form.control}
                render={({ field }) => (
                  <Form.Item>
                    <Form.Label htmlFor="name" className="leading-none">
                      Name <span className="text-destructive">*</span>
                    </Form.Label>
                    <Form.Control>
                      <Input
                        id="name"
                        type="text"
                        placeholder="City Name"
                        disabled={isLoading}
                        {...field}
                      />
                    </Form.Control>
                    <Form.Message className="font-normal" />
                  </Form.Item>
                )}
              />

              <Form.Field
                name="countryId"
                control={form.control}
                render={({ field }) => (
                  <Form.Item className="mt-5">
                    <Form.Label htmlFor="countryId" className="leading-none">
                      Country <span className="text-destructive">*</span>
                    </Form.Label>
                    <Form.Control>
                      <CountrySelector
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isLoading}
                        placeholder="Select country..."
                      />
                    </Form.Control>
                    <Form.Message className="font-normal" />
                  </Form.Item>
                )}
              />
            </Dialog.Body>

            <Dialog.Footer>
              <Button
                type="button"
                variant="outline"
                onClick={() => onClose()}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Saving...' : payload ? 'Update' : 'Create'}
              </Button>
            </Dialog.Footer>
          </form>
        </Form>
      </Dialog.Content>
    </Dialog>
  );
};
