import { CitySelector } from '@/components/selectors/city-selector';
import { CountrySelector } from '@/components/selectors/country-selector';
import { DistrictSelector } from '@/components/selectors/district-selector';
import { Form, Input, Textarea } from '@muraadso/ui';
import React from 'react';
import type { Control, FieldPath, FieldValues } from 'react-hook-form';

interface AddressFormFieldsProps<T extends FieldValues> {
  control: Control<T>;
  fieldPrefix: FieldPath<T>;
  countryValue?: string;
  cityValue?: string;
  onCountryChange?: (countryId: string | undefined) => void;
  onCityChange?: (cityId: string | undefined) => void;
  disabled?: boolean;
}

export const AddressFormFields = <T extends FieldValues>({
  control,
  fieldPrefix,
  countryValue,
  cityValue,
  onCountryChange,
  onCityChange,
  disabled = false,
}: AddressFormFieldsProps<T>) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 gap-5 md:grid-cols-2">
        <Form.Field
          control={control}
          name={`${fieldPrefix}.countryId` as FieldPath<T>}
          render={({ field }) => (
            <Form.Item>
              <div className="flex w-full items-end justify-between">
                <Form.Label>
                  Country <span className="text-destructive">*</span>
                </Form.Label>
                <Form.Message />
              </div>
              <Form.Control>
                <CountrySelector
                  value={field.value}
                  onValueChange={(value) => {
                    field.onChange(value);
                    onCountryChange?.(value);
                  }}
                  disabled={disabled}
                  placeholder="Select country..."
                />
              </Form.Control>
            </Form.Item>
          )}
        />

        <Form.Field
          control={control}
          name={`${fieldPrefix}.cityId` as FieldPath<T>}
          render={({ field }) => (
            <Form.Item>
              <div className="flex w-full items-end justify-between">
                <Form.Label>
                  City <span className="text-destructive">*</span>
                </Form.Label>
                <Form.Message />
              </div>
              <Form.Control>
                <CitySelector
                  countryId={countryValue || ''}
                  value={field.value}
                  onValueChange={(value) => {
                    field.onChange(value);
                    onCityChange?.(value);
                  }}
                  disabled={disabled || !countryValue}
                  placeholder="Select city..."
                />
              </Form.Control>
            </Form.Item>
          )}
        />
      </div>

      <div className="grid grid-cols-1 gap-5 md:grid-cols-2">
        <Form.Field
          control={control}
          name={`${fieldPrefix}.districtId` as FieldPath<T>}
          render={({ field }) => (
            <Form.Item>
              <div className="flex w-full items-end justify-between">
                <Form.Label>
                  District <span className="text-destructive">*</span>
                </Form.Label>
                <Form.Message />
              </div>
              <Form.Control>
                <DistrictSelector
                  cityId={cityValue}
                  value={field.value}
                  onValueChange={field.onChange}
                  disabled={disabled || !cityValue}
                  placeholder="Select district..."
                />
              </Form.Control>
            </Form.Item>
          )}
        />

        <Form.Field
          control={control}
          name={`${fieldPrefix}.street` as FieldPath<T>}
          render={({ field }) => (
            <Form.Item>
              <div className="flex w-full items-end justify-between">
                <Form.Label>
                  Street Address <span className="text-destructive">*</span>
                </Form.Label>
                <Form.Message />
              </div>
              <Form.Control>
                <Input
                  placeholder="Enter street address"
                  disabled={disabled}
                  {...field}
                  value={field.value || ''}
                />
              </Form.Control>
            </Form.Item>
          )}
        />
      </div>

      <Form.Field
        control={control}
        name={`${fieldPrefix}.additionalInfo` as FieldPath<T>}
        render={({ field }) => (
          <Form.Item>
            <div className="flex w-full items-end justify-between">
              <Form.Label optional>Additional Information</Form.Label>
              <Form.Message />
            </div>
            <Form.Control>
              <Textarea
                rows={3}
                placeholder="Landmarks, directions, or other details"
                disabled={disabled}
                {...field}
                value={field.value || ''}
              />
            </Form.Control>
          </Form.Item>
        )}
      />
    </div>
  );
};
