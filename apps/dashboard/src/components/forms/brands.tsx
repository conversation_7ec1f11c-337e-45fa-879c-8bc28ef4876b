import { type Image, ImagePicker } from '@/components/image-picker';
import { slugify } from '@/lib/slug';
import { getImageUrl } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  useCreateBrand,
  useDeleteBrandImage,
  useGetCategories,
  useUpdateBrand,
  useUploadBrandImage,
} from '@muraadso/api/hooks';
import {
  type Brand,
  type CreateBrand,
  type UpdateBrand,
  createBrandSchema,
  updateBrandSchema,
} from '@muraadso/models/brands';
import {
  Button,
  Checkbox,
  Dialog,
  Form,
  Input,
  Label,
  Textarea,
} from '@muraadso/ui';
import type React from 'react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

export const BrandsForm: React.FC<{
  open: boolean;
  onClose: (brand?: Brand) => void;
  payload?: Brand;
}> = ({ open, onClose, payload }) => {
  const { mutateAsync: create, isPending: isCreating } = useCreateBrand();
  const { mutateAsync: update, isPending: isUpdating } = useUpdateBrand();
  const { mutateAsync: uploadImage, isPending: isUploading } =
    useUploadBrandImage();

  const { mutateAsync: deleteImage, isPending: isDeletingImage } =
    useDeleteBrandImage();

  const { categories, isPending: isCategoriesLoading } = useGetCategories();

  const isLoading = isCreating || isUpdating || isUploading || isDeletingImage;

  const [images, setImages] = useState<Image[]>(() => {
    if (payload?.image) {
      return [
        {
          id: crypto.randomUUID(),
          url: getImageUrl(payload.image),
          isExisting: true,
        },
      ];
    }
    return [];
  });

  const form = useForm({
    resolver: zodResolver(payload ? updateBrandSchema : createBrandSchema),
    defaultValues: {
      id: payload?.id ?? undefined,
      name: payload?.name ?? '',
      slug: payload?.slug ?? '',
      description: payload?.description ?? '',
      rank: payload?.rank ?? 1,
      categoryIds: payload?.categories?.map((cat) => cat.id) ?? [],
    } as CreateBrand | UpdateBrand,
  });

  const handleDeleteImage = async (image: Image) => {
    if (!image?.id) return false;
    const confirmed = window.confirm(
      'Are you sure you want to delete this brand image?',
    );
    if (!confirmed) return false;

    try {
      await deleteImage(payload!.id);
      return true;
    } catch (error) {
      console.error('Failed to delete brand image:', error);
      return false;
    }
  };

  useEffect(() => {
    if (payload) {
      form.reset({
        ...payload,
        categoryIds: payload.categories?.map((cat) => cat.id) ?? [],
      });
    } else {
      form.reset({
        name: '',
        slug: '',
        description: '',
        rank: 1,
        categoryIds: [],
      });
    }
  }, [payload, form.reset]);

  const onSubmit = async (data: CreateBrand | UpdateBrand) => {
    const isUpdate = !!payload;

    try {
      const {
        payload: { id },
      } = isUpdate
        ? await update(data as UpdateBrand)
        : await create(data as CreateBrand);

      const newImage = images.find((img) => img.file && !img.isExisting);
      if (newImage?.file) {
        await uploadImage({ id, image: newImage?.file });
      }

      form.reset();
      onClose();
    } catch (_) {}
  };

  return (
    <Dialog open={open} onOpenChange={() => onClose()}>
      <Dialog.Content className="min-w-md">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <Dialog.Body title={`${payload ? 'Edit' : 'New'} Brand`}>
              <div className="grid gap-2.5">
                <Label className="leading-none">Image</Label>
                <ImagePicker
                  value={images}
                  onChange={setImages}
                  onDeleteExisting={handleDeleteImage}
                />
              </div>

              <div className="mt-5 grid w-full grid-cols-2 items-start gap-x-6">
                <Form.Field
                  name="name"
                  control={form.control}
                  render={({ field: { onChange, ...rest } }) => (
                    <Form.Item>
                      <Form.Label htmlFor="name" className="leading-none">
                        Name <span className="text-destructive">*</span>
                      </Form.Label>
                      <Form.Control>
                        <Input
                          id="name"
                          type="text"
                          placeholder="Apple"
                          onChange={(e) => {
                            onChange(e.target.value);
                            form.setValue('slug', slugify(e.target.value));
                            form.trigger('slug');
                          }}
                          disabled={isLoading}
                          {...rest}
                        />
                      </Form.Control>
                      <Form.Message className="font-normal" />
                    </Form.Item>
                  )}
                />

                <Form.Field
                  name="slug"
                  control={form.control}
                  render={({ field }) => (
                    <Form.Item>
                      <Form.Label
                        tooltip={
                          <span>
                            A slug is a human-readable ID that must be <br />
                            unique. It&apos;s often used in URLs.
                          </span>
                        }
                        htmlFor="slug"
                        className="leading-none"
                      >
                        <span>Slug </span>
                        <span className="text-destructive">*</span>
                      </Form.Label>
                      <Form.Control>
                        <Input
                          id="slug"
                          type="text"
                          placeholder="/apple"
                          disabled={isLoading}
                          {...field}
                        />
                      </Form.Control>
                      <Form.Message className="font-normal" />
                    </Form.Item>
                  )}
                />
              </div>
              <Form.Field
                name="description"
                control={form.control}
                render={({ field }) => (
                  <Form.Item className="mt-5">
                    <Form.Label
                      optional
                      htmlFor="description"
                      className="leading-none"
                    >
                      Description
                    </Form.Label>
                    <Form.Control>
                      <Textarea
                        id="description"
                        rows={3}
                        className="resize-none"
                        disabled={isLoading}
                        {...field}
                        value={field.value ?? ''}
                      />
                    </Form.Control>
                    <Form.Message className="font-normal" />
                  </Form.Item>
                )}
              />

              <Form.Field
                name="rank"
                control={form.control}
                render={({ field }) => (
                  <Form.Item className="mt-5">
                    <Form.Label htmlFor="rank" className="leading-none">
                      <span>Display order </span>
                      <span className="text-destructive">*</span>
                    </Form.Label>
                    <Form.Control>
                      <Input
                        id="rank"
                        type="number"
                        placeholder="Display Order"
                        disabled={isLoading}
                        {...field}
                        value={field.value ?? ''}
                        onChange={(e) =>
                          field.onChange(
                            e.target.value === ''
                              ? undefined
                              : Number(e.target.value),
                          )
                        }
                      />
                    </Form.Control>
                    <Form.Message className="font-normal" />
                    <Form.Description>
                      Sorting: Lower numbers have higher priority
                    </Form.Description>
                  </Form.Item>
                )}
              />

              {/* Categories Selection */}
              <Form.Field
                name="categoryIds"
                control={form.control}
                render={({ field }) => (
                  <Form.Item className="mt-5">
                    <Form.Label className="leading-none">Categories</Form.Label>
                    <Form.Control>
                      <div className="mt-2 grid grid-cols-2 gap-3">
                        {isCategoriesLoading ? (
                          <div className="col-span-2 text-muted-foreground text-sm">
                            Loading categories...
                          </div>
                        ) : categories && categories.length > 0 ? (
                          categories.map((category) => (
                            <div
                              key={category.id}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={`category-${category.id}`}
                                checked={
                                  field.value?.includes(category.id) || false
                                }
                                onCheckedChange={(checked) => {
                                  const currentIds = field.value || [];
                                  if (checked) {
                                    field.onChange([
                                      ...currentIds,
                                      category.id,
                                    ]);
                                  } else {
                                    field.onChange(
                                      currentIds.filter(
                                        (id) => id !== category.id,
                                      ),
                                    );
                                  }
                                }}
                                disabled={isLoading}
                              />
                              <Label
                                htmlFor={`category-${category.id}`}
                                className="cursor-pointer font-normal text-sm"
                              >
                                {category.name}
                              </Label>
                            </div>
                          ))
                        ) : (
                          <div className="col-span-2 text-muted-foreground text-sm">
                            No categories available
                          </div>
                        )}
                      </div>
                    </Form.Control>
                    <Form.Description>
                      Select the categories this brand belongs to
                    </Form.Description>
                    <Form.Message className="font-normal" />
                  </Form.Item>
                )}
              />
            </Dialog.Body>
            <Dialog.Footer>
              <Button
                type="button"
                disabled={isLoading}
                onClick={() => onClose()}
                variant="outline"
              >
                Cancel
              </Button>

              <Button type="submit" loading={isLoading}>
                Save changes
              </Button>
            </Dialog.Footer>
          </form>
        </Form>
      </Dialog.Content>
    </Dialog>
  );
};
