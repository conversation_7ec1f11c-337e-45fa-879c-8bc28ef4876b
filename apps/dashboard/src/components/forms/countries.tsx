import { zodResolver } from '@hookform/resolvers/zod';
import { useCreateCountry, useUpdateCountry } from '@muraadso/api/hooks';
import {
  type Country,
  type CreateCountry,
  type UpdateCountry,
  createCountrySchema,
  updateCountrySchema,
} from '@muraadso/models/countries';
import { Button, Dialog, Form, Input } from '@muraadso/ui';
import type React from 'react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';

export const CountriesForm: React.FC<{
  open: boolean;
  onClose: (country?: Country) => void;
  payload?: Country;
}> = ({ open, onClose, payload }) => {
  const { mutateAsync: create, isPending: isCreating } = useCreateCountry();
  const { mutateAsync: update, isPending: isUpdating } = useUpdateCountry();

  const isLoading = isCreating || isUpdating;

  const form = useForm<CreateCountry | UpdateCountry>({
    resolver: zodResolver(payload ? updateCountrySchema : createCountrySchema),
    defaultValues: {
      id: payload?.id ?? undefined,
      name: payload?.name ?? '',
      code: payload?.code ?? '',
      emoji: payload?.emoji ?? '',
    },
  });

  const onSubmit = async (data: CreateCountry | UpdateCountry) => {
    try {
      const result = payload
        ? await update(data as UpdateCountry)
        : await create(data as CreateCountry);
      onClose(result);
    } catch (error) {
      console.error('Error saving country:', error);
    }
  };

  useEffect(() => {
    if (payload) {
      form.reset({
        id: payload.id,
        name: payload.name,
        code: payload.code,
        emoji: payload.emoji,
      });
    } else {
      form.reset({
        name: '',
        code: '',
        emoji: '',
      });
    }
  }, [payload, form]);

  return (
    <Dialog open={open} onOpenChange={() => onClose()}>
      <Dialog.Content className="min-w-md">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <Dialog.Body title={`${payload ? 'Edit' : 'New'} Country`}>
              <Form.Field
                name="name"
                control={form.control}
                render={({ field }) => (
                  <Form.Item>
                    <Form.Label htmlFor="name" className="leading-none">
                      Name <span className="text-destructive">*</span>
                    </Form.Label>
                    <Form.Control>
                      <Input
                        id="name"
                        type="text"
                        placeholder="Country Name"
                        disabled={isLoading}
                        {...field}
                      />
                    </Form.Control>
                    <Form.Message className="font-normal" />
                  </Form.Item>
                )}
              />

              <Form.Field
                name="code"
                control={form.control}
                render={({ field }) => (
                  <Form.Item className="mt-5">
                    <Form.Label htmlFor="code" className="leading-none">
                      Code <span className="text-destructive">*</span>
                    </Form.Label>
                    <Form.Control>
                      <Input
                        id="code"
                        type="text"
                        placeholder="Country Code (e.g., US, GB)"
                        disabled={isLoading}
                        {...field}
                      />
                    </Form.Control>
                    <Form.Message className="font-normal" />
                  </Form.Item>
                )}
              />

              <Form.Field
                name="emoji"
                control={form.control}
                render={({ field }) => (
                  <Form.Item className="mt-5">
                    <Form.Label htmlFor="emoji" className="leading-none">
                      Emoji <span className="text-destructive">*</span>
                    </Form.Label>
                    <Form.Control>
                      <Input
                        id="emoji"
                        type="text"
                        placeholder="🇺🇸"
                        disabled={isLoading}
                        {...field}
                      />
                    </Form.Control>
                    <Form.Message className="font-normal" />
                  </Form.Item>
                )}
              />
            </Dialog.Body>

            <Dialog.Footer>
              <Button
                type="button"
                variant="outline"
                onClick={() => onClose()}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Saving...' : payload ? 'Update' : 'Create'}
              </Button>
            </Dialog.Footer>
          </form>
        </Form>
      </Dialog.Content>
    </Dialog>
  );
};
