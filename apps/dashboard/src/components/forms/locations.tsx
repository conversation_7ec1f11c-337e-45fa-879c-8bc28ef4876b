import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useCreateLocation, useUpdateLocation } from '@muraadso/api/hooks';
import {
  type CreateLocation,
  type Location,
  type UpdateLocation,
  createLocationSchema,
  updateLocationSchema,
} from '@muraadso/models/locations';
import { Button, Dialog, Form, Input, Textarea } from '@muraadso/ui';
import type React from 'react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';

export const LocationsForm: React.FC<{
  open: boolean;
  onClose: (location?: Location) => void;
  payload?: Location;
}> = ({ open, onClose, payload }) => {
  const { mutateAsync: create, isPending: isCreating } = useCreateLocation();
  const { mutateAsync: update, isPending: isUpdating } = useUpdateLocation();

  const isLoading = isCreating || isUpdating;

  const form = useForm<CreateLocation | UpdateLocation>({
    resolver: zodResolver(
      payload ? updateLocationSchema : createLocationSchema,
    ),
    defaultValues: {
      id: payload?.id ?? undefined,
      name: payload?.name ?? '',
      description: payload?.description ?? '',
      address: payload?.address ?? '',
      city: payload?.city ?? '',
      contactNumber: payload?.contactNumber ?? '',
    },
  });

  useEffect(() => {
    form.reset(payload);
  }, [payload, form.reset]);

  const onSubmit = async (data: CreateLocation | UpdateLocation) => {
    const isUpdate = !!payload;

    try {
      isUpdate ? await update(data) : await create(data);

      form.reset();
      onClose();
    } catch (_) {}
  };

  return (
    <Dialog open={open} onOpenChange={() => onClose()}>
      <Dialog.Content className="min-w-md">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <Dialog.Body title={`${payload ? 'Edit' : 'New'} Location`}>
              <Form.Field
                name="name"
                control={form.control}
                render={({ field }) => (
                  <Form.Item>
                    <Form.Label htmlFor="name" className="leading-none">
                      Name <span className="text-destructive">*</span>
                    </Form.Label>
                    <Form.Control>
                      <Input
                        id="name"
                        type="text"
                        placeholder="Location Name"
                        disabled={isLoading}
                        {...field}
                      />
                    </Form.Control>
                    <Form.Message className="font-normal" />
                  </Form.Item>
                )}
              />

              <Form.Field
                name="description"
                control={form.control}
                render={({ field }) => (
                  <Form.Item className="mt-5">
                    <Form.Label
                      optional
                      htmlFor="description"
                      className="leading-none"
                    >
                      Description
                    </Form.Label>
                    <Form.Control>
                      <Textarea
                        id="description"
                        placeholder="Description"
                        rows={3}
                        className="resize-none"
                        disabled={isLoading}
                        {...field}
                        value={field.value ?? ''}
                      />
                    </Form.Control>
                    <Form.Message className="font-normal" />
                  </Form.Item>
                )}
              />

              <Form.Field
                name="address"
                control={form.control}
                render={({ field }) => (
                  <Form.Item className="mt-5">
                    <Form.Label htmlFor="address" className="leading-none">
                      Address <span className="text-destructive">*</span>
                    </Form.Label>
                    <Form.Control>
                      <Input
                        id="address"
                        type="text"
                        placeholder="Street Address"
                        disabled={isLoading}
                        {...field}
                      />
                    </Form.Control>
                    <Form.Message className="font-normal" />
                  </Form.Item>
                )}
              />

              <div className="mt-5 grid w-full grid-cols-2 items-start gap-x-6">
                <Form.Field
                  name="city"
                  control={form.control}
                  render={({ field }) => (
                    <Form.Item>
                      <Form.Label htmlFor="city" className="leading-none">
                        City <span className="text-destructive">*</span>
                      </Form.Label>
                      <Form.Control>
                        <Input
                          id="city"
                          type="text"
                          placeholder="City"
                          disabled={isLoading}
                          {...field}
                        />
                      </Form.Control>
                      <Form.Message className="font-normal" />
                    </Form.Item>
                  )}
                />

                <Form.Field
                  name="contactNumber"
                  control={form.control}
                  render={({ field }) => (
                    <Form.Item>
                      <Form.Label
                        htmlFor="contactNumber"
                        className="leading-none"
                      >
                        Contact Number{' '}
                        <span className="text-destructive">*</span>
                      </Form.Label>
                      <Form.Control>
                        <Input
                          id="contactNumber"
                          type="text"
                          placeholder="Contact Number"
                          disabled={isLoading}
                          {...field}
                        />
                      </Form.Control>
                      <Form.Message className="font-normal" />
                    </Form.Item>
                  )}
                />
              </div>
            </Dialog.Body>
            <Dialog.Footer>
              <Button
                type="button"
                disabled={isLoading}
                onClick={() => onClose()}
                variant="outline"
              >
                Cancel
              </Button>

              <Button type="submit" loading={isLoading}>
                Save changes
              </Button>
            </Dialog.Footer>
          </form>
        </Form>
      </Dialog.Content>
    </Dialog>
  );
};
