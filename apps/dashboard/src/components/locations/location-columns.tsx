'use client';

import { useModal } from '@/components/modal';
import { useDeleteLocation } from '@muraadso/api/hooks';
import { MoreHorizontalIcon } from '@muraadso/icons';
import type { Location } from '@muraadso/models/locations';
import { Button, Checkbox, DropdownMenu } from '@muraadso/ui';
import type { ColumnDef } from '@tanstack/react-table';
import { formatDate } from 'date-fns';

export function useLocationColumns(): ColumnDef<Location>[] {
  const { mutateAsync: deleteLocation } = useDeleteLocation();
  const modal = useModal();

  return [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
    },
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => (
        <div className="capitalize">{row.original?.name ?? '-'}</div>
      ),
    },
    {
      accessorKey: 'description',
      header: 'Description',
      cell: ({ row }) => (
        <div className="line-clamp-1 max-w-sm">
          {row.getValue('description') || '-'}
        </div>
      ),
    },
    {
      accessorKey: 'address',
      header: 'Address',
      cell: ({ row }) => (
        <div className="line-clamp-1 max-w-sm">
          {row.getValue('address') || '-'}
        </div>
      ),
    },
    {
      accessorKey: 'city',
      header: 'City',
      cell: ({ row }) => (
        <div className="capitalize">{row.getValue('city') || '-'}</div>
      ),
    },
    {
      accessorKey: 'contactNumber',
      header: 'Contact Number',
      cell: ({ row }) => <div>{row.getValue('contactNumber') || '-'}</div>,
    },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      cell: ({ row }) => (
        <div className="whitespace-nowrap">
          {formatDate(row.original?.createdAt, 'dd MMM, yyyy') ?? '-'}
        </div>
      ),
    },
    {
      id: 'actions',
      enableHiding: false,
      cell: ({ row }) => {
        const location = row.original;
        return (
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontalIcon className="size-5 text-muted-foreground opacity-0 group-hover:opacity-100" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content align="end">
              <DropdownMenu.Label>Actions</DropdownMenu.Label>
              <DropdownMenu.Item
                onClick={() => modal.open('locations', location)}
              >
                Edit
              </DropdownMenu.Item>
              <DropdownMenu.Item
                onClick={async () => {
                  try {
                    await deleteLocation(location.id);
                  } catch (_) {}
                }}
              >
                Delete
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu>
        );
      },
    },
  ];
}
