'use client';

import { DataTable } from '@/components/data-table';
import type { Location } from '@muraadso/models/locations';
import { useLocationColumns } from './location-columns';

interface LocationTableProps {
  locations: Location[];
}

export const LocationTable = ({ locations }: LocationTableProps) => {
  const columns = useLocationColumns();

  return (
    <DataTable
      columns={columns}
      data={locations}
      enablePagination={false} // Disable client-side pagination as we're using server-side pagination
    />
  );
};
