import { CheckIcon } from '@muraadso/icons';
import { cn } from '@muraadso/ui/utils';
import React from 'react';

export type Step = {
  title: string;
  description?: string;
};

export const Stepper = ({
  steps,
  currentStep,
  className,
}: {
  steps: Step[];
  currentStep: Step;
  className?: string;
}) => {
  return (
    <div className={cn('w-full', className)}>
      <div className="flex items-center justify-between">
        {steps.map((step, index) => (
          <div key={step.title} className="flex items-center">
            <div className="flex flex-col items-center">
              {/* Step Circle */}
              <div
                className={cn(
                  'flex h-10 w-10 items-center justify-center rounded-full border-2 transition-colors',
                  {
                    'border-primary bg-primary text-primary-foreground':
                      index < steps.indexOf(currentStep),
                    'border-muted-foreground/30 bg-muted text-muted-foreground':
                      index > steps.indexOf(currentStep),
                  },
                )}
              >
                {index < steps.indexOf(currentStep) ? (
                  <CheckIcon className="h-5 w-5" />
                ) : (
                  <span className="font-medium text-sm">{index + 1}</span>
                )}
              </div>

              {/* Step Title */}
              <div className="mt-2 text-center">
                <div
                  className={cn('font-medium text-sm', {
                    'text-primary': index < steps.indexOf(currentStep),
                    'text-muted-foreground': index > steps.indexOf(currentStep),
                  })}
                >
                  {step.title}
                </div>
                {step.description && (
                  <div className="text-muted-foreground text-xs">
                    {step.description}
                  </div>
                )}
              </div>
            </div>

            {/* Connection Line */}
            {index < steps.length - 1 && (
              <div
                className={cn('mx-4 h-0.5 w-16 transition-colors', {
                  'bg-primary': index < steps.indexOf(currentStep),
                  'bg-muted-foreground/30': index >= steps.indexOf(currentStep),
                })}
              />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
