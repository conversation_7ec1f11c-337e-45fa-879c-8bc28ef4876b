'use client';

import { useModal } from '@/components/modal';
import { PlusIcon, UploadIcon } from '@muraadso/icons';
import { Button } from '@muraadso/ui';

export function CustomerActions() {
  const { open } = useModal();

  return (
    <div className="flex flex-1 items-center justify-end space-x-4">
      <Button variant="outline">
        <UploadIcon className="size-4 text-muted-foreground" />
        <span className="ml-2">Export</span>
      </Button>

      <Button onClick={() => open('customers')}>
        <PlusIcon className="size-4" />
        <span className="ml-2">New Customer</span>
      </Button>
    </div>
  );
}
