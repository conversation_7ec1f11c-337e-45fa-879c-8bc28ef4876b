'use client';

import { ChevronLeftIcon, ChevronRightIcon } from '@muraadso/icons';
import { Button } from '@muraadso/ui';
import { parseAsInteger, useQueryState } from 'nuqs';

interface CustomerPaginationProps {
  totalPages: number;
  totalItems?: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export function CustomerPagination({
  totalPages,
  totalItems,
  hasNextPage,
  hasPreviousPage,
}: CustomerPaginationProps) {
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1));

  const handlePreviousPage = () => {
    if (hasPreviousPage) {
      setPage(page - 1);
    }
  };

  const handleNextPage = () => {
    if (hasNextPage) {
      setPage(page + 1);
    }
  };

  return (
    <div className="flex items-center justify-between px-2 py-4">
      <div className="flex-1 text-muted-foreground text-sm">
        {totalItems && (
          <p>
            Showing page {page} of {totalPages} ({totalItems} total customers)
          </p>
        )}
      </div>
      <div className="flex items-center space-x-6 lg:space-x-8">
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={handlePreviousPage}
            disabled={!hasPreviousPage}
          >
            <span className="sr-only">Go to previous page</span>
            <ChevronLeftIcon className="h-4 w-4" />
          </Button>
          <div className="flex w-[100px] items-center justify-center text-sm font-medium">
            Page {page} of {totalPages}
          </div>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={handleNextPage}
            disabled={!hasNextPage}
          >
            <span className="sr-only">Go to next page</span>
            <ChevronRightIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
