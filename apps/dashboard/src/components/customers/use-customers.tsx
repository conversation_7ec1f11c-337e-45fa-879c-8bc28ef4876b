'use client';

import { useGetCustomers } from '@muraadso/api/hooks';
import { parseAsInteger, useQueryState } from 'nuqs';

export function useCustomers() {
  const [page] = useQueryState('page', parseAsInteger.withDefault(1));
  const [pageSize] = useQueryState('pageSize', parseAsInteger.withDefault(10));
  const [q] = useQueryState('q');
  const [startDate] = useQueryState('startDate');
  const [endDate] = useQueryState('endDate');
  const [hasUser] = useQueryState('hasUser');
  const [sortOrder] = useQueryState('sortOrder', { defaultValue: 'desc' });

  // Convert hasUser string to boolean if it exists
  const hasUserValue =
    hasUser === 'true' ? true : hasUser === 'false' ? false : undefined;

  // Fetch customers with the query parameters
  const { isPending, customers, pagination, error } = useGetCustomers({
    page,
    pageSize,
    q: q || undefined,
    startDate: startDate || undefined,
    endDate: endDate || undefined,
    hasUser: hasUserValue,
    sortOrder: sortOrder as 'asc' | 'desc',
  });

  return {
    isPending,
    error,
    customers: customers || [],
    pagination: pagination,
  };
}
