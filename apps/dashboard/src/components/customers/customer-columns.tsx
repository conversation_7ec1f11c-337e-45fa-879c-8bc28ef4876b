'use client';

import { useModal } from '@/components/modal';
import { useDeleteCustomer } from '@muraadso/api/hooks';
import { MoreHorizontalIcon } from '@muraadso/icons';
import type { Customer } from '@muraadso/models/customers';
import {
  Avatar,
  Badge,
  Button,
  Checkbox,
  DropdownMenu,
  toast,
} from '@muraadso/ui';
import type { ColumnDef } from '@tanstack/react-table';
import { formatDate } from 'date-fns';

export function useCustomerColumns(): ColumnDef<Customer>[] {
  const { mutateAsync: deleteCustomer } = useDeleteCustomer();
  const modal = useModal();

  return [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
    },
    {
      accessorKey: 'name',
      header: 'Customer',
      cell: ({ row }) => (
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <Avatar.Image
              src={row.original.image || '/images/avatar.svg'}
              alt={row.original.name}
            />
            <Avatar.Fallback>
              {row.original.name
                .split(' ')
                .map((n) => n[0])
                .join('')
                .toUpperCase()}
            </Avatar.Fallback>
          </Avatar>
          <div className="flex flex-col">
            <span className="font-medium">{row.original.name}</span>
            {row.original.user && (
              <Badge variant="secondary" className="w-fit text-xs">
                Has Account
              </Badge>
            )}
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'email',
      header: 'Email',
      cell: ({ row }) => (
        <div className="text-muted-foreground">
          {row.original.email || row.original.user?.email || '-'}
        </div>
      ),
    },
    {
      accessorKey: 'phone',
      header: 'Phone',
      cell: ({ row }) => (
        <div className="text-muted-foreground">
          {row.original.phone || row.original.user?.phone || '-'}
        </div>
      ),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => (
        <div className="text-muted-foreground">
          {!row.original.user ? 'Guest' : 'Registered'}
        </div>
      ),
    },
    {
      accessorKey: 'createdAt',
      header: 'Registration Date',
      cell: ({ row }) => (
        <div className="text-muted-foreground">
          {formatDate(row.original.createdAt, 'MMM dd, yyyy') ?? '-'}
        </div>
      ),
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const customer = row.original;

        return (
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontalIcon className="h-4 w-4" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content align="end">
              <DropdownMenu.Label>Actions</DropdownMenu.Label>
              <DropdownMenu.Item onClick={() => {}}>
                Edit customer
              </DropdownMenu.Item>
              <DropdownMenu.Item
                onClick={() => {
                  navigator.clipboard.writeText(customer.id);
                  toast.success('Customer ID copied to clipboard!');
                }}
              >
                Copy customer ID
              </DropdownMenu.Item>
              <DropdownMenu.Item>View orders</DropdownMenu.Item>
              <DropdownMenu.Separator />
              <DropdownMenu.Item
                className="text-destructive"
                onClick={async () => {
                  if (
                    confirm('Are you sure you want to delete this customer?')
                  ) {
                    await deleteCustomer(customer.id);
                  }
                }}
              >
                Delete customer
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu>
        );
      },
    },
  ];
}
