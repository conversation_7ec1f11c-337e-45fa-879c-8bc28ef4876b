'use client';

import { DataTable } from '@/components/data-table';
import type { Customer } from '@muraadso/models/customers';
import { useCustomerColumns } from './customer-columns';

type CustomerTableProps = {
  customers: Customer[];
};

export const CustomerTable = ({ customers }: CustomerTableProps) => {
  const columns = useCustomerColumns();

  return (
    <DataTable
      columns={columns}
      data={customers}
      enablePagination={false} // Disable client-side pagination as we're using server-side pagination
    />
  );
};
