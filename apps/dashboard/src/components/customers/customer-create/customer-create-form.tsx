import { zodResolver } from '@hookform/resolvers/zod';
import { useCreateCustomer } from '@muraadso/api/hooks';
import type { Customer } from '@muraadso/models/customers';
import { Button, Dialog, Form, Input, Tabs } from '@muraadso/ui';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod/v4';

const createCustomerWithUserSchema = z.object({
  user: z.object({
    email: z.email().optional(),
    phone: z.string().min(1, 'Phone number is required'),
  }),
  customer: z.object({
    name: z.string().min(1, 'Name is required'),
    nationalId: z.string().optional(),
    image: z.string().optional(),
  }),
  address: z
    .object({
      label: z.string().optional(),
      countryId: z.string().uuid('Please select a country'),
      street: z.string().min(1, 'Street address is required'),
      buildingNumber: z.string().optional(),
      cityId: z.string().uuid('Please select a city'),
      districtId: z.string().uuid().optional(),
      additionalInfo: z.string().optional(),
    })
    .optional(),
});

type CreateCustomerWithUser = z.infer<typeof createCustomerWithUserSchema>;

type CustomerCreateFormProps = {
  open: boolean;
  onClose: (customer?: Customer) => void;
};

export const CustomerCreateForm = ({
  open,
  onClose,
}: CustomerCreateFormProps) => {
  const { mutateAsync: createCustomer, isPending: isCreating } =
    useCreateCustomer();

  const form = useForm<CreateCustomerWithUser>({
    resolver: zodResolver(createCustomerWithUserSchema),
    defaultValues: {
      user: {
        email: '',
        phone: '',
      },
      customer: {
        name: '',
        nationalId: '',
        image: '',
      },
      address: {
        label: '',
        countryId: '',
        street: '',
        buildingNumber: '',
        cityId: '',
        districtId: '',
        additionalInfo: '',
      },
    },
  });

  const onSubmit = async (data: CreateCustomerWithUser) => {
    try {
      const result = await createCustomer(data);
      form.reset();
      onClose(result);
    } catch (_) {}
  };

  return (
    <Dialog open={open} onOpenChange={() => onClose()}>
      <Dialog.Portal>
        <Dialog.Content className="z-50 min-w-2xl">
          <Dialog.Body title="Create New Customer">
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <Form.Field
                    control={form.control}
                    name="customer.name"
                    render={({ field }) => (
                      <Form.Item>
                        <Form.Label>
                          Full Name <span className="text-destructive">*</span>
                        </Form.Label>
                        <Form.Control>
                          <Input placeholder="Enter full name" {...field} />
                        </Form.Control>
                        <Form.Message />
                      </Form.Item>
                    )}
                  />

                  <Form.Field
                    control={form.control}
                    name="user.phone"
                    render={({ field }) => (
                      <Form.Item>
                        <Form.Label>
                          Phone <span className="text-destructive">*</span>
                        </Form.Label>
                        <Form.Control>
                          <Input placeholder="+252 63 xxxx xxx" {...field} />
                        </Form.Control>
                        <Form.Message />
                      </Form.Item>
                    )}
                  />

                  <Form.Field
                    control={form.control}
                    name="user.email"
                    render={({ field }) => (
                      <Form.Item>
                        <Form.Label optional>Email</Form.Label>
                        <Form.Control>
                          <Input
                            type="email"
                            placeholder="<EMAIL>"
                            {...field}
                          />
                        </Form.Control>
                        <Form.Message />
                      </Form.Item>
                    )}
                  />

                  <Form.Field
                    control={form.control}
                    name="customer.nationalId"
                    render={({ field }) => (
                      <Form.Item>
                        <Form.Label optional>National ID</Form.Label>
                        <Form.Control>
                          <Input
                            placeholder="1234-5678-9012"
                            {...field}
                            value={field.value || ''}
                          />
                        </Form.Control>
                        <Form.Message />
                      </Form.Item>
                    )}
                  />
                </div>
              </form>
            </Form>
          </Dialog.Body>
          <Dialog.Footer>
            <Button type="button" variant="outline" onClick={() => onClose()}>
              Cancel
            </Button>
            <Button
              type="button"
              onClick={form.handleSubmit(onSubmit)}
              disabled={isCreating}
            >
              {isCreating ? 'Creating...' : 'Create Customer'}
            </Button>
          </Dialog.Footer>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog>
  );
};
