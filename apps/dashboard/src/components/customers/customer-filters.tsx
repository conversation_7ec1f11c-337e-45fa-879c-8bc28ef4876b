'use client';

import { FilterIcon } from '@muraadso/icons';
import { Button, Label, Popover, Select } from '@muraadso/ui';
import { useQueryState } from 'nuqs';
import { format } from 'date-fns';
import { subDays } from 'date-fns';

export function CustomerFilters() {
  const [hasUser, setHasUser] = useQueryState('hasUser');
  const [startDate, setStartDate] = useQueryState('startDate');
  const [endDate, setEndDate] = useQueryState('endDate');

  // Reset all filters
  const resetFilters = () => {
    setHasUser(null);
    setStartDate(null);
    setEndDate(null);
  };

  // Quick date filters
  const setDateRange = (days: number) => {
    const end = new Date();
    const start = subDays(end, days);
    setStartDate(format(start, 'yyyy-MM-dd'));
    setEndDate(format(end, 'yyyy-MM-dd'));
  };

  return (
    <Popover>
      <Popover.Trigger asChild>
        <Button variant="outline" className="gap-2">
          <FilterIcon className="h-4 w-4" />
          Filters
        </Button>
      </Popover.Trigger>
      <Popover.Content className="w-80">
        <div className="space-y-4">
          <div className="-mx-4 -mt-4 rounded-t-md bg-accent px-4 py-3">
            <h4 className="font-medium">Filter Customers</h4>
          </div>

          <div className="flex flex-col space-y-2">
            <Label htmlFor="hasUser">Account Status</Label>
            <Select
              onValueChange={(value) =>
                setHasUser(value === 'all' ? null : value)
              }
              value={hasUser || 'all'}
            >
              <Select.Trigger id="hasUser">
                <Select.Value placeholder="All Customers" />
              </Select.Trigger>
              <Select.Content>
                <Select.Item value="all">All Customers</Select.Item>
                <Select.Item value="true">Has User Account</Select.Item>
                <Select.Item value="false">No User Account</Select.Item>
              </Select.Content>
            </Select>
          </div>

          <div className="flex flex-col space-y-2">
            <Label>Registration Date</Label>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setDateRange(7)}
                className="text-xs"
              >
                Last 7 days
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setDateRange(30)}
                className="text-xs"
              >
                Last 30 days
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setDateRange(90)}
                className="text-xs"
              >
                Last 90 days
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setDateRange(365)}
                className="text-xs"
              >
                Last year
              </Button>
            </div>
          </div>

          <div className="flex justify-between pt-4">
            <Button variant="outline" size="sm" onClick={resetFilters}>
              Reset
            </Button>
          </div>
        </div>
      </Popover.Content>
    </Popover>
  );
}
