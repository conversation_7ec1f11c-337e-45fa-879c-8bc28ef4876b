'use client';

import { useGetCustomerStats } from '@muraadso/api/hooks';
import { Skeleton } from '@muraadso/ui';

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};

const formatPercentage = (percentage: number) => {
  const sign = percentage >= 0 ? '+' : '';
  return `${sign}${percentage.toFixed(1)}%`;
};

export function CustomerStats() {
  const { stats, isPending, error } = useGetCustomerStats();

  if (isPending) {
    return (
      <div className="mb-8 grid w-full grid-cols-4 gap-8 divide-x divide-border border-border border-y border-dashed py-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="flex w-full flex-col space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-8 w-16" />
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="mb-8 grid w-full grid-cols-4 gap-8 divide-x divide-border border-border border-y border-dashed py-6">
        <div className="col-span-4 text-center text-destructive text-sm">
          Failed to load customer stats
        </div>
      </div>
    );
  }

  return (
    <div className="mb-8 grid w-full grid-cols-4 gap-8 divide-x divide-border border-border border-y border-dashed py-6">
      <div className="flex w-full flex-col space-y-2">
        <p className="font-medium text-muted-foreground text-sm">
          Total Customers
        </p>
        <div className="flex items-center space-x-2">
          <span className="font-medium text-2xl">
            {stats.totalCustomers.toLocaleString()}
          </span>
          <span className="text-green-600 text-xs">
            +{stats.customersThisWeek}
          </span>
          <span className="text-muted-foreground text-xs">this week</span>
        </div>
      </div>

      <div className="flex w-full flex-col space-y-2">
        <p className="font-medium text-muted-foreground text-sm">
          With Accounts
        </p>
        <div className="flex items-center space-x-2">
          <span className="font-medium text-2xl">
            {stats.customersWithAccounts.toLocaleString()}
          </span>
          <span className="text-green-600 text-xs">
            +{stats.accountsThisWeek}
          </span>
          <span className="text-muted-foreground text-xs">this week</span>
        </div>
      </div>

      <div className="flex w-full flex-col space-y-2">
        <p className="font-medium text-muted-foreground text-sm">
          New This Month
        </p>
        <div className="flex items-center space-x-2">
          <span className="font-medium text-2xl">
            {stats.customersThisMonth.toLocaleString()}
          </span>
          <span
            className={`text-xs ${stats.monthlyGrowthPercentage >= 0 ? 'text-green-600' : 'text-destructive'}`}
          >
            {formatPercentage(stats.monthlyGrowthPercentage)}
          </span>
          <span className="text-muted-foreground text-xs">vs last month</span>
        </div>
      </div>

      <div className="flex w-full flex-col space-y-2">
        <p className="font-medium text-muted-foreground text-sm">
          Avg Lifetime Value
        </p>
        <div className="flex items-center space-x-2">
          <span className="font-medium text-2xl">
            {formatCurrency(stats.avgCustomerLifetimeValue)}
          </span>
          <span className="text-muted-foreground text-xs">
            {stats.repeatCustomerRate.toFixed(1)}% repeat rate
          </span>
        </div>
      </div>
    </div>
  );
}
