'use client';

import {
  CustomerActions,
  CustomerFilters,
  CustomerPagination,
  CustomerSearch,
  CustomerSort,
  CustomerStats,
  CustomerTable,
  useCustomers,
} from '@/components/customers';

export const CustomersPage = () => {
  const { isPending, customers, pagination, error } = useCustomers();

  return (
    <div className="flex w-full flex-col px-12 py-2">
      <CustomerStats />

      <div className="mb-6 flex flex-wrap gap-4">
        <CustomerSearch />
        <CustomerFilters />
        <CustomerSort />
        <CustomerActions />
      </div>

      {isPending && (
        <div className="flex items-center justify-center py-8">
          <div className="text-muted-foreground">Loading customers...</div>
        </div>
      )}

      {error && (
        <div className="flex items-center justify-center py-8">
          <div className="text-destructive">
            Error loading customers: {error.message}
          </div>
        </div>
      )}

      {!isPending && !error && customers.length === 0 && (
        <div className="flex items-center justify-center py-8">
          <div className="text-muted-foreground">No customers found.</div>
        </div>
      )}

      {!isPending && !error && customers.length > 0 && (
        <>
          <CustomerTable customers={customers} />

          {pagination && (
            <CustomerPagination
              totalPages={pagination.totalPages}
              totalItems={pagination.totalCount}
              hasNextPage={pagination.hasNextPage}
              hasPreviousPage={pagination.hasPreviousPage}
            />
          )}
        </>
      )}
    </div>
  );
};
