import { useGetCountries } from '@muraadso/api/hooks';
import { CheckIcon, ChevronDownIcon, SearchIcon } from '@muraadso/icons';
import { Button, Command, Popover, ScrollArea } from '@muraadso/ui';
import { cn } from '@muraadso/ui/utils';
import { useState } from 'react';

interface CountrySelectorProps {
  value?: string;
  onValueChange: (value: string | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export const CountrySelector = ({
  value,
  onValueChange,
  placeholder = 'Select country...',
  disabled = false,
  className,
}: CountrySelectorProps) => {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');

  const { countries, isPending } = useGetCountries();

  const selectedCountry = countries.find((country) => country.id === value);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <Popover.Trigger asChild>
        <Button
          variant="outline"
          aria-expanded={open}
          className={cn(
            'w-full justify-between',
            !selectedCountry && 'text-muted-foreground',
            className,
          )}
          disabled={disabled}
        >
          {selectedCountry ? (
            <div className="flex items-center gap-2">
              <span>{selectedCountry.name}</span>
              <span className="text-muted-foreground text-xs">
                ({selectedCountry.code})
              </span>
            </div>
          ) : (
            placeholder
          )}
          <ChevronDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </Popover.Trigger>
      <Popover.Content
        className="w-(--radix-popover-trigger-width) p-0"
        align="start"
      >
        <Command>
          <Command.Input
            placeholder="Search countries..."
            value={search}
            onValueChange={setSearch}
          />
          <Command.List>
            <ScrollArea className="h-60">
              {isPending ? (
                <Command.Loading>Loading countries...</Command.Loading>
              ) : countries.length === 0 ? (
                <Command.Empty>No countries found.</Command.Empty>
              ) : (
                <Command.Group>
                  {countries.map((country) => (
                    <Command.Item
                      key={country.id}
                      value={country.id}
                      onSelect={(currentValue) => {
                        onValueChange(
                          currentValue === value ? undefined : currentValue,
                        );
                        setOpen(false);
                      }}
                      className="flex items-center justify-between"
                    >
                      <div className="flex flex-col">
                        <div className="flex items-center gap-2">
                          <span className="text-muted-foreground text-xs">
                            {country.emoji}
                          </span>
                          <span>{country.name}</span>
                          <span className="text-muted-foreground text-xs">
                            ({country.code})
                          </span>
                        </div>
                      </div>
                      <CheckIcon
                        className={cn(
                          'ml-2 h-4 w-4',
                          value === country.id ? 'opacity-100' : 'opacity-0',
                        )}
                      />
                    </Command.Item>
                  ))}
                </Command.Group>
              )}
            </ScrollArea>
          </Command.List>
        </Command>
      </Popover.Content>
    </Popover>
  );
};
