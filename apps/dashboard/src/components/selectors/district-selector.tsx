import { useGetDistricts } from '@muraadso/api/hooks';
import { CheckIcon, ChevronDownIcon, SearchIcon } from '@muraadso/icons';
import { Button, Command, Popover, ScrollArea } from '@muraadso/ui';
import { cn } from '@muraadso/ui/utils';
import { useState } from 'react';

interface DistrictSelectorProps {
  cityId?: string;
  value?: string;
  onValueChange: (value: string | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export const DistrictSelector = ({
  cityId,
  value,
  onValueChange,
  placeholder = 'Select district...',
  disabled = false,
  className,
}: DistrictSelectorProps) => {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');

  const { districts, isPending } = useGetDistricts();

  const selectedDistrict = districts?.find((district) => district.id === value);

  // Filter districts by cityId and search term
  const filteredDistricts =
    districts?.filter((district) => {
      const matchesCity = !cityId || district.cityId === cityId;
      const matchesSearch = district.name
        .toLowerCase()
        .includes(search.toLowerCase());
      return matchesCity && matchesSearch;
    }) || [];

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <Popover.Trigger asChild>
        <Button
          variant="outline"
          aria-expanded={open}
          className={cn(
            'w-full justify-between',
            !selectedDistrict && 'text-muted-foreground',
            className,
          )}
          disabled={disabled || !cityId}
        >
          {selectedDistrict ? selectedDistrict.name : placeholder}
          <ChevronDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </Popover.Trigger>
      <Popover.Content
        className="w-(--radix-popover-trigger-width) p-0"
        align="start"
      >
        <Command>
          <Command.Input
            placeholder="Search districts..."
            value={search}
            onValueChange={setSearch}
          />
          <Command.List>
            <ScrollArea className="h-60">
              {isPending ? (
                <Command.Loading>Loading districts...</Command.Loading>
              ) : filteredDistricts.length === 0 ? (
                <Command.Empty>No districts found.</Command.Empty>
              ) : (
                <Command.Group>
                  {filteredDistricts.map((district) => (
                    <Command.Item
                      key={district.id}
                      value={district.id}
                      onSelect={() => {
                        onValueChange(
                          district.id === value ? undefined : district.id,
                        );
                        setOpen(false);
                      }}
                      className="flex items-center gap-2 px-2 py-1.5"
                    >
                      <CheckIcon
                        className={cn(
                          'h-4 w-4',
                          district.id === value ? 'opacity-100' : 'opacity-0',
                        )}
                      />
                      {district.name}
                    </Command.Item>
                  ))}
                </Command.Group>
              )}
            </ScrollArea>
          </Command.List>
        </Command>
      </Popover.Content>
    </Popover>
  );
};
//         <Popover.Content className="w-full p-0" align="start">
//           <Command>
//             <div className="flex items-center border-b px-3">
//               <SearchIcon className="mr-2 h-4 w-4 shrink-0 opacity-50" />
//               <Command.Input
//                 placeholder="Search districts..."
//                 value={search}
//                 onValueChange={setSearch}
//                 className="flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
//               />
//             </div>
//             <Command.List>
//               <ScrollArea className="h-60">
//                 {isPending ? (
//                   <Command.Loading>Loading districts...</Command.Loading>
//                 ) : filteredDistricts.length === 0 ? (
//                   <Command.Empty>
//                     <div className="flex flex-col items-center gap-2 py-4">
//                       <span>No districts found.</span>
//                       {allowCreate && cityId && (
//                         <Button
//                           size="sm"
//                           onClick={() => {
//                             setCreateOpen(true);
//                             setOpen(false);
//                           }}
//                         >
//                           <PlusIcon className="mr-2 h-4 w-4" />
//                           Create District
//                         </Button>
//                       )}
//                     </div>
//                   </Command.Empty>
//                 ) : (
//                   <Command.Group>
//                     {allowCreate && cityId && (
//                       <Command.Item
//                         onSelect={() => {
//                           setCreateOpen(true);
//                           setOpen(false);
//                         }}
//                         className="flex items-center text-primary"
//                       >
//                         <PlusIcon className="mr-2 h-4 w-4" />
//                         Create new district
//                       </Command.Item>
//                     )}
//                     {filteredDistricts.map((district) => (
//                       <Command.Item
//                         key={district.id}
//                         value={district.id}
//                         onSelect={(currentValue) => {
//                           onValueChange(currentValue === value ? undefined : currentValue);
//                           setOpen(false);
//                         }}
//                         className="flex items-center justify-between"
//                       >
//                         <span>{district.name}</span>
//                         <CheckIcon
//                           className={cn(
//                             'ml-2 h-4 w-4',
//                             value === district.id ? 'opacity-100' : 'opacity-0',
//                           )}
//                         />
//                       </Command.Item>
//                     ))}
//                   </Command.Group>
//                 )}
//               </ScrollArea>
//             </Command.List>
//           </Command>
//         </Popover.Content>
//       </Popover>
//
//       {/* Create District Dialog */}
//       <Dialog open={createOpen} onOpenChange={setCreateOpen}>
//         <Dialog.Portal>
//           <Dialog.Content className="max-w-sm">
//             <Dialog.Body title="Create New District">
//               <Form {...form}>
//                 <form
//                   onSubmit={form.handleSubmit(handleCreateDistrict)}
//                   className="space-y-4"
//                 >
//                   <Form.Field
//                     control={form.control}
//                     name="name"
//                     render={({ field }) => (
//                       <Form.Item>
//                         <Form.Label>
//                           District Name <span className="text-destructive">*</span>
//                         </Form.Label>
//                         <Form.Control>
//                           <Input placeholder="Enter district name" {...field} />
//                         </Form.Control>
//                         <Form.Message />
//                       </Form.Item>
//                     )}
//                   />
//                   <div className="flex justify-end space-x-2">
//                     <Button
//                       type="button"
//                       variant="outline"
//                       onClick={() => setCreateOpen(false)}
//                     >
//                       Cancel
//                     </Button>
//                     <Button type="submit" disabled={isCreating}>
//                       {isCreating ? 'Creating...' : 'Create'}
//                     </Button>
//                   </div>
//                 </form>
//               </Form>
//             </Dialog.Body>
//           </Dialog.Content>
//         </Dialog.Portal>
//       </Dialog>
//     </>
//   );
// };
