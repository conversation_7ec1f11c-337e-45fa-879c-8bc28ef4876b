import { useGetCities } from '@muraadso/api/hooks';
import { CheckIcon, ChevronDownIcon, SearchIcon } from '@muraadso/icons';
import type { City } from '@muraadso/models/cities';
import { Button, Command, Popover, ScrollArea } from '@muraadso/ui';
import { cn } from '@muraadso/ui/utils';
import { useState } from 'react';

interface CitySelectorProps {
  countryId?: string;
  value?: string;
  onValueChange: (value: string | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export const CitySelector = ({
  countryId,
  value,
  onValueChange,
  placeholder = 'Select city...',
  disabled = false,
  className,
}: CitySelectorProps) => {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');

  const { cities, isPending } = useGetCities();

  const selectedCity = cities.find((city) => city.id === value);

  // Filter cities by countryId if provided
  const filteredCities = countryId
    ? cities.filter((city) => city.countryId === countryId)
    : cities;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <Popover.Trigger asChild>
        <Button
          variant="outline"
          aria-expanded={open}
          className={cn(
            'w-full justify-between',
            !selectedCity && 'text-muted-foreground',
            className,
          )}
          disabled={disabled}
        >
          {selectedCity ? selectedCity.name : placeholder}
          <ChevronDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </Popover.Trigger>
      <Popover.Content
        className="w-(--radix-popover-trigger-width) p-0"
        align="start"
      >
        <Command>
          <Command.Input
            placeholder="Search cities..."
            value={search}
            onValueChange={setSearch}
          />
          <Command.List>
            <ScrollArea className="h-60">
              {isPending ? (
                <Command.Loading>Loading cities...</Command.Loading>
              ) : filteredCities.length === 0 ? (
                <Command.Empty>No cities found.</Command.Empty>
              ) : (
                <Command.Group>
                  {filteredCities.map((city) => (
                    <Command.Item
                      key={city.id}
                      value={city.id}
                      onSelect={(currentValue) => {
                        onValueChange(
                          currentValue === value ? undefined : currentValue,
                        );
                        setOpen(false);
                      }}
                      className="flex items-center justify-between"
                    >
                      <div className="flex flex-col">
                        <span>{city.name}</span>
                        {city.country && (
                          <span className="text-muted-foreground text-xs">
                            {city?.country.name}
                          </span>
                        )}
                      </div>
                      <CheckIcon
                        className={cn(
                          'ml-2 h-4 w-4',
                          value === city.id ? 'opacity-100' : 'opacity-0',
                        )}
                      />
                    </Command.Item>
                  ))}
                </Command.Group>
              )}
            </ScrollArea>
          </Command.List>
        </Command>
      </Popover.Content>
    </Popover>
  );
};
