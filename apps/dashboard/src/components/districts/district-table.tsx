'use client';

import { DataTable } from '@/components/data-table';
import type { District } from '@muraadso/models/districts';
import { useDistrictColumns } from './district-columns';

interface DistrictTableProps {
  districts: District[];
}

export const DistrictTable = ({ districts }: DistrictTableProps) => {
  const columns = useDistrictColumns();

  return (
    <DataTable
      columns={columns}
      data={districts}
      enablePagination={false}
    />
  );
};
