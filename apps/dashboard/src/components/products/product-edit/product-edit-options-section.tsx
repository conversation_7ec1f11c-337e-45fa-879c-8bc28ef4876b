import { ChipInput } from '@/components/chip-input';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  useCreateProductOption,
  useDeleteProductOption,
  useUpdateProductOption,
} from '@muraadso/api/hooks';
import { MoreHorizontalIcon, PlusIcon } from '@muraadso/icons';
import type { OptionValue } from '@muraadso/models/option-values';
import type { Option } from '@muraadso/models/options';
import type { Product } from '@muraadso/models/products';
import {
  Button,
  Card,
  Dialog,
  DropdownMenu,
  Form,
  Input,
  Table,
} from '@muraadso/ui';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod/v4';

type ProductOptionsSectionProps = {
  product: Product;
};

export const ProductEditOptionsSection = ({
  product,
}: ProductOptionsSectionProps) => {
  const { mutate: deleteOption, isPending: isDeletingOption } =
    useDeleteProductOption();

  const [open, setOpen] = useState(false);
  const [editingOption, setEditingOption] = useState<
    | (Option & {
        values: OptionValue[];
      })
    | undefined
  >();

  return (
    <>
      <Card className="!gap-2 w-full rounded-lg shadow-black/2.5 shadow-sm">
        <Card.Header className="!pb-2 flex items-center justify-between">
          <div className="flex flex-col space-y-1">
            <Card.Title className="font-normal">Options</Card.Title>
            <Card.Description className="">
              Define the options for the product, e.g. color, size, etc.
            </Card.Description>
          </div>
          <Card.Action className="!self-center">
            <Button size="sm" variant="outline" onClick={() => setOpen(true)}>
              <PlusIcon className="me-2 size-4" />
              <span>New Option</span>
            </Button>
          </Card.Action>
        </Card.Header>
        <Card.Content>
          {product.options && product.options.length > 0 ? (
            <Table>
              <Table.Header className="border-border border-y">
                <Table.Row>
                  <Table.Head>Name</Table.Head>
                  <Table.Head>Values</Table.Head>
                  <Table.Head className="text-end">Actions</Table.Head>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {product.options.map((option) => (
                  <Table.Row key={option.id}>
                    <Table.Cell>{option.name}</Table.Cell>
                    <Table.Cell>
                      <div className="flex flex-wrap gap-1.5">
                        {option.values.map((value) => (
                          <span
                            key={value.id}
                            className="inline-flex items-center rounded-md bg-accent px-1.5 py-1 text-xs"
                          >
                            {value.value}
                          </span>
                        ))}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex justify-end">
                        <DropdownMenu>
                          <DropdownMenu.Trigger asChild>
                            <Button variant="ghost" className="size-8">
                              <MoreHorizontalIcon className="size-4 text-muted-foreground" />
                            </Button>
                          </DropdownMenu.Trigger>
                          <DropdownMenu.Content>
                            <DropdownMenu.Item
                              onSelect={async () => {
                                setEditingOption(option);
                                setOpen(true);
                              }}
                            >
                              Edit
                            </DropdownMenu.Item>
                            <DropdownMenu.Item
                              onSelect={() => {
                                deleteOption({
                                  productId: product.id,
                                  optionId: option.id,
                                });
                              }}
                              disabled={isDeletingOption}
                            >
                              Delete
                            </DropdownMenu.Item>
                          </DropdownMenu.Content>
                        </DropdownMenu>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          ) : (
            <div className="flex flex-col items-center justify-center rounded-lg border border-border border-dashed py-8">
              <p className="text-muted-foreground text-sm">No options yet!</p>
            </div>
          )}
        </Card.Content>
      </Card>

      <ProductEditOptionSectionForm
        open={open}
        onClose={() => {
          setOpen(false);
          setEditingOption(undefined);
        }}
        productId={product.id}
        payload={editingOption}
      />
    </>
  );
};

type ProductEditOptionSectionForm = {
  open: boolean;
  onClose: () => void;
  productId: string;
  payload?: Option & {
    values: OptionValue[];
  };
};

const ProductEditOptionSectionForm = ({
  open,
  onClose,
  productId,
  payload,
}: ProductEditOptionSectionForm) => {
  const { mutateAsync: createOption, isPending: isCreatingOption } =
    useCreateProductOption();
  const { mutateAsync: updateOption, isPending: isUpdatingOption } =
    useUpdateProductOption();

  const isLoading = isCreatingOption || isUpdatingOption;

  const form = useForm({
    resolver: zodResolver(
      z.object({
        name: z.string().min(1, 'Name is required'),
        values: z.string().min(1, 'Value is required').array().nonempty(),
      }),
    ),
    defaultValues: {
      name: '',
      values: [] as string[],
    },
  });

  const onSubmit = form.handleSubmit(async (data) => {
    if (!payload) {
      await createOption({
        productId: productId,
        payload: {
          productId,
          name: data.name,
          values: data.values.map((value) => ({ value })),
        },
      });
    } else {
      await updateOption({
        productId: productId,
        payload: {
          productId,
          id: payload.id,
          name: data.name,
          values: data.values.map((value) => ({ value })),
        },
      });
    }

    form.reset();
    onClose();
  });

  useEffect(() => {
    if (payload) {
      form.reset({
        name: payload.name,
        values: payload.values.map((v) => v.value),
      });
    } else {
      form.reset({
        name: '',
        values: [],
      });
    }
  }, [payload, form]);

  return (
    <Dialog
      open={open}
      onOpenChange={() => {
        form.reset();
        onClose();
      }}
    >
      <Dialog.Content>
        <Form {...form}>
          <form onSubmit={onSubmit}>
            <Dialog.Body
              title={payload ? 'Edit Option' : 'Create Option'}
              description={
                payload
                  ? 'Edit the option details'
                  : 'Add a new option for this product'
              }
            >
              <div className="grid gap-4">
                <Form.Field
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <Form.Item>
                      <Form.Label>Option Name</Form.Label>
                      <Form.Control>
                        <Input placeholder="e.g. Color, Size" {...field} />
                      </Form.Control>
                      <Form.Message />
                    </Form.Item>
                  )}
                />
                <Form.Field
                  control={form.control}
                  name="values"
                  render={({ field }) => (
                    <Form.Item>
                      <Form.Label>Option Values</Form.Label>
                      <Form.Control>
                        <ChipInput
                          name="values"
                          placeholder="e.g. Red, Blue, Green"
                          value={field.value}
                          onChange={field.onChange}
                        />
                      </Form.Control>
                      <Form.Description>
                        Press Enter or comma to add a value
                      </Form.Description>
                      <Form.Message />
                    </Form.Item>
                  )}
                />
              </div>
            </Dialog.Body>
            <Dialog.Footer>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button loading={isLoading} type="submit">
                {payload ? 'Update' : 'Create'}
              </Button>
            </Dialog.Footer>
          </form>
        </Form>
      </Dialog.Content>
    </Dialog>
  );
};
