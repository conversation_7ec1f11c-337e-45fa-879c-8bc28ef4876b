import {
  useGetBrands,
  useGetCategories,
  useUpdateProduct,
} from '@muraadso/api/hooks';
import type { Product, UpdateProduct } from '@muraadso/models/products';
import { Button, Form, Input, Select, Textarea } from '@muraadso/ui';
import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';

type ProductEditGeneralSectionProps = {
  product: Product;
};

export const ProductEditGeneralSection = ({
  product,
}: ProductEditGeneralSectionProps) => {
  const { categories } = useGetCategories();
  const { brands } = useGetBrands();
  const { mutate: updateProduct, isPending: isUpdating } = useUpdateProduct();

  const form = useForm<UpdateProduct>({
    defaultValues: {
      name: product?.name || '',
      slug: product?.slug || '',
      description: product?.description || '',
      categoryId: product?.categoryId || '',
      brandId: product?.brandId || '',
    },
  });

  const onSubmit = form.handleSubmit((values) => {
    updateProduct({
      id: product?.id,
      ...values,
    });
  });

  useEffect(() => {
    form.reset({
      name: product?.name,
      slug: product?.slug,
      description: product?.description,
      categoryId: product?.categoryId,
      brandId: product?.brandId,
    });
  }, [product, form.reset]);

  return (
    <Form {...form}>
      <form onSubmit={onSubmit}>
        <div className="grid w-full grid-cols-2 gap-4 py-4">
          <Form.Field
            control={form.control}
            name="name"
            render={({ field }) => (
              <Form.Item>
                <Form.Label>
                  <span>Name</span>
                  <span className="ms-1 text-destructive">*</span>
                </Form.Label>
                <Form.Control>
                  <Input placeholder="Name" className="" {...field} />
                </Form.Control>
                <Form.Message />
              </Form.Item>
            )}
          />

          <Form.Field
            control={form.control}
            name="slug"
            render={({ field }) => (
              <Form.Item>
                <Form.Label
                  tooltip={
                    <span>
                      A slug is a human-readable ID that must be <br />
                      unique. It&apos;s often used in URLs.
                    </span>
                  }
                >
                  <span>Slug</span>
                  <span className="ms-1 text-destructive">*</span>
                </Form.Label>
                <Form.Control>
                  <Input placeholder="Slug" className="" {...field} />
                </Form.Control>
                <Form.Message />
              </Form.Item>
            )}
          />

          <Form.Field
            control={form.control}
            name="categoryId"
            render={({ field }) => (
              <Form.Item>
                <Form.Label>
                  <span>Category</span>
                  <span className="ms-1 text-destructive">*</span>
                </Form.Label>
                <Form.Control>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <Select.Trigger className="">
                      <Select.Value placeholder="Select a category" />
                    </Select.Trigger>
                    <Select.Content>
                      {categories?.map((category) => (
                        <Select.Item key={category.id} value={category.id}>
                          {category.name}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                </Form.Control>
                <Form.Message />
              </Form.Item>
            )}
          />

          <Form.Field
            control={form.control}
            name="brandId"
            render={({ field }) => (
              <Form.Item>
                <Form.Label>
                  <span>Brand</span>
                  <span className="ms-1 text-destructive">*</span>
                </Form.Label>
                <Form.Control>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <Select.Trigger className="">
                      <Select.Value placeholder="Select a brand" />
                    </Select.Trigger>
                    <Select.Content>
                      {brands?.map((brand) => (
                        <Select.Item key={brand.id} value={brand.id}>
                          {brand.name}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                </Form.Control>
                <Form.Message />
              </Form.Item>
            )}
          />

          <Form.Field
            control={form.control}
            name="description"
            render={({ field }) => (
              <Form.Item className="col-span-2">
                <Form.Label optional>Description</Form.Label>
                <Form.Control>
                  <Textarea
                    placeholder="Description"
                    className="resize-none"
                    rows={4}
                    {...field}
                    value={field.value ?? ''}
                  />
                </Form.Control>
                <Form.Message />
              </Form.Item>
            )}
          />

          <div className="col-span-2 flex justify-end space-x-4 pt-2.5">
            <Button
              disabled={isUpdating || !form.formState.isDirty}
              onClick={() => form.reset()}
              variant="outline"
            >
              Reset
            </Button>
            <Button
              type="submit"
              disabled={isUpdating || !form.formState.isDirty}
              loading={isUpdating}
            >
              Save Changes
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
};
