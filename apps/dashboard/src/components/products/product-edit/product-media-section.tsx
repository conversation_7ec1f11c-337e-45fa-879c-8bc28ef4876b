import { ImagePicker } from '@/components/image-picker';
import { ImageIcon } from '@muraadso/icons';
import { Card } from '@muraadso/ui';

export const ProductMediaSection = () => {
  return (
    <Card className="w-88 rounded-lg shadow-black/2.5 shadow-sm">
      <Card.Header>
        <Card.Title className="inline-flex items-center space-x-2">
          <ImageIcon className="size-5 text-muted-foreground" />
          <span className="">Media</span>
        </Card.Title>
        <Card.Description>Upload and manage product images</Card.Description>
        <Card.Content className="px-0 pt-2">
          <ImagePicker value={[]} onChange={() => {}} />
        </Card.Content>
      </Card.Header>
    </Card>
  );
};
