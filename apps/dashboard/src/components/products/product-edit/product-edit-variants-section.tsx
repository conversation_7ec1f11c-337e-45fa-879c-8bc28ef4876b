import { getImageUrl } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  useCreateProductVariant,
  useDeleteProductVariant,
  useUpdateProductVariant,
} from '@muraadso/api/hooks';
import { MoreHorizontalIcon, PlusIcon } from '@muraadso/icons';
import type { Product } from '@muraadso/models/products';
import type { Variant } from '@muraadso/models/variants';
import {
  Button,
  Card,
  Dialog,
  DropdownMenu,
  Form,
  Input,
  Select,
  Table,
} from '@muraadso/ui';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod/v4';
import { ProductCreateVariantsImages } from '../product-create/product-create-variants/product-create-variants-images';

type ProductOptionsSectionProps = {
  product: Product;
};

export const ProductEditVariantsSection = ({
  product,
}: ProductOptionsSectionProps) => {
  const { mutate: deleteVariant, isPending: isDeletingVariant } =
    useDeleteProductVariant();

  const [open, setOpen] = useState(false);
  const [editingVariant, setEditingVariant] = useState<Variant | undefined>();

  return (
    <>
      <Card className="!gap-2 w-full rounded-lg shadow-black/2.5 shadow-sm">
        <Card.Header className="!pb-2 flex items-center justify-between">
          <div className="flex flex-col space-y-1">
            <Card.Title className="font-normal">Variants</Card.Title>
            <Card.Description className="">
              Manage product variants
            </Card.Description>
          </div>
          <Card.Action className="!self-center">
            <Button size="sm" variant="outline" onClick={() => setOpen(true)}>
              <PlusIcon className="me-2 size-4" />
              <span>New Variant</span>
            </Button>
          </Card.Action>
        </Card.Header>
        <Card.Content>
          {product.variants && product.variants.length > 0 ? (
            <Table>
              <Table.Header className="border-border border-y">
                <Table.Row>
                  <Table.Head>Name</Table.Head>
                  <Table.Head>SKU</Table.Head>
                  <Table.Head>Images</Table.Head>
                  <Table.Head>Stock</Table.Head>
                  <Table.Head>Price</Table.Head>
                  <Table.Head className="!rounded-sm !h-8" />
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {product.variants.map((variant) => (
                  <Table.Row key={variant.id} className="hover:bg-background">
                    <Table.Cell>{variant.name}</Table.Cell>
                    <Table.Cell>{variant.sku}</Table.Cell>
                    <Table.Cell>
                      {variant.images && variant.images.length > 0 ? (
                        <div className="-space-x-1 flex">
                          {variant.images.slice(0, 3).map((img) => (
                            <img
                              key={img.id}
                              src={getImageUrl(img.path)}
                              alt=""
                              className="size-6 rounded border border-background object-cover"
                            />
                          ))}
                          {variant.images.length > 3 && (
                            <div className="flex size-6 items-center justify-center rounded border border-background bg-muted text-xs">
                              +{variant.images.length - 3}
                            </div>
                          )}
                        </div>
                      ) : (
                        <span className="text-muted-foreground text-sm">
                          No images
                        </span>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      {/* TODO: Add stock quantity when available */}-
                    </Table.Cell>
                    <Table.Cell className="!h-8">
                      {variant.prices?.retailPrice
                        ? `$${variant.prices.retailPrice}`
                        : '-'}
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex justify-end">
                        <DropdownMenu>
                          <DropdownMenu.Trigger asChild>
                            <Button variant="ghost" className="size-8">
                              <MoreHorizontalIcon className="size-4 text-muted-foreground" />
                            </Button>
                          </DropdownMenu.Trigger>
                          <DropdownMenu.Content>
                            <DropdownMenu.Item
                              onSelect={() => {
                                setOpen(true);
                                setEditingVariant(variant);
                              }}
                            >
                              Edit
                            </DropdownMenu.Item>
                            <DropdownMenu.Item
                              onSelect={() =>
                                deleteVariant({
                                  productId: product.id,
                                  variantId: variant.id,
                                })
                              }
                              disabled={isDeletingVariant}
                            >
                              Delete
                            </DropdownMenu.Item>
                          </DropdownMenu.Content>
                        </DropdownMenu>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          ) : (
            <div className="flex flex-col items-center justify-center rounded-lg border border-border border-dashed py-8">
              <p className="text-muted-foreground text-sm">No variants yet!</p>
            </div>
          )}
        </Card.Content>
      </Card>

      <ProductEditOptionSectionForm
        open={open}
        onClose={() => {
          setOpen(false);
          setEditingVariant(undefined);
        }}
        product={product}
        payload={editingVariant}
        existingVariantsCount={product.variants.length}
      />
    </>
  );
};

type ProductEditOptionSectionForm = {
  open: boolean;
  onClose: () => void;
  product: Product;
  payload?: Variant;
  existingVariantsCount: number;
};

const ProductEditOptionSectionForm = ({
  open,
  onClose,
  product,
  payload,
  existingVariantsCount,
}: ProductEditOptionSectionForm) => {
  const { mutateAsync: createVariant, isPending: isCreatingVariant } =
    useCreateProductVariant();
  const { mutateAsync: updateVariant, isPending: isUpdatingVariant } =
    useUpdateProductVariant();

  const isLoading = isCreatingVariant || isUpdatingVariant;

  const form = useForm({
    resolver: zodResolver(
      z.object({
        name: z.string().nonempty('Name is required'),
        sku: z.string().nonempty('SKU is required'),
        retailPrice: z.number().positive(),
        tradeInPrice: z.number().positive(),
        consignmentPrice: z.number().positive(),
        buybackPrice: z.number().positive(),
        options: z.record(z.string(), z.string()),
        imageIndices: z.array(z.number()).optional(),
      }),
    ),
    defaultValues: {
      name: '',
      sku: '',
      retailPrice: 0,
      tradeInPrice: 0,
      consignmentPrice: 0,
      buybackPrice: 0,
      options: {} as Record<string, string>,
      imageIndices: [] as number[],
    },
  });

  const onSubmit = form.handleSubmit(async (data) => {
    if (!payload) {
      await createVariant({
        productId: product.id,
        payload: {
          name: data.name,
          sku: data.sku,
          options: data.options,
          prices: {
            retailPrice: data.retailPrice,
            tradeInPrice: data.tradeInPrice,
            consignmentPrice: data.consignmentPrice,
            buybackPrice: data.buybackPrice,
          },
          isDefault: false,
          shouldCreate: true,
          rank: existingVariantsCount + 1,
          imageIndices: data.imageIndices,
        },
      });
    } else {
      await updateVariant({
        productId: product.id,
        payload: {
          id: payload.id,
          name: data.name,
          sku: data.sku,
          options: data.options,
          prices: {
            retailPrice: data.retailPrice,
            tradeInPrice: data.tradeInPrice,
            consignmentPrice: data.consignmentPrice,
            buybackPrice: data.buybackPrice,
          },
          rank: existingVariantsCount + 1,
          imageIndices: data.imageIndices,
        },
      });
    }

    form.reset();
    onClose();
  });

  useEffect(() => {
    if (payload) {
      // Map existing variant images to indices
      const existingImageIndices =
        payload.variantImages
          ?.map((variantImage) => {
            return product.images.findIndex(
              (img) => img.id === variantImage.imageId,
            );
          })
          .filter((index) => index !== -1) || [];

      form.reset({
        name: payload.name || '',
        sku: payload.sku || '',
        retailPrice: payload.prices?.retailPrice || 0,
        tradeInPrice: payload.prices?.tradeInPrice || 0,
        consignmentPrice: payload.prices?.consignmentPrice || 0,
        buybackPrice: payload.prices?.buybackPrice || 0,
        options: product.options.reduce(
          (acc, option) => {
            acc[option.name] =
              option.values.find((val) =>
                payload.optionValues?.some((ov) => ov.id === val.id),
              )?.value || '';
            return acc;
          },
          {} as Record<string, string>,
        ),
        imageIndices: existingImageIndices,
      });
    } else {
      form.reset({
        name: '',
        sku: '',
        retailPrice: 0,
        tradeInPrice: 0,
        consignmentPrice: 0,
        buybackPrice: 0,
        options: {},
        imageIndices: [],
      });
    }
  }, [payload, form, product.options, product.images]);

  return (
    <Dialog
      open={open}
      onOpenChange={() => {
        form.reset();
        onClose();
      }}
    >
      <Dialog.Content>
        <Form {...form}>
          <form onSubmit={onSubmit}>
            <Dialog.Body
              title={`${payload ? 'Update' : 'Create'} Variant`}
              description={
                payload
                  ? 'Update the variant details'
                  : 'Add a new variant for this product'
              }
            >
              <div className="grid gap-4">
                {/* Images Section */}
                <Form.Field
                  control={form.control}
                  name="imageIndices"
                  render={({ field }) => (
                    <Form.Item>
                      <Form.Label>Images</Form.Label>
                      <Form.Control>
                        <div className="h-9 rounded-lg border border-input">
                          <ProductCreateVariantsImages
                            availableImages={product.images.map((img) => ({
                              id: img.id,
                              url: getImageUrl(img.path),
                              isExisting: true,
                            }))}
                            selectedImageIndices={field.value || []}
                            onSelectionChange={field.onChange}
                          />
                        </div>
                      </Form.Control>
                      <Form.Message />
                    </Form.Item>
                  )}
                />

                <Form.Field
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <Form.Item className="flex-1">
                      <Form.Label>Variant Name</Form.Label>
                      <Form.Control>
                        <Input placeholder="e.g. Red Small" {...field} />
                      </Form.Control>
                      <Form.Message />
                    </Form.Item>
                  )}
                />
                <div className="flex w-full items-center space-x-4">
                  <Form.Field
                    control={form.control}
                    name="sku"
                    render={({ field }) => (
                      <Form.Item className="flex-1">
                        <Form.Label>SKU</Form.Label>
                        <Form.Control>
                          <Input placeholder="e.g. PROD-RED-SM" {...field} />
                        </Form.Control>
                        <Form.Message />
                      </Form.Item>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {product.options.map((option) => (
                    <Form.Field
                      key={option.id}
                      control={form.control}
                      name={`options.${option.name}`}
                      render={({ field }) => (
                        <Form.Item>
                          <Form.Label>{option.name}</Form.Label>
                          <Form.Control>
                            <Select
                              value={field.value}
                              onValueChange={field.onChange}
                            >
                              <Select.Trigger className="mb-0">
                                <Select.Value
                                  placeholder={`Select ${option.name}`}
                                />
                              </Select.Trigger>
                              <Select.Content>
                                {option.values.map((value) => (
                                  <Select.Item
                                    key={value.id}
                                    value={value.value}
                                  >
                                    {value.value}
                                  </Select.Item>
                                ))}
                              </Select.Content>
                            </Select>
                          </Form.Control>
                          <Form.Message />
                        </Form.Item>
                      )}
                    />
                  ))}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <Form.Field
                    control={form.control}
                    name="retailPrice"
                    render={({ field }) => (
                      <Form.Item>
                        <Form.Label>Price - Retail</Form.Label>
                        <Form.Control>
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            {...field}
                            onChange={(e) =>
                              field.onChange(Number.parseFloat(e.target.value))
                            }
                          />
                        </Form.Control>
                        <Form.Message />
                      </Form.Item>
                    )}
                  />
                  <Form.Field
                    control={form.control}
                    name="tradeInPrice"
                    render={({ field }) => (
                      <Form.Item>
                        <Form.Label>Price - Isku Bedel</Form.Label>
                        <Form.Control>
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            {...field}
                            onChange={(e) =>
                              field.onChange(Number.parseFloat(e.target.value))
                            }
                          />
                        </Form.Control>
                        <Form.Message />
                      </Form.Item>
                    )}
                  />
                  <Form.Field
                    control={form.control}
                    name="consignmentPrice"
                    render={({ field }) => (
                      <Form.Item>
                        <Form.Label>Price - U iibin</Form.Label>
                        <Form.Control>
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            {...field}
                            onChange={(e) =>
                              field.onChange(Number.parseFloat(e.target.value))
                            }
                          />
                        </Form.Control>
                        <Form.Message />
                      </Form.Item>
                    )}
                  />
                  <Form.Field
                    control={form.control}
                    name="buybackPrice"
                    render={({ field }) => (
                      <Form.Item>
                        <Form.Label>Price - Ka iibsasho</Form.Label>
                        <Form.Control>
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            {...field}
                            onChange={(e) =>
                              field.onChange(Number.parseFloat(e.target.value))
                            }
                          />
                        </Form.Control>
                        <Form.Message />
                      </Form.Item>
                    )}
                  />
                </div>
              </div>
            </Dialog.Body>
            <Dialog.Footer>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  form.reset();
                  onClose();
                }}
              >
                Cancel
              </Button>
              <Button loading={isLoading} type="submit">
                {payload ? 'Update' : 'Create'}
              </Button>
            </Dialog.Footer>
          </form>
        </Form>
      </Dialog.Content>
    </Dialog>
  );
};
