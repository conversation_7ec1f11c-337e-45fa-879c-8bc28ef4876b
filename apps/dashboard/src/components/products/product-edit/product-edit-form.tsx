import { ProductEditVariantsSection } from '@/components/products/product-edit/product-edit-variants-section';
import { useGetProduct } from '@muraadso/api/hooks';
import { SpinnerIcon } from '@muraadso/icons';
import type { Product } from '@muraadso/models/products';
import { FocusModal } from '@muraadso/ui';
import React from 'react';
import { ProductEditGeneralSection } from './product-edit-general-section';
import { ProductEditMediaSection } from './product-edit-media-section';
import { ProductEditOptionsSection } from './product-edit-options-section';

type ProductEditFormProps = {
  open: boolean;
  onClose: (product?: Product) => void;
  payload: Product;
};

export const ProductEditForm = ({
  open,
  onClose,
  payload: { id: productId },
}: ProductEditFormProps) => {
  return (
    <FocusModal open={open} onOpenChange={() => onClose()}>
      <FocusModal.Content>
        <FocusModal.Header />
        <FocusModal.Body className="size-full overflow-y-auto">
          <ProductEditFormContent productId={productId} />
        </FocusModal.Body>
      </FocusModal.Content>
    </FocusModal>
  );
};

const ProductEditFormContent = ({ productId }: { productId: string }) => {
  const { product, isPending, isError } = useGetProduct(productId);

  console.log('rerender');
  console.log(product?.name);

  if (isPending) {
    return (
      <div className="flex justify-center py-12">
        <SpinnerIcon className="size-8 text-muted-foreground" />
      </div>
    );
  }

  if (isError || !product) {
    return (
      <div className="flex justify-center py-12">
        <p className="text-destructive">Failed to load product</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center p-16">
      <div className="flex w-full max-w-[720px] flex-col space-y-8">
        <div className="flex flex-col space-y-6 border-border border-b pb-4">
          <h4 className="font-medium text-lg">General</h4>
          <ProductEditGeneralSection product={product} />
        </div>

        <div className="flex flex-col space-y-6 border-border border-b pb-4">
          <h4 className="font-medium text-lg">Media</h4>
          <ProductEditMediaSection product={product} />
        </div>

        <div className="mt-4 flex flex-col space-y-6">
          <h4 className="font-medium text-lg">Variants</h4>
          <ProductEditOptionsSection product={product} />
          <ProductEditVariantsSection product={product} />
        </div>
      </div>
    </div>
  );
};
