import { type Image, ImagePicker } from '@/components/image-picker';
import { getImageUrl } from '@/lib/utils';
import {
  useDeleteProductImage,
  useUploadProductImages,
} from '@muraadso/api/hooks';
import type { Product } from '@muraadso/models/products';
import { Card } from '@muraadso/ui';
import React, { useState, useEffect } from 'react';

type ProductEditMediaSectionProps = {
  product: Product;
};

export const ProductEditMediaSection = ({
  product,
}: ProductEditMediaSectionProps) => {
  const [images, setImages] = useState<Image[]>([]);
  const { mutateAsync: uploadImages, isPending: isUploading } =
    useUploadProductImages();
  const { mutateAsync: deleteImage, isPending: isDeletingImage } =
    useDeleteProductImage();

  // Initialize images from product data
  useEffect(() => {
    if (product.images) {
      const existingImages: Image[] = product.images.map((img) => ({
        id: img.id,
        url: getImageUrl(img.path),
        isExisting: true,
      }));
      setImages(existingImages);
    }
  }, [product.images]);

  const handleImagesChange = async (newImages: Image[]) => {
    setImages(newImages);

    // Only handle new image uploads here
    // Deletions are handled by the onDeleteExisting callback
    const imagesToUpload = newImages.filter(
      (img) => img.file && !img.isExisting,
    );
    if (imagesToUpload.length > 0) {
      const files = imagesToUpload.map((img) => img.file!);
      await uploadImages({ id: product.id, images: files });
    }
  };

  return (
    <Card className="w-full rounded-lg shadow-black/2.5 shadow-sm">
      <Card.Header>
        <div className="flex flex-col space-y-1">
          <Card.Title className="font-normal">Media</Card.Title>
          <Card.Description>
            Manage product images and media files
          </Card.Description>
        </div>
      </Card.Header>
      <Card.Content>
        <ImagePicker
          multiple
          value={images}
          onChange={handleImagesChange}
          onDeleteExisting={async (imageToDelete) => {
            try {
              await deleteImage({
                id: product.id,
                imageId: imageToDelete.id,
              });
              return true;
            } catch {
              return false;
            }
          }}
        />
      </Card.Content>
    </Card>
  );
};
