'use client';

import { SortIcon } from '@muraadso/icons';
import { Select } from '@muraadso/ui';
import { useQueryState } from 'nuqs';

export function ProductSort() {
  const [sortOrder, setSortOrder] = useQueryState('sortOrder', {
    defaultValue: 'desc',
  });

  return (
    <Select
      value={sortOrder}
      onValueChange={(value) => setSortOrder(value as 'asc' | 'desc')}
    >
      <Select.Trigger className="w-[156px]">
        <div className="flex flex-1 items-center justify-start font-medium text-sm">
          <SortIcon className="mr-2 size-4 text-muted-foreground" />
          <Select.Value className="text-start" placeholder="Sort by" />
        </div>
      </Select.Trigger>
      <Select.Content>
        <Select.Item value="asc">Newest first</Select.Item>
        <Select.Item value="desc">Oldest first</Select.Item>
      </Select.Content>
    </Select>
  );
}
