'use client';

import { FilterIcon } from '@muraadso/icons';
import { Button, Label, Popover, Select } from '@muraadso/ui';
import { useQueryState } from 'nuqs';

export const ProductFilters = () => {
  const [categoryId, setCategoryId] = useQueryState('categoryId');
  const [brandId, setBrandId] = useQueryState('brandId');
  const [isPublished, setIsPublished] = useQueryState('isPublished');

  // Reset all filters
  const resetFilters = () => {
    setCategoryId(null);
    setBrandId(null);
    setIsPublished(null);
  };

  return (
    <Popover>
      <Popover.Trigger asChild>
        <Button variant="outline" className="gap-2">
          <FilterIcon className="h-4 w-4" />
          Filters
        </Button>
      </Popover.Trigger>
      <Popover.Content className="w-80">
        <div className="space-y-4">
          <div className="-mx-4 -mt-4 rounded-t-md bg-accent px-4 py-3">
            <h4 className="font-medium">Filter Products</h4>
          </div>

          <div className="flex flex-col space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select
              onValueChange={(value) =>
                setCategoryId(value === 'all' ? null : value)
              }
              value={categoryId || 'all'}
            >
              <Select.Trigger id="category">
                <Select.Value placeholder="All Categories" />
              </Select.Trigger>
              <Select.Content>
                <Select.Item value="all">All Categories</Select.Item>
                {/* Add categories dynamically here */}
              </Select.Content>
            </Select>
          </div>

          <div className="flex flex-col space-y-2">
            <Label htmlFor="brand">Brand</Label>
            <Select
              onValueChange={(value) =>
                setBrandId(value === 'all' ? null : value)
              }
              value={brandId || 'all'}
            >
              <Select.Trigger id="brand">
                <Select.Value placeholder="All Brands" />
              </Select.Trigger>
              <Select.Content>
                <Select.Item value="all">All Brands</Select.Item>
                {/* Add brands dynamically here */}
              </Select.Content>
            </Select>
          </div>

          <div className="flex flex-col space-y-2">
            <Label htmlFor="published">Status</Label>
            <Select
              onValueChange={(value) => {
                if (value === 'all') {
                  setIsPublished(null);
                } else {
                  setIsPublished(value);
                }
              }}
              value={isPublished || 'all'}
            >
              <Select.Trigger id="published">
                <Select.Value placeholder="All Status" />
              </Select.Trigger>
              <Select.Content>
                <Select.Item value="all">All Status</Select.Item>
                <Select.Item value="true">Published</Select.Item>
                <Select.Item value="false">Unpublished</Select.Item>
              </Select.Content>
            </Select>
          </div>

          <div className="flex w-full items-center space-x-4">
            <Button variant="outline" onClick={resetFilters} className="w-full">
              Reset
            </Button>
            <Button onClick={resetFilters} className="w-full">
              Apply
            </Button>
          </div>
        </div>
      </Popover.Content>
    </Popover>
  );
};
