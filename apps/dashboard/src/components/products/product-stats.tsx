'use client';

import { useGetProductStats } from '@muraadso/api/hooks';
import { Skeleton } from '@muraadso/ui';

export const ProductStats = () => {
  const { stats, isPending, error } = useGetProductStats();

  if (isPending) {
    return (
      <div className="mb-8 grid w-full grid-cols-4 gap-8 divide-x divide-border border-border border-y border-dashed py-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="flex w-full flex-col space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-8 w-16" />
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="mb-8 grid w-full grid-cols-4 gap-8 divide-x divide-border border-border border-y border-dashed py-6">
        <div className="col-span-4 text-center text-destructive text-sm">
          Failed to load item stats
        </div>
      </div>
    );
  }

  return (
    <div className="mb-8 grid w-full grid-cols-4 gap-8 divide-x divide-border border-border border-y border-dashed py-6">
      <div className="flex w-full flex-col space-y-2">
        <p className="font-medium text-muted-foreground text-sm">All Items</p>
        <div className="flex items-center space-x-2">
          <span className="font-medium text-2xl">
            {stats.totalProducts.toLocaleString()}
          </span>
          <span className="text-green-600 text-xs">
            +{stats.productsThisWeek}
          </span>
          <span className="text-muted-foreground text-xs">this week</span>
        </div>
      </div>

      <div className="flex w-full flex-col space-y-2">
        <p className="font-medium text-muted-foreground text-sm">Published</p>
        <div className="flex items-center space-x-2">
          <span className="font-medium text-2xl">
            {stats.publishedProducts.toLocaleString()}
          </span>
          <span className="text-green-600 text-xs">
            +{stats.publishedThisWeek}
          </span>
          <span className="text-muted-foreground text-xs">this week</span>
        </div>
      </div>

      <div className="flex w-full flex-col space-y-2">
        <p className="font-medium text-muted-foreground text-sm">Draft</p>
        <div className="flex items-center space-x-2">
          <span className="font-medium text-2xl">
            {stats.draftProducts.toLocaleString()}
          </span>
          <span className="text-green-600 text-xs">+{stats.draftThisWeek}</span>
          <span className="text-muted-foreground text-xs">this week</span>
        </div>
      </div>

      <div className="flex w-full flex-col space-y-2">
        <p className="font-medium text-muted-foreground text-sm">Trashed</p>
        <div className="flex items-center space-x-2">
          <span className="font-medium text-2xl">
            {stats.trashedProducts.toLocaleString()}
          </span>
          <span className="text-muted-foreground text-xs">
            {stats.trashedProducts > 0 ? 'Requires attention' : 'All clean'}
          </span>
        </div>
      </div>
    </div>
  );
};
