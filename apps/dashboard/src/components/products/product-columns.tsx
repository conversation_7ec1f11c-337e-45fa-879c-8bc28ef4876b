'use client';

import { useModal } from '@/components/modal';
import { getImageUrl } from '@/lib/utils';
import { useDeleteProduct } from '@muraadso/api/hooks';
import { ImageIcon, MoreHorizontalIcon } from '@muraadso/icons';
import type { Product } from '@muraadso/models/products';
import { Avatar, Button, Checkbox, DropdownMenu } from '@muraadso/ui';
import type { ColumnDef } from '@tanstack/react-table';
import { formatDate } from 'date-fns';

export const useProductColumns = (): ColumnDef<Product>[] => {
  const { mutateAsync: deleteProduct } = useDeleteProduct();
  const modal = useModal();

  return [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
    },
    {
      header: 'Product',
      cell: ({ row }) => (
        <div className="flex items-start space-x-2">
          <Avatar className="size-8 rounded-sm">
            <Avatar.Image
              src={
                row.original.images[0]?.path
                  ? getImageUrl(row.original.images[0]?.path)
                  : undefined
              }
              alt=""
            />
            <Avatar.Fallback className="rounded-sm">
              <ImageIcon className="size-4 text-muted-foreground" />
            </Avatar.Fallback>
          </Avatar>
          <div className="flex flex-col space-y-1">
            <span>
              {row.original.brand.name} {row.original?.name ?? '-'}
            </span>
            <span className="text-muted-foreground text-xs">
              {row.original.category.name}
            </span>
          </div>
        </div>
      ),
    },
    {
      header: 'Slug',
      cell: ({ row }) => <div>/{row.original.slug}</div>,
    },
    {
      header: 'Variants',
      cell: ({ row }) => <div>{row.original.variants.length} Variants</div>,
    },
    {
      header: 'Stock',
      cell: ({ row }) => (
        <div>
          {/*{row.original.variants.reduce((acc, v) => {*/}
          {/*  return acc + v.stockQty;*/}
          {/*}, 0)}*/}
        </div>
      ),
    },
    {
      header: 'Created At',
      cell: ({ row }) => (
        <div>{formatDate(row.original.createdAt, 'dd MMM, yyyy') ?? '-'}</div>
      ),
    },
    {
      id: 'actions',
      enableHiding: false,
      cell: ({ row }) => {
        const { id } = row.original;
        return (
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontalIcon className="size-5 text-muted-foreground opacity-0 group-hover:opacity-100" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content align="end">
              <DropdownMenu.Label>Actions</DropdownMenu.Label>
              <DropdownMenu.Item
                onClick={() => modal.open('products', row.original)}
              >
                Edit
              </DropdownMenu.Item>
              <DropdownMenu.Separator />
              <DropdownMenu.Item
                className="text-destructive"
                onClick={async () => {
                  try {
                    await deleteProduct(id);
                  } catch (_) {}
                }}
              >
                Delete
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu>
        );
      },
    },
  ];
};
