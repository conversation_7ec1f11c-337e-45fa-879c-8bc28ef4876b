import { useGetProducts, usePagination } from '@muraadso/api/hooks';
import { useQueryState } from 'nuqs';

export const useProducts = () => {
  const { q, page, pageSize } = usePagination();

  const [categoryId] = useQueryState('categoryId');
  const [brandId] = useQueryState('brandId');
  const [isPublished] = useQueryState('isPublished');
  const [sortOrder] = useQueryState('sortOrder', { defaultValue: 'desc' });

  const publishedValue =
    isPublished === 'true' ? true : isPublished === 'false' ? false : undefined;

  const params = {
    page,
    pageSize,
    q: q || undefined,
    categoryId: categoryId || undefined,
    brandId: brandId || undefined,
    isPublished: publishedValue,
    sortOrder: sortOrder as 'asc' | 'desc',
  };

  return useGetProducts(params);
};
