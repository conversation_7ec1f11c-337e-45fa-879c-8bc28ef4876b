import type { Image } from '@/components/image-picker';
import type { CreateProduct } from '@muraadso/models/products';
import type { CreateVariant } from '@muraadso/models/variants';
import { Button, Form, Input } from '@muraadso/ui';
import {
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import React, { useMemo } from 'react';
import { type UseFormReturn, useWatch } from 'react-hook-form';
import { ProductCreateVariantsImages } from './product-create-variants-images';

type CreateVariantWithOptionsAndIndex = CreateVariant & {
  originalIndex: number;
};

type ProductCreateVariantsFormProps = {
  form: UseFormReturn<CreateProduct>;
  images: Image[];
};

export const ProductCreateVariantsForm = ({
  form,
  images,
}: ProductCreateVariantsFormProps) => {
  const variants = useWatch({
    control: form.control,
    name: 'variants',
    defaultValue: [],
  });

  const options = useWatch({
    control: form.control,
    name: 'options',
    defaultValue: [],
  });

  const variantData = useMemo(() => {
    const ret: CreateVariantWithOptionsAndIndex[] = [];

    variants.forEach((v, i) => {
      if (v.shouldCreate) {
        ret.push({ ...v, originalIndex: i });
      }
    });

    return ret;
  }, [variants]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const columns: ColumnDef<CreateVariantWithOptionsAndIndex>[] = useMemo(
    () => [
      {
        id: 'options',
        header: () => (
          <div className="w-56">{options.map((o) => o.name).join(' / ')}</div>
        ),
        cell: ({ row }) => (
          <div className="flex h-10 items-center truncate bg-muted/50 px-4 text-muted-foreground">
            {options.map((o) => row.original.options[o.name]).join(' / ')}
          </div>
        ),
      },
      {
        id: 'images',
        header: () => <div className="w-48">Images</div>,
        cell: ({ row }) => (
          <div>
            <Form.Field
              control={form.control}
              name={`variants.${row.original.originalIndex}.imageIndices`}
              render={({ field }) => (
                <ProductCreateVariantsImages
                  availableImages={images}
                  selectedImageIndices={field.value || []}
                  onSelectionChange={field.onChange}
                />
              )}
            />
          </div>
        ),
      },
      {
        id: 'name',
        header: () => <div className="w-40">Name</div>,
        cell: ({ row }) => (
          <Form.Field
            control={form.control}
            name={`variants.${row.original.originalIndex}.name`}
            render={({ field }) => (
              <div className="px-4 text-muted-foreground">{field.value}</div>
            )}
          />
        ),
      },
      {
        id: 'sku',
        header: () => <div className="w-40">SKU</div>,
        cell: ({ row }) => (
          <div className="border-2 border-transparent focus-within:border-primary focus-within:bg-primary/5">
            <Form.Field
              control={form.control}
              name={`variants.${row.original.originalIndex}.sku`}
              render={({ field }) => (
                <Input
                  className="h-9 w-full rounded-none border-0 px-2 shadow-none focus-visible:ring-0"
                  containerClassName="w-full"
                  {...field}
                  value={field.value || ''}
                />
              )}
            />
          </div>
        ),
      },
      {
        id: 'retail',
        header: () => <div className="w-40">Price - Retail</div>,
        cell: ({ row }) => (
          <div className="border-2 border-transparent focus-within:border-primary focus-within:bg-primary/5">
            <Form.Field
              control={form.control}
              name={`variants.${row.original.originalIndex}.prices.retailPrice`}
              render={({ field }) => (
                <Input
                  type="number"
                  leading={<span>$</span>}
                  className="h-9 w-full rounded-none border-0 px-2 text-end shadow-none focus-visible:ring-0"
                  containerClassName="w-full"
                  {...field}
                  onChange={(e) =>
                    field.onChange(
                      e.target.value === ''
                        ? undefined
                        : Number(e.target.value),
                    )
                  }
                />
              )}
            />
          </div>
        ),
      },
      {
        id: 'isku-bedel',
        header: () => <div className="w-40">Price - Isku bedel</div>,
        cell: ({ row }) => (
          <div className="border-2 border-transparent focus-within:border-primary focus-within:bg-primary/5">
            <Form.Field
              control={form.control}
              name={`variants.${row.original.originalIndex}.prices.tradeInPrice`}
              render={({ field }) => (
                <Input
                  type="number"
                  leading={<span>$</span>}
                  className="h-9 w-full rounded-none border-0 px-2 text-end shadow-none focus-visible:ring-0"
                  containerClassName="w-full"
                  {...field}
                  onChange={(e) =>
                    field.onChange(
                      e.target.value === ''
                        ? undefined
                        : Number(e.target.value),
                    )
                  }
                />
              )}
            />
          </div>
        ),
      },
      {
        id: 'ka-iibsi',
        header: () => <div className="w-40">Price - Ka iibsi</div>,
        cell: ({ row }) => (
          <div className="border-2 border-transparent focus-within:border-primary focus-within:bg-primary/5">
            <Form.Field
              control={form.control}
              name={`variants.${row.original.originalIndex}.prices.buybackPrice`}
              render={({ field }) => (
                <Input
                  type="number"
                  leading={<span>$</span>}
                  className="h-9 w-full rounded-none border-0 px-2 text-end shadow-none focus-visible:ring-0"
                  containerClassName="w-full"
                  {...field}
                  onChange={(e) =>
                    field.onChange(
                      e.target.value === ''
                        ? undefined
                        : Number(e.target.value),
                    )
                  }
                />
              )}
            />
          </div>
        ),
      },
      {
        id: 'u-iibin',
        header: () => <div className="w-40">Price - U iibin</div>,
        cell: ({ row }) => (
          <div className="border-2 border-transparent focus-within:border-primary focus-within:bg-primary/5">
            <Form.Field
              control={form.control}
              name={`variants.${row.original.originalIndex}.prices.consignmentPrice`}
              render={({ field }) => (
                <Input
                  type="number"
                  leading={<span>$</span>}
                  className="h-9 w-full rounded-none border-0 px-2 text-end shadow-none focus-visible:ring-0"
                  containerClassName="w-full"
                  {...field}
                  onChange={(e) =>
                    field.onChange(
                      e.target.value === ''
                        ? undefined
                        : Number(e.target.value),
                    )
                  }
                />
              )}
            />
          </div>
        ),
      },
    ],
    [images],
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const tableConfig = useMemo(
    () => ({
      columns,
      data: variantData,
      getCoreRowModel: getCoreRowModel(),
    }),
    [variantData],
  );

  const table = useReactTable(tableConfig);

  return (
    <div className="flex size-full flex-col divide-y divide-border overflow-hidden py-4">
      <div className="flex w-full justify-between px-4 pb-4">
        <Button type="button" variant="outline">
          Calculate Prices
        </Button>
      </div>

      <div className="w-full flex-1 overflow-x-auto">
        <table className="min-w-full">
          <thead className="border-border border-b bg-background">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr
                key={headerGroup.id}
                className="divide-x divide-border bg-background"
              >
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    className="whitespace-nowrap px-4 py-3 text-start font-medium text-foreground/70 text-sm"
                  >
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext(),
                    )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className="divide-y divide-border border-border border-b">
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <tr
                  key={row.id}
                  className="divide-x divide-border px-0 hover:bg-background"
                >
                  {row.getVisibleCells().map((cell) => (
                    <td key={cell.id} className="text-sm">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </td>
                  ))}
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={columns.length}
                  className="h-24 text-center text-muted-foreground text-sm"
                >
                  No variants
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};
