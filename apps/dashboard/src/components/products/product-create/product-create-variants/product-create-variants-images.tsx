import type { Image } from '@/components/image-picker';
import { CheckIcon } from '@muraadso/icons';
import { Button, Popover } from '@muraadso/ui';
import { cn } from '@muraadso/ui/utils';
import React, { useState } from 'react';

type VariantImageSelectorProps = {
  availableImages: Image[];
  selectedImageIndices: number[];
  onSelectionChange: (imageIndices: number[]) => void;
  disabled?: boolean;
};

export const ProductCreateVariantsImages = ({
  availableImages,
  selectedImageIndices,
  onSelectionChange,
  disabled = false,
}: VariantImageSelectorProps) => {
  const [open, setOpen] = useState(false);

  const toggleImageSelection = (imageIndex: number) => {
    const isSelected = selectedImageIndices.includes(imageIndex);
    if (isSelected) {
      onSelectionChange(
        selectedImageIndices.filter((idx) => idx !== imageIndex),
      );
    } else {
      onSelectionChange([...selectedImageIndices, imageIndex]);
    }
  };

  const selectedImages = selectedImageIndices
    .map((idx) => availableImages[idx])
    .filter(Boolean);

  if (availableImages.length === 0) {
    return (
      <div className="flex h-9 items-center px-2 text-muted-foreground text-sm">
        No images available
      </div>
    );
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <Popover.Trigger asChild>
        <Button
          variant="ghost"
          disabled={disabled}
          className="h-full w-full justify-start rounded-none"
        >
          {selectedImages.length === 0 ? (
            <span className="text-start font-normal text-muted-foreground">
              Select images
            </span>
          ) : (
            <div className="flex items-center gap-2">
              <div className="-space-x-1 flex">
                {selectedImages.slice(0, 3).map((img) => (
                  <img
                    key={img?.id}
                    src={img?.url}
                    alt=""
                    className="size-5 rounded border border-background object-cover"
                  />
                ))}
              </div>
              <span className="text-sm">
                {selectedImages.length} image
                {selectedImages.length !== 1 ? 's' : ''}
              </span>
            </div>
          )}
        </Button>
      </Popover.Trigger>
      <Popover.Content className="w-80 p-0" align="start">
        <div className="p-3">
          <h4 className="font-medium text-sm">Select Images</h4>
          <p className="text-muted-foreground text-xs">
            Choose images for this variant
          </p>
        </div>
        <div className="max-h-60 overflow-y-auto border-border border-t">
          <div className="grid grid-cols-4 gap-2 p-3">
            {availableImages.map((image, index) => {
              const isSelected = selectedImageIndices.includes(index);
              return (
                <button
                  key={image.id}
                  type="button"
                  onClick={() => toggleImageSelection(index)}
                  className={cn(
                    'relative aspect-square overflow-hidden rounded border-2 transition-colors',
                    isSelected
                      ? 'border-primary'
                      : 'border-border hover:border-muted-foreground',
                  )}
                >
                  <img
                    src={image.url}
                    alt=""
                    className="size-full object-cover"
                  />
                  {isSelected && (
                    <div className="absolute inset-0 flex items-center justify-center bg-primary/20">
                      <div className="flex size-6 items-center justify-center rounded-full bg-primary text-primary-foreground">
                        <CheckIcon className="size-3" />
                      </div>
                    </div>
                  )}
                </button>
              );
            })}
          </div>
        </div>
        {selectedImages.length > 0 && (
          <div className="border-border border-t p-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSelectionChange([])}
              className="w-full"
            >
              Clear Selection
            </Button>
          </div>
        )}
      </Popover.Content>
    </Popover>
  );
};
