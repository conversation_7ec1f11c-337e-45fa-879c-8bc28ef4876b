import type { Image } from '@/components/image-picker';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCreateProductWithImages } from '@muraadso/api/hooks';
import {
  type CreateProduct,
  type Product,
  createProductSchema,
} from '@muraadso/models/products';
import {
  Button,
  FocusModal,
  Form,
  ProgressTabs,
  type ProgressTabsStatus,
  VisuallyHidden,
} from '@muraadso/ui';
import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { ProductCreateDetailsForm } from './product-create-details/product-create-details-form';
import { ProductCreateVariantsForm } from './product-create-variants/product-create-variants-form';

enum Tab {
  DETAILS = 'details',
  VARIANTS = 'variants',
}

type TabState = Record<Tab, ProgressTabsStatus>;

export const ProductCreateForm = ({
  open,
  onClose,
}: {
  open: boolean;
  onClose: (product?: Product) => void;
}) => {
  const [tab, setTab] = useState<Tab>(Tab.DETAILS);
  const [tabState, setTabState] = useState<TabState>({
    [Tab.DETAILS]: 'in-progress',
    [Tab.VARIANTS]: 'not-started',
  });

  const {
    mutateAsync: createProductWithImages,
    isPending: isCreatingWithImages,
  } = useCreateProductWithImages();

  const [images, setImages] = useState<Image[]>([]);

  const form = useForm<CreateProduct>({
    resolver: zodResolver(createProductSchema),
    defaultValues: {
      name: '',
      slug: '',
      description: '',
      categoryId: '',
      brandId: '',
      isPublished: true,
      variantsEnabled: false,
      options: [
        {
          name: 'Default option',
          values: [
            {
              value: 'Default option value',
            },
          ],
        },
      ],
      variants: [
        {
          name: 'Default variant',
          sku: '',
          rank: 0,
          isDefault: true,
          shouldCreate: true,
          options: {
            'Default option': 'Default option value',
          },
          prices: {
            retailPrice: 0,
            buybackPrice: 0,
            consignmentPrice: 0,
            tradeInPrice: 0,
          },
        },
      ],
    },
  });

  const onSubmit = async (data: CreateProduct) => {
    try {
      // Prepare FormData with product data and files
      const formData = new FormData();

      // Add product data as JSON
      const productData = { ...data };
      formData.append('productData', JSON.stringify(productData));

      // Add image files in order
      const newImages = images.filter((img) => img.file && !img.isExisting);
      newImages.forEach((img, _) => {
        formData.append('images', img.file!);
      });

      // Create product with images and variant images in one call
      const result = await createProductWithImages(formData);

      form.reset();
      onClose(result);
    } catch (_) {}
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    const currentState = { ...tabState };
    if (tab === Tab.DETAILS) {
      currentState[Tab.DETAILS] = 'in-progress';
    }
    if (tab === Tab.VARIANTS) {
      currentState[Tab.DETAILS] = 'completed';
      currentState[Tab.VARIANTS] = 'in-progress';
    }

    setTabState({ ...currentState });
  }, [tab]);

  return (
    <FocusModal open={open} onOpenChange={() => onClose()}>
      <FocusModal.Portal>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FocusModal.Content>
              <ProgressTabs
                value={tab}
                onValueChange={async (tab) => {
                  const valid = await form.trigger();
                  if (valid) {
                    setTab(tab as Tab);
                  }
                }}
                className="flex h-full flex-col overflow-hidden"
              >
                <FocusModal.Header>
                  <VisuallyHidden>
                    <FocusModal.Title>New Product</FocusModal.Title>
                    <FocusModal.Description />
                  </VisuallyHidden>
                  <div className="-my-2 w-full border-border border-l">
                    <ProgressTabs.List className="flex w-full items-center justify-start">
                      <ProgressTabs.Trigger
                        value={Tab.DETAILS}
                        status={tabState[Tab.DETAILS]}
                        className="max-w-[200px] truncate"
                      >
                        Details
                      </ProgressTabs.Trigger>
                      <ProgressTabs.Trigger
                        value={Tab.VARIANTS}
                        status={tabState[Tab.VARIANTS]}
                        className="max-w-[200px] truncate"
                      >
                        Variants
                      </ProgressTabs.Trigger>
                    </ProgressTabs.List>
                  </div>
                </FocusModal.Header>
                <FocusModal.Body className="size-full overflow-y-hidden">
                  <ProgressTabs.Content
                    value="details"
                    className="size-full overflow-y-auto"
                  >
                    <ProductCreateDetailsForm
                      form={form}
                      images={images}
                      setImages={setImages}
                    />
                  </ProgressTabs.Content>
                  <ProgressTabs.Content
                    className="size-full overflow-y-auto"
                    value="variants"
                  >
                    <ProductCreateVariantsForm form={form} images={images} />
                  </ProgressTabs.Content>
                </FocusModal.Body>
              </ProgressTabs>
              <FocusModal.Footer>
                <Button
                  type="button"
                  onClick={() => onClose()}
                  variant="outline"
                >
                  Cancel
                </Button>
                <PrimaryButton
                  tab={tab}
                  loading={isCreatingWithImages}
                  onNext={async () => {
                    const valid = await form.trigger();
                    if (!valid) return;
                    if (tab !== 'variants') {
                      setTab(Tab.VARIANTS);
                      setTabState({
                        ...tabState,
                        [Tab.VARIANTS]: 'in-progress',
                      });
                    }
                  }}
                />
              </FocusModal.Footer>
            </FocusModal.Content>
          </form>
        </Form>
      </FocusModal.Portal>
    </FocusModal>
  );
};

const PrimaryButton = ({
  tab,
  loading,
  onNext,
}: { tab: Tab; loading: boolean; onNext: () => void }) => {
  if (tab === Tab.VARIANTS) {
    return (
      <Button key="submit-button" loading={loading} type="submit">
        Publish
      </Button>
    );
  }

  return (
    <Button type="button" onClick={onNext}>
      Continue
    </Button>
  );
};
