import { type Image, ImagePicker } from '@/components/image-picker';
import { Label } from '@muraadso/ui';

type ProductCreateDetailsMediaSectionProps = {
  images: Image[];
  setImages: (images: Image[]) => void;
};

export const ProductCreateDetailsMediaSection = ({
  images,
  setImages,
}: ProductCreateDetailsMediaSectionProps) => {
  return (
    <div className="mt-5 grid gap-3">
      <Label htmlFor="image" className="leading-none">
        Media{' '}
        <span className="ml-1 font-normal text-muted-foreground">
          (Optional)
        </span>
      </Label>
      <ImagePicker multiple value={images} onChange={setImages} />
    </div>
  );
};
