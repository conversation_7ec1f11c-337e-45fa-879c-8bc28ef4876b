import { ChipInput } from '@/components/chip-input';
import { SortableList } from '@/components/sortable-list';
import { SwitchBox } from '@/components/switch-box';
import { CloseIcon } from '@muraadso/icons';
import type { CreateProduct } from '@muraadso/models/products';
import type { CreateVariant } from '@muraadso/models/variants';
import { Alert, Button, Checkbox, Form, Input, Tip } from '@muraadso/ui';
import { cn } from '@muraadso/ui/utils';
import React from 'react';
import {
  Controller,
  type FieldArrayWithId,
  type UseFormReturn,
  useFieldArray,
  useWatch,
} from 'react-hook-form';

type ProductCreateDetailsVariantsSectionProps = {
  form: UseFormReturn<CreateProduct>;
};

const getPermutations = (
  data: { name: string; values: { value: string }[] }[],
): { [key: string]: string }[] => {
  const first = data[0];
  if (!first || data.length === 0) {
    return [];
  }

  if (first && data.length === 1) {
    return first.values.map((value) => ({ [first.name]: value.value }));
  }

  const rest = data.slice(1);

  return first.values.flatMap((value) => {
    return getPermutations(rest).map((permutation) => {
      return {
        [first.name]: value.value,
        ...permutation,
      };
    });
  });
};

const getVariantName = (options: Record<string, string>) => {
  return Object.values(options).join(' / ');
};

export const ProductCreateDetailsVariantsSection = ({
  form,
}: ProductCreateDetailsVariantsSectionProps) => {
  const options = useFieldArray({
    control: form.control,
    name: 'options',
  });

  const watchedOptions = useWatch({
    control: form.control,
    name: 'options',
    defaultValue: [],
  });

  const variants = useFieldArray({
    control: form.control,
    name: 'variants',
  });

  const watchedVariants = useWatch({
    control: form.control,
    name: 'variants',
    defaultValue: [],
  });

  const watchedAreVariantsEnabled = useWatch({
    control: form.control,
    name: 'variantsEnabled',
    defaultValue: false,
  });

  const showInvalidOptionsMessage = !!form.formState.errors.options?.length;
  const showInvalidVariantsMessage =
    form.formState.errors.variants?.root?.message === 'invalid_length';

  const createDefaultOptionAndVariant = () => {
    form.setValue('options', [
      {
        name: 'Default option',
        values: [
          {
            value: 'Default option value',
          },
        ],
      },
    ]);
  };

  const handleRemoveOption = (index: number) => {
    if (index === 0) {
      return;
    }

    options.remove(index);

    const newOptions = [...watchedOptions];
    newOptions.splice(index, 1);

    const permutations = getPermutations(newOptions);
    const oldVariants = [...watchedVariants];

    const findMatchingPermutation = (options: Record<string, string>) => {
      return permutations.find((permutation) =>
        Object.keys(options).every((key) => options[key] === permutation[key]),
      );
    };

    const newVariants = oldVariants.reduce(
      (variants, variant) => {
        const match = findMatchingPermutation(variant.options);

        if (match) {
          variants.push({
            ...variant,
            name: getVariantName(match),
            options: match,
          });
        }

        return variants;
      },
      [] as typeof oldVariants,
    );

    const usedPermutations = new Set(
      newVariants.map((variant) => variant.options),
    );
    const unusedPermutations = permutations.filter(
      (permutation) => !usedPermutations.has(permutation),
    );

    for (const permutation of unusedPermutations) {
      newVariants.push({
        name: getVariantName(permutation),
        sku: '',
        options: permutation,
        isDefault: false,
        shouldCreate: false,
        rank: newVariants.length,
        prices: {
          retailPrice: 0,
          tradeInPrice: 0,
          consignmentPrice: 0,
          buybackPrice: 0,
        },
      });
    }

    form.setValue('variants', newVariants);
  };

  const handleOptionValueUpdate = (value: string[], index: number) => {
    const { isTouched: hasUserSelectedVariants } =
      form.getFieldState('variants');

    const newOptions = [...watchedOptions];
    newOptions[index]!.values = value.map((it) => ({ value: it }));

    const permutations = getPermutations(newOptions);
    const oldVariants = [...watchedVariants];

    const findMatchingPermutation = (options: Record<string, string>) => {
      return permutations.find((permutation) =>
        Object.keys(options).every((key) => options[key] === permutation[key]),
      );
    };

    const newVariants = oldVariants.reduce(
      (variants, variant) => {
        const match = findMatchingPermutation(variant.options);

        if (match) {
          variants.push({
            ...variant,
            name: getVariantName(match),
            options: match,
          });
        }

        return variants;
      },
      [] as typeof oldVariants,
    );

    const usedPermutations = new Set(
      newVariants.map((variant) => variant.options),
    );
    const unusedPermutations = permutations.filter(
      (permutation) => !usedPermutations.has(permutation),
    );

    for (const permutation of unusedPermutations) {
      newVariants.push({
        name: getVariantName(permutation),
        options: permutation,
        shouldCreate: !hasUserSelectedVariants,
        rank: newVariants.length,
        isDefault: false,
        sku: '',
        prices: {
          retailPrice: 0,
          tradeInPrice: 0,
          consignmentPrice: 0,
          buybackPrice: 0,
        },
      });
    }

    form.setValue('variants', newVariants);
  };

  const getCheckboxState = (variants: CreateVariant[]) => {
    if (variants.every((variant) => variant.shouldCreate)) {
      return true;
    }

    if (variants.some((variant) => variant.shouldCreate)) {
      return 'indeterminate';
    }

    return false;
  };

  const onCheckboxChange = (value: boolean | 'indeterminate') => {
    switch (value) {
      case true: {
        const update = watchedVariants.map((variant) => {
          return {
            ...variant,
            should_create: true,
          };
        });

        form.setValue('variants', update);
        break;
      }
      case false: {
        const update = watchedVariants.map((variant) => {
          return {
            ...variant,
            should_create: false,
          };
        });

        form.setValue('variants', update);
        break;
      }
      case 'indeterminate':
        break;
    }
  };

  const handleRankChange = (
    items: FieldArrayWithId<CreateProduct, 'variants'>[],
  ) => {
    const update = items.map((item, index) => {
      const variant = watchedVariants.find((v) => v.name === item.name);

      return {
        id: item.id,
        ...(variant || item),
        rank: index,
      };
    });

    variants.replace(update);
  };

  return (
    <div id="variants" className="flex flex-col space-y-8">
      <div className="flex flex-col space-y-6">
        <h2 className="font-medium text-lg">Variants</h2>
        <SwitchBox
          control={form.control}
          name="variantsEnabled"
          title="Yes, this is a product with variants"
          description="When unchecked, we will create a default variant for you"
          onCheckedChange={(checked) => {
            if (checked) {
              form.setValue('options', [
                {
                  name: '',
                  values: [],
                },
              ]);
              form.setValue('variants', []);
            } else {
              createDefaultOptionAndVariant();
            }
          }}
        />
      </div>

      {watchedAreVariantsEnabled && (
        <>
          <div className="flex flex-col space-y-6">
            <Form.Field
              control={form.control}
              name="options"
              render={() => (
                <Form.Item>
                  <div className="flex flex-col space-y-6">
                    <div className="flex items-start justify-between gap-x-4">
                      <div className="flex flex-col">
                        <p className="text-base">Product options</p>
                        <p className="text-muted-foreground text-sm">
                          Define the options for the product, e.g. color, size,
                          etc.
                        </p>
                      </div>
                      <Button
                        type="button"
                        variant="secondary"
                        onClick={() => {
                          options.append({
                            name: '',
                            values: [],
                          });
                        }}
                      >
                        Add
                      </Button>
                    </div>

                    {showInvalidOptionsMessage && (
                      <Alert dismissible variant="error">
                        Please create at least one option.
                      </Alert>
                    )}

                    <ol className="flex flex-col space-y-4">
                      {options.fields.map((option, index) => (
                        <li
                          key={option.id}
                          className="flex w-full items-start space-x-4 rounded-lg border border-border py-4 ps-4 pe-2 shadow-black/5 shadow-sm"
                        >
                          <div className="flex flex-1 flex-col space-y-2">
                            <div className="flex w-full items-center space-x-4">
                              <Form.Label className="w-12">Title</Form.Label>
                              <Input
                                {...form.register(`options.${index}.name`)}
                                containerClassName="flex-1 rounded-lg bg-background"
                                placeholder="Color"
                              />
                            </div>

                            <div className="flex w-full items-center space-x-4">
                              <Form.Label className="w-12">Values</Form.Label>
                              <div className="flex-1">
                                <Controller
                                  control={form.control}
                                  name={`options.${index}.values`}
                                  render={({
                                    field: { onChange, ...field },
                                  }) => {
                                    return (
                                      <ChipInput
                                        {...field}
                                        value={field.value.map(
                                          (it) => it.value,
                                        )}
                                        onChange={(value) => {
                                          handleOptionValueUpdate(value, index);
                                          onChange(
                                            value.map((it) => ({ value: it })),
                                          );
                                        }}
                                        placeholder="Black, White, Blue"
                                      />
                                    );
                                  }}
                                />
                              </div>
                            </div>
                          </div>

                          <Button
                            size="icon"
                            variant="ghost"
                            disabled={index === 0}
                            onClick={() => handleRemoveOption(index)}
                          >
                            <CloseIcon className="size-5 text-muted-foreground" />
                          </Button>
                        </li>
                      ))}
                    </ol>
                  </div>
                </Form.Item>
              )}
            />
          </div>
          <div className="flex flex-col space-y-8">
            <div className="flex flex-col">
              <p className="">Product variants</p>
              <p className="text-muted-foreground text-sm">
                This randing will effect the variants; order in the storefront
              </p>
            </div>
            {!showInvalidOptionsMessage && showInvalidVariantsMessage && (
              <Alert dismissible variant="error">
                Please select at least one variant.
              </Alert>
            )}

            {variants.fields.length > 0 ? (
              <div className="overflow-hidden rounded-xl border border-border">
                <div
                  className="grid items-center gap-3 border-border border-b bg-accent px-6 py-2.5 text-muted-foreground"
                  style={{
                    gridTemplateColumns: `20px 28px repeat(${watchedOptions.length}, 1fr)`,
                  }}
                >
                  <div>
                    <Checkbox
                      className="relative"
                      checked={getCheckboxState(watchedVariants)}
                      onCheckedChange={onCheckboxChange}
                    />
                  </div>
                  <div />
                  {watchedOptions.map((option, index) => (
                    <div key={index}>
                      <p className="font-medium text-sm">{option.name}</p>
                    </div>
                  ))}
                </div>
                <SortableList
                  items={variants.fields}
                  onChange={handleRankChange}
                  renderItem={(item, index) => {
                    return (
                      <SortableList.Item
                        id={item.id}
                        className={cn('border-border border-b bg-background', {
                          'border-b-0': index === variants.fields.length - 1,
                        })}
                      >
                        <div
                          className="grid w-full items-center gap-3 px-6 py-2.5 text-muted-foreground"
                          style={{
                            gridTemplateColumns: `20px 28px repeat(${watchedOptions.length}, 1fr)`,
                          }}
                        >
                          <Form.Field
                            control={form.control}
                            name={`variants.${index}.shouldCreate` as const}
                            render={({
                              field: { value, onChange, ...field },
                            }) => {
                              return (
                                <Form.Item>
                                  <Form.Control>
                                    <Checkbox
                                      className="relative"
                                      {...field}
                                      checked={value}
                                      onCheckedChange={onChange}
                                    />
                                  </Form.Control>
                                </Form.Item>
                              );
                            }}
                          />
                          <SortableList.DragHandle />
                          {Object.values(item.options).map((value, index) => (
                            <p key={index} className="text-sm">
                              {value}
                            </p>
                          ))}
                        </div>
                      </SortableList.Item>
                    );
                  }}
                />
              </div>
            ) : (
              <Alert>Add options to create variants.</Alert>
            )}
            {variants.fields.length > 0 && (
              <Tip label="Tip">
                Variants left unchecked won&apos;t be created. You can always
                create and edit variants afterwards but this list fits the
                variations in your product options.
              </Tip>
            )}
          </div>
        </>
      )}
    </div>
  );
};
