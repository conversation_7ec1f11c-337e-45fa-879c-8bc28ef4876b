import { slugify } from '@/lib/slug';
import { useGetBrands, useGetCategories } from '@muraadso/api/hooks';
import type { CreateProduct } from '@muraadso/models/products';
import { Form, Input, Select, Textarea } from '@muraadso/ui';
import type { UseFormReturn } from 'react-hook-form';

type ProductCreateDetailsGeneralSectionProps = {
  form: UseFormReturn<CreateProduct>;
};

export const ProductCreateDetailsGeneralSection = ({
  form,
}: ProductCreateDetailsGeneralSectionProps) => {
  const { brands } = useGetBrands();
  const { categories } = useGetCategories();

  return (
    <div className="flex w-full flex-col space-y-6">
      <div className="grid w-full grid-cols-2 items-start space-x-6">
        <Form.Field
          control={form.control}
          name="name"
          render={({ field: { onChange, ...field } }) => (
            <Form.Item>
              <Form.Label>
                <span>Name</span>
                <span className="ms-1 text-destructive">*</span>
              </Form.Label>
              <Form.Control>
                <Input
                  id="name"
                  type="text"
                  {...field}
                  onChange={(e) => {
                    onChange(e.target.value);
                    form.setValue('slug', slugify(e.target.value));
                    form.trigger('slug');
                  }}
                  placeholder="Name"
                />
              </Form.Control>
              <Form.Message className="font-normal" />
            </Form.Item>
          )}
        />

        <Form.Field
          control={form.control}
          name="slug"
          render={({ field }) => (
            <Form.Item>
              <Form.Label
                tooltip={
                  <span>
                    A slug is a human-readable ID that must be <br />
                    unique. It&apos;s often used in URLs.
                  </span>
                }
              >
                <span>Slug</span>
                <span className="ms-1 text-destructive">*</span>
              </Form.Label>
              <Form.Control>
                <Input id="name" type="text" {...field} placeholder="Slug" />
              </Form.Control>
              <Form.Message className="font-normal" />
            </Form.Item>
          )}
        />
      </div>
      <div className="grid w-full grid-cols-2 items-start space-x-6">
        <Form.Field
          control={form.control}
          name="categoryId"
          render={({ field: { value, onChange } }) => (
            <Form.Item>
              <Form.Label>
                <span>Category</span>
                <span className="ms-1 text-destructive">*</span>
              </Form.Label>
              <Form.Control>
                <Select value={value} onValueChange={onChange}>
                  <Select.Trigger>
                    <Select.Value placeholder="Select a category" />
                  </Select.Trigger>
                  <Select.Content>
                    {categories?.map(({ id, name }) => (
                      <Select.Item key={id} value={id}>
                        {name}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>
              </Form.Control>
              <Form.Message className="font-normal" />
            </Form.Item>
          )}
        />

        <Form.Field
          control={form.control}
          name="brandId"
          render={({ field: { value, onChange } }) => (
            <Form.Item>
              <Form.Label>
                <span>Brand</span>
                <span className="ms-1 text-destructive">*</span>
              </Form.Label>
              <Form.Control>
                <Select value={value} onValueChange={onChange}>
                  <Select.Trigger>
                    <Select.Value placeholder="Select a brand" />
                  </Select.Trigger>
                  <Select.Content>
                    {brands?.map(({ id, name }) => (
                      <Select.Item key={id} value={id}>
                        {name}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>
              </Form.Control>
              <Form.Message className="font-normal" />
            </Form.Item>
          )}
        />
      </div>

      <Form.Field
        control={form.control}
        name="description"
        render={({ field }) => (
          <Form.Item>
            <Form.Label optional>
              <span>Description</span>
            </Form.Label>
            <Form.Control>
              <Textarea
                rows={3}
                className="resize-none"
                placeholder="Description"
                {...field}
                value={field.value ?? ''}
              />
            </Form.Control>
          </Form.Item>
        )}
      />
    </div>
  );
};
