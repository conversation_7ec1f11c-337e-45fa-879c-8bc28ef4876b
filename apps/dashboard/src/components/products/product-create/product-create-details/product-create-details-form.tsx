import type { Image } from '@/components/image-picker';
import type { CreateProduct } from '@muraadso/models/products';
import { Separator } from '@muraadso/ui';
import React from 'react';
import type { UseFormReturn } from 'react-hook-form';
import { ProductCreateDetailsGeneralSection } from './product-create-details-general-secion';
import { ProductCreateDetailsMediaSection } from './product-create-details-media-section';
import { ProductCreateDetailsVariantsSection } from './product-create-details-variants-section';

type ProductCreateDetailsFormProps = {
  form: UseFormReturn<CreateProduct>;
  images: Image[];
  setImages: (images: Image[]) => void;
};

export const ProductCreateDetailsForm = ({
  form,
  images,
  setImages,
}: ProductCreateDetailsFormProps) => {
  return (
    <div className="flex flex-col items-center p-16">
      <div className="flex w-full max-w-[720px] flex-col space-y-8">
        <div className="flex flex-col space-y-6">
          <h4 className="font-medium text-lg">General</h4>
          <ProductCreateDetailsGeneralSection form={form} />
          <ProductCreateDetailsMediaSection
            images={images}
            setImages={setImages}
          />
        </div>
        <Separator />
        <ProductCreateDetailsVariantsSection form={form} />
      </div>
    </div>
  );
};
