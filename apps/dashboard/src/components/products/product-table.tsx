'use client';

import { DataTable } from '@/components/data-table';
import type { Product } from '@muraadso/models/products';
import { useProductColumns } from './product-columns';

interface ProductTableProps {
  products: Product[];
}

export const ProductTable = ({ products }: ProductTableProps) => {
  const columns = useProductColumns();

  return (
    <DataTable columns={columns} data={products} enablePagination={false} />
  );
};
