'use client';

import React from 'react';
import { Pagination } from './../paginations';
import { ProductActions } from './product-actions';
import { ProductFilters } from './product-filters';
import { ProductSearch } from './product-search';
import { ProductSort } from './product-sort';
import { ProductStats } from './product-stats';
import { ProductTable } from './product-table';
import { useProducts } from './use-products';

export const ProductsPage = () => {
  const { isPending, products, pagination, error } = useProducts();

  if (error) return <div>Error</div>;

  return (
    <div className="px4 flex w-full flex-col px-12 py-2">
      <ProductStats />

      <div className="mb-6 flex flex-wrap gap-4">
        <ProductSearch />
        <ProductFilters />
        <ProductSort />
        <ProductActions />
      </div>

      {isPending && (
        <div className="flex items-center justify-center py-8">
          <div className="text-muted-foreground">Loading items...</div>
        </div>
      )}

      {products.length > 0 && (
        <>
          <ProductTable products={products} />

          {pagination && (
            <Pagination
              totalPages={pagination.totalPages}
              hasNextPage={pagination.hasNextPage}
              hasPreviousPage={pagination.hasPreviousPage}
            />
          )}
        </>
      )}
    </div>
  );
};
