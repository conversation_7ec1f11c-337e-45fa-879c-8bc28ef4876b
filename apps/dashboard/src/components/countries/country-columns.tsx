'use client';

import { useModal } from '@/components/modal';
import { useDeleteCountry } from '@muraadso/api/hooks';
import { MoreHorizontalIcon } from '@muraadso/icons';
import type { Country } from '@muraadso/models/countries';
import { Button, Checkbox, DropdownMenu } from '@muraadso/ui';
import type { ColumnDef } from '@tanstack/react-table';
import { formatDate } from 'date-fns';

export function useCountryColumns(): ColumnDef<Country>[] {
  const { mutateAsync: deleteCountry } = useDeleteCountry();
  const modal = useModal();

  return [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => {
        const country = row.original;
        return (
          <div className="flex items-center gap-2">
            <span className="text-lg">{country.emoji}</span>
            <span className="font-medium">{country.name}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'code',
      header: 'Code',
      cell: ({ row }) => (
        <span className="font-mono text-sm">{row.getValue('code')}</span>
      ),
    },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      cell: ({ row }) => {
        const date = row.getValue('createdAt') as string;
        return <span>{formatDate(new Date(date), 'MMM dd, yyyy')}</span>;
      },
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      cell: ({ row }) => {
        const date = row.getValue('updatedAt') as string;
        return <span>{formatDate(new Date(date), 'MMM dd, yyyy')}</span>;
      },
    },
    {
      id: 'actions',
      enableHiding: false,
      cell: ({ row }) => {
        const country = row.original;
        return (
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontalIcon className="size-5 text-muted-foreground opacity-0 group-hover:opacity-100" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content align="end">
              <DropdownMenu.Label>Actions</DropdownMenu.Label>
              <DropdownMenu.Item
                onClick={() => modal.open('countries', country)}
              >
                Edit
              </DropdownMenu.Item>
              <DropdownMenu.Item
                onClick={async () => {
                  try {
                    await deleteCountry(country.id);
                  } catch (_) {}
                }}
              >
                Delete
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu>
        );
      },
    },
  ];
}
