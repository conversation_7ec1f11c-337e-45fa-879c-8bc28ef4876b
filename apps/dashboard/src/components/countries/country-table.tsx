'use client';

import { DataTable } from '@/components/data-table';
import type { Country } from '@muraadso/models/countries';
import { useCountryColumns } from './country-columns';

interface CountryTableProps {
  countries: Country[];
}

export const CountryTable = ({ countries }: CountryTableProps) => {
  const columns = useCountryColumns();

  return (
    <DataTable
      columns={columns}
      data={countries}
      enablePagination={false}
    />
  );
};
