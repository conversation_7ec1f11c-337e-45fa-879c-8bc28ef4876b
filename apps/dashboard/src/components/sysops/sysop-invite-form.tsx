import { zodResolver } from '@hookform/resolvers/zod';
import { useGetLocations, useInviteSysop } from '@muraadso/api/hooks';
import { MailIcon, UserIcon } from '@muraadso/icons';
import { type InviteUser, inviteUserSchema } from '@muraadso/models/auth';
import { Button, Checkbox, Dialog, Form, Input, Select } from '@muraadso/ui';
import React from 'react';
import { useForm } from 'react-hook-form';

export const SysopInviteForm = ({
  open,
  onClose,
}: { open: boolean; onClose: () => void }) => {
  const { mutateAsync, isPending } = useInviteSysop();
  const { locations, isPending: locationsLoading } = useGetLocations();

  const form = useForm<InviteUser>({
    mode: 'onSubmit',
    resolver: zodResolver(inviteUserSchema),
    defaultValues: {
      email: '',
      name: '',
      locationId: undefined,
      isSuperAdmin: false,
    },
  });

  const onSubmit = async (data: InviteUser) => {
    try {
      await mutateAsync(data);
      onClose();
    } catch (_) {}
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <Dialog.Content>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <Dialog.Body
              title="Invite User"
              description="Invite a new user to the dashboard."
            >
              <Form.Field
                name="email"
                control={form.control}
                render={({ field }) => (
                  <Form.Item>
                    <div className="mb-2.5 flex w-full items-center justify-between">
                      <Form.Label htmlFor="email" className="leading-none">
                        Email <span className="text-destructive">*</span>
                      </Form.Label>
                      <Form.Message className="font-normal" />
                    </div>
                    <Form.Control>
                      <Input
                        id="email"
                        type="email"
                        autoComplete="email"
                        leading={<MailIcon size={16} />}
                        readOnly={form.formState.isSubmitting}
                        placeholder="<EMAIL>"
                        disabled={isPending}
                        className="mt-1"
                        {...field}
                      />
                    </Form.Control>
                  </Form.Item>
                )}
              />

              <Form.Field
                name="name"
                control={form.control}
                render={({ field }) => (
                  <Form.Item className="mt-4">
                    <div className="mb-2.5 flex w-full items-center justify-between">
                      <Form.Label htmlFor="name" className="leading-none">
                        Name <span className="text-destructive">*</span>
                      </Form.Label>
                      <Form.Message className="font-normal" />
                    </div>
                    <Form.Control>
                      <Input
                        id="name"
                        type="text"
                        leading={<UserIcon size={16} />}
                        readOnly={form.formState.isSubmitting}
                        placeholder="Full name"
                        disabled={isPending}
                        className="mt-1"
                        {...field}
                      />
                    </Form.Control>
                  </Form.Item>
                )}
              />

              <Form.Field
                name="isSuperAdmin"
                control={form.control}
                render={({ field }) => (
                  <Form.Item className="mt-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="isSuperAdmin"
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isPending}
                      />
                      <Form.Label
                        htmlFor="isSuperAdmin"
                        className="leading-none"
                      >
                        Super Admin
                      </Form.Label>
                    </div>
                    <Form.Message className="font-normal" />
                  </Form.Item>
                )}
              />

              {!form.watch('isSuperAdmin') && (
                <Form.Field
                  name="locationId"
                  control={form.control}
                  render={({ field }) => (
                    <Form.Item className="mt-4">
                      <div className="mb-2.5 flex w-full items-center justify-between">
                        <Form.Label
                          htmlFor="locationId"
                          className="leading-none"
                        >
                          Location <span className="text-destructive">*</span>
                        </Form.Label>
                        <Form.Message className="font-normal" />
                      </div>
                      <Select
                        defaultValue={field.value}
                        onValueChange={field.onChange}
                        disabled={isPending || locationsLoading}
                      >
                        <Form.Control>
                          <Select.Trigger>
                            <Select.Value placeholder="Select location" />
                          </Select.Trigger>
                        </Form.Control>
                        <Select.Content>
                          {locations?.map((location) => (
                            <Select.Item key={location.id} value={location.id}>
                              {location.name} - {location.city}
                            </Select.Item>
                          ))}
                        </Select.Content>
                      </Select>
                    </Form.Item>
                  )}
                />
              )}
            </Dialog.Body>
            <Dialog.Footer>
              <div className="flex w-full items-center justify-end gap-4">
                <Button
                  variant="outline"
                  disabled={isPending}
                  onClick={onClose}
                >
                  Cancel
                </Button>
                <Button loading={isPending} type="submit">
                  Send Invite
                </Button>
              </div>
            </Dialog.Footer>
          </form>
        </Form>
      </Dialog.Content>
    </Dialog>
  );
};
