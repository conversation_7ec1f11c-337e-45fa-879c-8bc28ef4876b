'use client';

import { useModal } from '@/components/modal';
import { useQuickSearch } from '@/components/quick-search';
import { useNavigation } from '@/hooks/navigation';
import {
  BellIcon,
  HomeIcon,
  LogoutIcon,
  PlusIcon,
  SearchIcon,
  SettingsIcon,
  SunIcon,
  UserIcon,
} from '@muraadso/icons';
import { Avatar, Button, DropdownMenu, Separator } from '@muraadso/ui';
import { Sidebar } from '@muraadso/ui/blocks';
import { cn } from '@muraadso/ui/utils';
import { useTheme } from 'next-themes';
import React from 'react';

export const Header = () => {
  const { activePathInfo } = useNavigation();
  const { theme, setTheme } = useTheme();

  const { open: openModal } = useModal();
  const { openSearch } = useQuickSearch();

  return (
    <header className="flex min-h-16 shrink-0 items-center gap-2 px-12 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
      <div className="flex flex-1 items-center gap-3">
        {/* Page Icon, Title and Description */}
        <div className="flex items-center gap-3">
          {activePathInfo.icon && (
            <div className="flex size-12 items-center justify-center rounded-full border border-border">
              <activePathInfo.icon className="size-5 text-foreground opacity-80" />
            </div>
          )}
          <div className="flex flex-col space-y-1">
            <h1 className="font-medium text-foreground text-lg leading-tight opacity-80">
              {activePathInfo.title}
            </h1>
            <p className="text-muted-foreground text-sm leading-tight">
              {activePathInfo.description}
            </p>
          </div>
        </div>
      </div>

      <div className="flex items-center">
        <Button
          onClick={() => {
            openModal('orders');
          }}
          variant="secondary"
          className="mr-3 gap-2"
        >
          <PlusIcon className="h-4 w-4" />
          <span className="hidden font-normal sm:inline">Place order</span>
        </Button>

        <Button
          variant="ghost"
          size="icon"
          className="relative mr-1"
          onClick={() => openSearch()}
        >
          <SearchIcon className="h-4 w-4" />
        </Button>

        <Button variant="ghost" size="icon" className="relative mr-4">
          <BellIcon className="h-4 w-4" />
        </Button>

        <DropdownMenu>
          <DropdownMenu.Trigger asChild>
            <Button variant="ghost" className="relative size-8 rounded-full">
              <Avatar className="size-9">
                <Avatar.Image src="/images/avatar.png" alt="" />
              </Avatar>
            </Button>
          </DropdownMenu.Trigger>
          <DropdownMenu.Content className="w-56" align="end" forceMount>
            <DropdownMenu.Label className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <Avatar className="h-8 w-8 rounded-lg">
                  <Avatar.Image src="/images/avatar.png" alt="" />
                  <Avatar.Fallback className="rounded-lg">CN</Avatar.Fallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-medium">Admin</span>
                  <span className="truncate text-muted-foreground text-xs">
                    <EMAIL>
                  </span>
                </div>
              </div>
            </DropdownMenu.Label>
            <DropdownMenu.Separator />
            <DropdownMenu.Item>
              <UserIcon className="mr-2 h-4 w-4" />
              <span>Profile</span>
            </DropdownMenu.Item>
            <DropdownMenu.Item>
              <SettingsIcon className="mr-2 h-4 w-4" />
              <span>Settings</span>
            </DropdownMenu.Item>
            <DropdownMenu.Item>
              <BellIcon className="mr-2 h-4 w-4" />
              <span>Notifications</span>
            </DropdownMenu.Item>

            <DropdownMenu.Sub>
              <DropdownMenu.SubTrigger>
                <SunIcon className="mr-2 h-4 w-4" />
                <span>Theme</span>
              </DropdownMenu.SubTrigger>
              <DropdownMenu.Portal>
                <DropdownMenu.SubContent>
                  <DropdownMenu.RadioGroup
                    value={theme}
                    onValueChange={(value) => setTheme(value)}
                  >
                    <DropdownMenu.RadioItem value="dark">
                      Dark
                    </DropdownMenu.RadioItem>
                    <DropdownMenu.RadioItem value="light">
                      Light
                    </DropdownMenu.RadioItem>
                    <DropdownMenu.RadioItem value="system">
                      System
                    </DropdownMenu.RadioItem>
                  </DropdownMenu.RadioGroup>
                </DropdownMenu.SubContent>
              </DropdownMenu.Portal>
            </DropdownMenu.Sub>

            <DropdownMenu.Separator />
            <DropdownMenu.Item>
              <LogoutIcon className="mr-2 h-4 w-4" />
              <span>Log out</span>
            </DropdownMenu.Item>
          </DropdownMenu.Content>
        </DropdownMenu>
      </div>
    </header>
  );
};
