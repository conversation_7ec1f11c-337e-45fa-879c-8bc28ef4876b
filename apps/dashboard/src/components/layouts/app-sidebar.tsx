'use client';

import { useModal } from '@/components/modal';
import { useQuickSearch } from '@/components/quick-search/quick-search-context';
import { useNavigation } from '@/hooks/navigation';
import {
  BellIcon,
  ChevronRightIcon,
  LogoutIcon,
  MoreVerticalIcon,
  SearchIcon,
  SettingsIcon,
  SunIcon,
  UserIcon,
} from '@muraadso/icons';
import { Avatar, Button, Collapsible, DropdownMenu, Kbd } from '@muraadso/ui';
import { Sidebar, useSidebar } from '@muraadso/ui/blocks';
import { cn } from '@muraadso/ui/utils';
import { useTheme } from 'next-themes';
import Link from 'next/link';
import React, { type PropsWithChildren } from 'react';

export const AppSidebar = () => {
  const { mainMenu, getSubNavItems, isActive } = useNavigation();
  const { open } = useSidebar();
  const { openSearch } = useQuickSearch();

  const { theme, setTheme } = useTheme();

  return (
    <Sidebar variant="inset" collapsible="icon">
      <Sidebar.Header>
        <div className="flex w-full items-start justify-between pt-2">
          <img
            className={cn('w-9', { hidden: !open })}
            src="/images/logo-alt.png"
            alt=""
          />

          <div className={cn('mx-3 flex flex-1 flex-col', { hidden: !open })}>
            <h4 className="font-medium text-sm">Muraadso</h4>
            <p className="text-muted-foreground text-xs">Admin Dashboard</p>
          </div>

          <Sidebar.Trigger
            className={cn('self-center', {
              'rotate-180': !open,
            })}
          />
        </div>
      </Sidebar.Header>
      <Sidebar.Content>
        {mainMenu.map((item) => {
          return (
            <Sidebar.Group key={item.title}>
              <Sidebar.GroupLabel>{item.title}</Sidebar.GroupLabel>
              <Sidebar.GroupContent>
                <Sidebar.Menu>
                  {item.items.map((subItem) => {
                    const subItemItems = getSubNavItems(subItem.path);
                    if (subItemItems.length > 0) {
                      return (
                        <Collapsible
                          key={subItem.path}
                          defaultOpen={isActive(subItem.path)}
                          className="group/collapsible"
                        >
                          <Sidebar.MenuItem>
                            <Collapsible.Trigger asChild>
                              <Sidebar.MenuButton tooltip={subItem.title}>
                                {<subItem.icon />}
                                <span>{subItem.title}</span>
                                <ChevronRightIcon className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                              </Sidebar.MenuButton>
                            </Collapsible.Trigger>
                            <Collapsible.Content>
                              <Sidebar.MenuSub>
                                {subItemItems.map((subItemSubItem) => (
                                  <Sidebar.MenuSubItem
                                    key={subItemSubItem.path}
                                  >
                                    <Sidebar.MenuSubButton asChild>
                                      <Link href={subItemSubItem.path}>
                                        <span>{subItemSubItem.title}</span>
                                      </Link>
                                    </Sidebar.MenuSubButton>
                                  </Sidebar.MenuSubItem>
                                ))}
                              </Sidebar.MenuSub>
                            </Collapsible.Content>
                          </Sidebar.MenuItem>
                        </Collapsible>
                      );
                    }

                    if (subItem.path === '/search') {
                      return (
                        <Sidebar.MenuItem key={subItem.title}>
                          <Sidebar.MenuButton
                            isActive={isActive(subItem.path)}
                            tooltip={subItem.title}
                            onClick={openSearch}
                          >
                            <subItem.icon />
                            <span>{subItem.title}</span>
                          </Sidebar.MenuButton>
                          <Sidebar.MenuBadge variant="ghost" className="">
                            <Kbd className="">⌘K</Kbd>
                          </Sidebar.MenuBadge>
                        </Sidebar.MenuItem>
                      );
                    }

                    return (
                      <Sidebar.MenuItem key={subItem.title}>
                        <Sidebar.MenuButton
                          asChild={subItem.path !== '/search'}
                          isActive={isActive(subItem.path)}
                          tooltip={subItem.title}
                        >
                          <Link href={subItem.path}>
                            {<subItem.icon />}
                            <span>{subItem.title}</span>
                          </Link>
                        </Sidebar.MenuButton>
                        {/*{subItem.path === '/orders' && (*/}
                        {/*  <Sidebar.MenuBadge variant="secondary">*/}
                        {/*    12k*/}
                        {/*  </Sidebar.MenuBadge>*/}
                        {/*)}*/}
                      </Sidebar.MenuItem>
                    );
                  })}
                </Sidebar.Menu>
              </Sidebar.GroupContent>
            </Sidebar.Group>
          );
        })}
      </Sidebar.Content>
      <Sidebar.Separator />
      <Sidebar.Footer>
        <Sidebar.Menu>
          <Sidebar.MenuItem>
            <DropdownMenu>
              <DropdownMenu.Trigger asChild>
                <Sidebar.MenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <Avatar.Image src="/images/avatar.png" alt="" />
                    <Avatar.Fallback className="rounded-lg">CN</Avatar.Fallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-medium">Admin</span>
                    <span className="truncate text-muted-foreground text-xs">
                      <EMAIL>
                    </span>
                  </div>
                  <MoreVerticalIcon className="ml-auto size-4" />
                </Sidebar.MenuButton>
              </DropdownMenu.Trigger>
              <DropdownMenu.Content
                side="right"
                align="end"
                sideOffset={4}
                className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
              >
                <DropdownMenu.Label className="p-0 font-normal">
                  <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                    <Avatar className="h-8 w-8 rounded-lg">
                      <Avatar.Image src="/images/avatar.png" alt="" />
                      <Avatar.Fallback className="rounded-lg">
                        CN
                      </Avatar.Fallback>
                    </Avatar>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-medium">Admin</span>
                      <span className="truncate text-muted-foreground text-xs">
                        <EMAIL>
                      </span>
                    </div>
                  </div>
                </DropdownMenu.Label>
                <DropdownMenu.Separator />
                <DropdownMenu.Item>
                  <UserIcon className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </DropdownMenu.Item>
                <DropdownMenu.Item>
                  <SettingsIcon className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </DropdownMenu.Item>
                <DropdownMenu.Item>
                  <BellIcon className="mr-2 h-4 w-4" />
                  <span>Notifications</span>
                </DropdownMenu.Item>

                <DropdownMenu.Sub>
                  <DropdownMenu.SubTrigger>
                    <SunIcon className="mr-2 h-4 w-4" />
                    <span>Theme</span>
                  </DropdownMenu.SubTrigger>
                  <DropdownMenu.Portal>
                    <DropdownMenu.SubContent>
                      <DropdownMenu.RadioGroup
                        value={theme}
                        onValueChange={(value) => setTheme(value)}
                      >
                        <DropdownMenu.RadioItem value="dark">
                          Dark
                        </DropdownMenu.RadioItem>
                        <DropdownMenu.RadioItem value="light">
                          Light
                        </DropdownMenu.RadioItem>
                        <DropdownMenu.RadioItem value="system">
                          System
                        </DropdownMenu.RadioItem>
                      </DropdownMenu.RadioGroup>
                    </DropdownMenu.SubContent>
                  </DropdownMenu.Portal>
                </DropdownMenu.Sub>

                <DropdownMenu.Separator />
                <DropdownMenu.Item>
                  <LogoutIcon className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenu.Item>
              </DropdownMenu.Content>
            </DropdownMenu>
          </Sidebar.MenuItem>
        </Sidebar.Menu>
      </Sidebar.Footer>
    </Sidebar>
  );
};

export const AppSidebarInset = ({ children }: PropsWithChildren) => {
  return <Sidebar.Inset>{children}</Sidebar.Inset>;
};
