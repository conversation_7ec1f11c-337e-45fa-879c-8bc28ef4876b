'use client';

import { useNavigation } from '@/hooks/navigation';
import type { NavItem } from '@/types/navigation';
import { ChevronRightIcon } from '@muraadso/icons';
import { cn } from '@muraadso/ui/utils';
import { motion } from 'motion/react';
import Link from 'next/link';
import React from 'react';

type Orientation = 'horizontal' | 'vertical';

type BaseNavbarProps = {
  orientation: Orientation;
} & React.PropsWithChildren;

export type ControlledNavItem = Pick<NavItem, 'title'>;

export type ControlledNavbarProps = BaseNavbarProps & {
  items: ControlledNavItem[];
  selectedItem: ControlledNavItem;
  onItemSelect: (item: ControlledNavItem) => void;
};

export type UncontrolledNavbarProps = BaseNavbarProps & {
  items?: never;
  selectedItem?: never;
  onItemSelect?: never;
};

export type NavbarProps = ControlledNavbarProps | UncontrolledNavbarProps;

const isControlled = (props: NavbarProps): props is ControlledNavbarProps => {
  return 'items' in props && Array.isArray(props.items);
};

export const Navbar: React.FC<NavbarProps> = (props) => {
  const { orientation, children } = props;
  const navigation = useNavigation();

  if (isControlled(props)) {
    // Controlled mode: use passed items and buttons
    return (
      <ControlledNavbar
        items={props.items}
        selectedItem={props.selectedItem}
        onItemSelect={props.onItemSelect}
        orientation={orientation}
      >
        {children}
      </ControlledNavbar>
    );
  }

  // Uncontrolled mode: use navigation hook and links
  return (
    <UncontrolledNavbar
      items={navigation.activePathSubNavItems}
      isActive={navigation.isActive}
      orientation={orientation}
    >
      {children}
    </UncontrolledNavbar>
  );
};

// Controlled Navbar Component
const ControlledNavbar: React.FC<{
  items: ControlledNavItem[];
  selectedItem: ControlledNavItem;
  onItemSelect: (item: ControlledNavItem) => void;
  orientation: Orientation;
  children: React.ReactNode;
}> = ({ items, selectedItem, onItemSelect, orientation, children }) => {
  const isSelected = (item: ControlledNavItem) =>
    item.title === selectedItem.title;

  if (orientation === 'vertical') {
    return (
      <div className="flex w-full items-start space-x-8 py-6">
        <nav className="block w-[258px] shrink-0 rounded-xl bg-background p-2.5 shadow-sm ring-1 ring-border ring-inset">
          <h4 className="mb-2 px-2 py-1 font-medium text-muted-foreground text-xs uppercase">
            select menu
          </h4>
          <div className="flex w-full flex-col space-y-2">
            {items.map((item) => {
              const selected = isSelected(item);
              return (
                <button
                  key={item.title}
                  type="button"
                  onClick={() => onItemSelect(item)}
                  data-active={selected}
                  className={cn(
                    'flex w-full items-center gap-x-2.5 rounded-lg p-2 text-left text-muted-foreground text-sm outline-none transition-colors hover:bg-accent focus:outline-none',
                    'data-[active=true]:bg-accent data-[active=true]:text-foreground',
                  )}
                >
                  <p className="flex-1 font-medium">{item.title}</p>
                  <div className="rounded-full bg-background p-0.5">
                    <ChevronRightIcon
                      data-active={selected}
                      className="hidden size-4 text-muted-foreground data-[active=true]:block"
                    />
                  </div>
                </button>
              );
            })}
          </div>
        </nav>
        <section className="flex-1">{children}</section>
      </div>
    );
  }

  return (
    <div className="flex w-full flex-col">
      <nav className="relative grid overflow-x-auto overflow-y-hidden overscroll-contain">
        <div className="relative hidden h-12 items-center gap-6 whitespace-nowrap border-border border-b md:flex">
          {items.map((item) => {
            const selected = isSelected(item);
            return (
              <button
                key={item.title}
                type="button"
                onClick={() => onItemSelect(item)}
                className={cn(
                  'relative flex h-12 cursor-pointer items-center justify-center gap-1.5 py-3.5 text-muted-foreground text-sm outline-none transition duration-200 ease-out hover:text-foreground focus:outline-none',
                  'data-[active=true]:text-primary',
                )}
                data-active={selected}
              >
                <span
                  className="absolute bottom-px hidden h-0.5 w-full bg-primary data-[active=true]:block"
                  data-active={selected}
                />
                <span>{item.title}</span>
              </button>
            );
          })}
        </div>
      </nav>
      {children}
    </div>
  );
};

// Uncontrolled Navbar Component
const UncontrolledNavbar: React.FC<{
  items: NavItem[];
  isActive: (path: string, onlyCurrent?: boolean) => boolean;
  orientation: Orientation;
  children: React.ReactNode;
}> = ({ items, isActive, orientation, children }) => {
  const [hoveredPath, setHoveredPath] = React.useState<string | null>(null);

  if (orientation === 'vertical') {
    return (
      <div className="flex w-full items-start space-x-8 py-6">
        <nav className="block w-[228px] shrink-0 rounded-xl bg-background p-2.5 shadow-sm ring-1 ring-border ring-inset">
          {/*<h4 className="mb-2 px-2 py-1 font-medium text-muted-foreground text-xs uppercase">*/}
          {/*  navigate*/}
          {/*</h4>*/}
          <div className="flex w-full flex-col space-y-2">
            {items.map((item) => {
              const active = isActive(item.path, true);
              return (
                <Link
                  key={item.path}
                  href={item.path}
                  data-active={active}
                  className={cn(
                    'flex w-full items-center gap-x-2.5 rounded-lg p-2 text-left text-muted-foreground text-sm outline-none transition-colors hover:bg-accent focus:outline-none',
                    'data-[active=true]:bg-accent data-[active=true]:text-foreground',
                  )}
                >
                  <item.icon
                    className={cn('size-5', active && 'text-primary')}
                  />
                  <p className="flex-1 font-medium">{item.title}</p>
                  <div className="rounded-full bg-background p-0.5">
                    <ChevronRightIcon
                      data-active={active}
                      className="hidden size-4 text-muted-foreground data-[active=true]:block"
                    />
                  </div>
                </Link>
              );
            })}
          </div>
        </nav>
        <section className="flex-1">{children}</section>
      </div>
    );
  }

  return (
    <div className="flex w-full flex-col">
      <nav className="-ml-10 group relative grid overflow-x-auto overflow-y-hidden overscroll-contain px-10">
        <div className="relative hidden h-12 items-center whitespace-nowrap border-border border-b md:flex">
          {items.map((item) => {
            const active = isActive(item.path, true);
            return (
              <Link
                key={item.path}
                href={item.path}
                onMouseOver={() => setHoveredPath(item.path)}
                onMouseLeave={() => setHoveredPath(null)}
                className={cn(
                  '-ml-4 relative flex h-12 items-center justify-center gap-1.5 px-5 py-3.5 text-muted-foreground text-sm outline-none transition duration-200 ease-out hover:text-foreground focus:outline-none',
                  'data-[active=true]:text-primary',
                )}
                data-active={active}
              >
                <span
                  className="absolute right-4 bottom-px left-4 hidden h-0.5 bg-primary data-[active=true]:block"
                  data-active={active}
                />
                <span>{item.title}</span>
                {item.path === hoveredPath && (
                  <motion.div
                    className="-z-10 -translate-y-1/2 absolute inset-0 top-1/2 hidden h-9 rounded-lg bg-muted group-hover:block"
                    layoutId="navbar"
                    aria-hidden="true"
                    style={{
                      width: '100%',
                    }}
                    transition={{
                      type: 'spring',
                      bounce: 0,
                      stiffness: 128,
                      damping: 16,
                      duration: 0.1,
                    }}
                  />
                )}
              </Link>
            );
          })}
        </div>
      </nav>
      {children}
    </div>
  );
};
