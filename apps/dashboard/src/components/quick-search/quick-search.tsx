'use client';

import { useQuickSearch } from '@/components/quick-search/quick-search-context';
import type { SearchResult } from '@/types/search';
import {
  ClockFastForwardIcon,
  PackageIcon,
  SearchIcon,
  ShoppingCartIcon,
  UsersIcon,
} from '@muraadso/icons';
import { Command, Kbd } from '@muraadso/ui';
import type React from 'react';
import { useEffect } from 'react';

const getResultIcon = (type: SearchResult['type']) => {
  switch (type) {
    case 'product':
      return PackageIcon;
    case 'order':
      return ShoppingCartIcon;
    case 'customer':
      return UsersIcon;
    case 'navigation':
      return SearchIcon;
    default:
      return SearchIcon;
  }
};

const getResultTypeLabel = (type: SearchResult['type']) => {
  switch (type) {
    case 'product':
      return 'Product';
    case 'order':
      return 'Order';
    case 'customer':
      return 'Customer';
    case 'navigation':
      return 'Navigate';
    default:
      return '';
  }
};

export const QuickSearch = () => {
  const {
    isOpen,
    query,
    results,
    isLoading,
    recentSearches,
    clearRecentSearches,
    closeSearch,
    setQuery,
    selectResult,
  } = useQuickSearch();

  // Handle escape key
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        closeSearch();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, closeSearch]);

  if (!isOpen) return null;

  const hasResults = Object.values(results).some(
    (arr: unknown[]) => arr.length > 0,
  );
  const showRecentSearches = !query && recentSearches.length > 0;

  return (
    <Command.Dialog
      open={isOpen}
      onOpenChange={closeSearch}
      title="Quick Search"
      description="Search for products, orders, customers, or navigate to different sections"
      className="w-full min-w-xl p-0"
    >
      <Command.Input
        placeholder="Search anything..."
        value={query}
        onValueChange={setQuery}
        loading={isLoading}
      />

      <Command.List className="min-h-[450px]">
        {!query && !showRecentSearches && (
          <Command.Empty>
            <div className="flex flex-col items-center gap-2 py-6">
              <SearchIcon className="h-8 w-8 text-muted-foreground" />
              <p className="text-muted-foreground text-sm">
                Start typing to search products, orders, customers, or navigate
                to different sections
              </p>
              <div className="flex items-center gap-2 text-muted-foreground text-xs">
                <span>Press</span>
                <Kbd>Esc</Kbd>
                <span>to close</span>
              </div>
            </div>
          </Command.Empty>
        )}

        {showRecentSearches && (
          <Command.Group heading="Recent Searches">
            {recentSearches.map((search: string, index: number) => (
              <Command.Item
                key={`recent-${index}`}
                value={search}
                onSelect={() => setQuery(search)}
              >
                <ClockFastForwardIcon className="mr-2 h-4 w-4" />
                {search}
              </Command.Item>
            ))}
          </Command.Group>
        )}

        {query && !hasResults && !isLoading && (
          <Command.Empty>
            <div className="flex flex-col items-center gap-2 py-6">
              <SearchIcon className="h-8 w-8 text-muted-foreground" />
              <p className="text-muted-foreground text-sm">
                No results found for &quot{query}&quot
              </p>
              <p className="text-muted-foreground text-xs">
                Try searching for products, orders, customers, or page names
              </p>
            </div>
          </Command.Empty>
        )}

        {results.navigation.length > 0 && (
          <Command.Group heading="Navigate">
            {results.navigation.map((result) => {
              const Icon = result.icon || SearchIcon;
              return (
                <Command.Item
                  key={result.id}
                  value={result.title}
                  onSelect={() => selectResult(result)}
                >
                  <Icon className="mr-2 h-4 w-4" />
                  <div className="flex flex-col">
                    <span>{result.title}</span>
                    {result.description && (
                      <span className="line-clamp-1 text-muted-foreground text-xs">
                        {result.description}
                      </span>
                    )}
                  </div>
                  <Command.Shortcut>
                    <span className="!tracking-tighter hidden">
                      {getResultTypeLabel(result.type)}
                    </span>
                  </Command.Shortcut>
                </Command.Item>
              );
            })}
          </Command.Group>
        )}

        {/* Product Results */}
        {results.products.length > 0 && (
          <Command.Group heading="Products">
            {results.products.map((result) => {
              const Icon = getResultIcon(result.type);
              return (
                <Command.Item
                  key={result.id}
                  value={result.title}
                  onSelect={() => selectResult(result)}
                >
                  <Icon className="mr-2 h-4 w-4" />
                  <div className="flex flex-col">
                    <span>{result.title}</span>
                    {result.description && (
                      <span className="mt-0.5 line-clamp-1 text-muted-foreground text-xs">
                        {result.description}
                      </span>
                    )}
                    {(result.category || result.brand) && (
                      <span className="mt-0.5 text-muted-foreground text-xs">
                        {[result.category, result.brand]
                          .filter(Boolean)
                          .join(' • ')}
                      </span>
                    )}
                  </div>
                  <Command.Shortcut>
                    {getResultTypeLabel(result.type)}
                  </Command.Shortcut>
                </Command.Item>
              );
            })}
          </Command.Group>
        )}

        {/* Order Results */}
        {results.orders.length > 0 && (
          <Command.Group heading="Orders">
            {results.orders.map((result) => {
              const Icon = getResultIcon(result.type);
              return (
                <Command.Item
                  key={result.id}
                  value={result.title}
                  onSelect={() => selectResult(result)}
                >
                  <Icon className="mr-2 h-4 w-4" />
                  <div className="flex flex-col">
                    <span>{result.title}</span>
                    {result.customerName && (
                      <span className="mt-0.5 text-muted-foreground text-xs">
                        Customer: {result.customerName}
                      </span>
                    )}
                    {result.description && (
                      <span className="mt-0.5 text-muted-foreground text-xs">
                        {result.description}
                      </span>
                    )}
                  </div>
                  <Command.Shortcut>
                    {getResultTypeLabel(result.type)}
                  </Command.Shortcut>
                </Command.Item>
              );
            })}
          </Command.Group>
        )}

        {/* Customer Results */}
        {results.customers.length > 0 && (
          <Command.Group heading="Customers">
            {results.customers.map((result) => {
              const Icon = getResultIcon(result.type);
              return (
                <Command.Item
                  key={result.id}
                  value={result.title}
                  onSelect={() => selectResult(result)}
                >
                  <Icon className="mr-2 h-4" />
                  <div className="flex flex-col">
                    <span>{result.title}</span>
                    {result.description && (
                      <span className="mt-0.5 text-muted-foreground text-xs">
                        {result.description}
                      </span>
                    )}
                  </div>
                  <Command.Shortcut>
                    {getResultTypeLabel(result.type)}
                  </Command.Shortcut>
                </Command.Item>
              );
            })}
          </Command.Group>
        )}
      </Command.List>
    </Command.Dialog>
  );
};
