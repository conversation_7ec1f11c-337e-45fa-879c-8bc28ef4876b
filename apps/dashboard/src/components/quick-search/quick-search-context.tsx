'use client';

import { useModal } from '@/components/modal';
import { navigationConfig } from '@/config/navigation';
import { useDebounce } from '@/hooks/debounce';
import type {
  CustomerSearchResult,
  NavigationSearchResult,
  OrderSearchResult,
  ProductSearchResult,
  SearchResult,
  SearchResults,
  UseQuickSearchReturn,
} from '@/types/search';
import {
  useGetCustomers,
  useGetOrders,
  useGetProducts,
} from '@muraadso/api/hooks';
import { useRouter } from 'next/navigation';
import React, {
  createContext,
  useContext,
  useCallback,
  useEffect,
  useState,
  type PropsWithChildren,
} from 'react';

const RECENT_SEARCHES_KEY = 'quick-search-recent';
const MAX_RECENT_SEARCHES = 5;

// Simple fuzzy search function
const fuzzyMatch = (text: string, query: string): boolean => {
  const textLower = text.toLowerCase();
  const queryLower = query.toLowerCase();

  if (textLower.includes(queryLower)) return true;

  // Simple fuzzy matching - check if all query characters appear in order
  let queryIndex = 0;
  for (let i = 0; i < textLower.length && queryIndex < queryLower.length; i++) {
    if (textLower[i] === queryLower[queryIndex]) {
      queryIndex++;
    }
  }
  return queryIndex === queryLower.length;
};

const QuickSearchContext = createContext<UseQuickSearchReturn | undefined>(
  undefined,
);

export const QuickSearchProvider = ({ children }: PropsWithChildren) => {
  const router = useRouter();
  const modal = useModal();
  const [isOpen, setIsOpen] = useState(false);
  const [query, setQuery] = useState('');
  const [recentSearches, setRecentSearches] = useState<string[]>([]);

  const debouncedQuery = useDebounce(query, 300);
  const shouldSearch = debouncedQuery.length > 0;

  // API calls - only when we have a search query
  const { products, isPending: isLoadingProducts } = useGetProducts({
    q: shouldSearch ? debouncedQuery : undefined,
    pageSize: 5,
  });

  const { orders, isPending: isLoadingOrders } = useGetOrders({
    q: shouldSearch ? debouncedQuery : undefined,
    pageSize: 5,
  });

  const { customers, isPending: isLoadingCustomers } = useGetCustomers({
    q: shouldSearch ? debouncedQuery : undefined,
    pageSize: 5,
  });

  const isLoading = isLoadingProducts || isLoadingOrders || isLoadingCustomers;

  // Load recent searches from localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem(RECENT_SEARCHES_KEY);
      if (stored) {
        setRecentSearches(JSON.parse(stored));
      }
    } catch (error) {
      console.warn('Failed to load recent searches:', error);
    }
  }, []);

  // Save recent searches to localStorage
  const saveRecentSearch = useCallback((searchQuery: string) => {
    if (!searchQuery.trim()) return;

    setRecentSearches((prev) => {
      const updated = [
        searchQuery,
        ...prev.filter((s) => s !== searchQuery),
      ].slice(0, MAX_RECENT_SEARCHES);

      try {
        localStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(updated));
      } catch (error) {
        console.warn('Failed to save recent search:', error);
      }

      return updated;
    });
  }, []);

  // Search navigation items
  const searchNavigation = useCallback(
    (searchQuery: string): NavigationSearchResult[] => {
      if (!searchQuery) return [];

      const results: NavigationSearchResult[] = [];

      for (const menu of navigationConfig.mainMenu) {
        for (const item of menu.items) {
          if (
            fuzzyMatch(item.title, searchQuery) ||
            fuzzyMatch(item.description, searchQuery)
          ) {
            results.push({
              id: `nav-${item.path}`,
              type: 'navigation',
              title: item.title,
              description: item.description,
              url: item.path,
              icon: item.icon,
            });
          }
        }
      }

      return results.slice(0, 5);
    },
    [],
  );

  // Process search results
  const results: SearchResults = {
    navigation: searchNavigation(debouncedQuery),
    products: products.map(
      (product): ProductSearchResult => ({
        id: `product-${product.id}`,
        type: 'product',
        title: product.name,
        description: product.description || undefined,
        url: `/products/${product.id}`,
        data: product,
        category: product.category?.name,
        brand: product.brand?.name,
      }),
    ),
    orders: orders.map(
      (order): OrderSearchResult => ({
        id: `order-${order.id}`,
        type: 'order',
        title: `Order ${order.trackingId}`,
        description: `${order.serviceType} • ${order.serviceType}`,
        url: `/orders/${order.id}`,
        data: order,
        customerName: order.customer?.name,
        status: order.status,
        amount: order.totalAmount,
      }),
    ),
    customers: customers.map(
      (customer): CustomerSearchResult => ({
        id: `customer-${customer.id}`,
        type: 'customer',
        title: customer.name,
        description: customer.phone || customer.email || undefined,
        url: `/customers/${customer.id}`,
        data: customer,
        email: customer.email || undefined,
        phone: customer.phone || undefined,
      }),
    ),
  };

  const openSearch = useCallback(() => {
    setIsOpen(true);
  }, []);

  const closeSearch = useCallback(() => {
    setIsOpen(false);
    setQuery('');
  }, []);

  const selectResult = useCallback(
    (result: SearchResult) => {
      saveRecentSearch(query);
      closeSearch();

      // Use modal for products, orders, and customers
      if (
        result.type === 'product' ||
        result.type === 'order' ||
        result.type === 'customer'
      ) {
        // Map singular search result types to plural modal view types
        const modalViewType = result.type === 'product' ? 'products'
          : result.type === 'order' ? 'orders'
          : 'customers'; // result.type === 'customer'

        modal.open(modalViewType, result.data);
      } else {
        // Use navigation for other items (like navigation results)
        router.push(result.url);
      }
    },
    [query, modal, router, saveRecentSearch, closeSearch],
  );

  const clearRecentSearches = useCallback(() => {
    setRecentSearches([]);
    try {
      localStorage.removeItem(RECENT_SEARCHES_KEY);
    } catch (error) {
      console.warn('Failed to clear recent searches:', error);
    }
  }, []);

  // Global keyboard shortcut
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'k' && (event.metaKey || event.ctrlKey)) {
        event.preventDefault();
        openSearch();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [openSearch]);

  const value: UseQuickSearchReturn = {
    isOpen,
    query,
    results,
    isLoading,
    recentSearches,
    openSearch,
    closeSearch,
    setQuery,
    selectResult,
    clearRecentSearches,
  };

  return (
    <QuickSearchContext.Provider value={value}>
      {children}
    </QuickSearchContext.Provider>
  );
};

export const useQuickSearch = (): UseQuickSearchReturn => {
  const context = useContext(QuickSearchContext);
  if (context === undefined) {
    throw new Error('useQuickSearch must be used within a QuickSearchProvider');
  }
  return context;
};
