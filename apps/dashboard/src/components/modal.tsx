'use client';

import React, {
  type PropsWithChildren,
  useCallback,
  createContext,
  useContext,
  useState,
  useMemo,
} from 'react';

type View =
  | 'categories'
  | 'conditions'
  | 'brands'
  | 'products'
  | 'services'
  | 'orders'
  | 'customers'
  | 'locations'
  | 'countries'
  | 'cities'
  | 'districts'
  | 'inventory'
  | 'sysops';

type Modal<P, R> = {
  id: string;
  view: View;
  payload?: P;
  resolve: (value: R) => void;
};

type ModalState<P = unknown, R = unknown> = {
  modals: Modal<P, R>[];
};

type ModalActions = {
  open: <P = unknown, R = unknown>(view: View, payload?: P) => Promise<R>;
  close: (id: string, returnValue?: unknown) => void;
  closeAll: () => void;
  closeTop: (returnValue?: unknown) => void;
};

const ModalContext = createContext<(ModalState & ModalActions) | undefined>(
  undefined,
);

export const ModalProvider = ({ children }: PropsWithChildren) => {
  const [state, setState] = useState<ModalState>({ modals: [] });

  const open = useCallback(
    <P = unknown, R = unknown>(view: View, payload?: P): Promise<R> => {
      return new Promise<R>((resolve) => {
        const id = crypto.randomUUID();

        const newModal: Modal<P, R> = {
          id,
          view,
          payload,
          resolve: resolve as (value: R) => void,
        };

        setState((prevState) => ({
          modals: [...prevState.modals, newModal as Modal<unknown, unknown>],
        }));
      });
    },
    [],
  );

  const close = useCallback((id: string, returnValue?: unknown) => {
    setState((prevState) => {
      const modalIndex = prevState.modals.findIndex((modal) => modal.id === id);
      if (modalIndex === -1) return prevState;

      const modal = prevState.modals[modalIndex];

      if (returnValue !== undefined) {
        modal?.resolve(returnValue);
      }

      const newModals = [...prevState.modals];
      newModals.splice(modalIndex, 1);

      return { modals: newModals };
    });
  }, []);

  const closeAll = useCallback(() => {
    setState(() => {
      return { modals: [] };
    });
  }, []);

  const closeTop = useCallback((returnValue?: unknown) => {
    setState((prevState) => {
      if (prevState.modals.length === 0) return prevState;

      const topModal = prevState.modals[prevState.modals.length - 1];

      if (returnValue !== undefined) {
        topModal?.resolve(returnValue);
      }

      return {
        modals: prevState.modals.slice(0, -1),
      };
    });
  }, []);

  const value: ModalState & ModalActions = useMemo(
    () => ({
      ...state,
      open,
      close,
      closeAll,
      closeTop,
    }),
    [state, open, close, closeAll, closeTop],
  );

  return (
    <ModalContext.Provider value={value}>{children}</ModalContext.Provider>
  );
};

export const useModal = () => {
  const context = useContext(ModalContext);

  if (context === undefined) {
    throw new Error('useModal must be used within a ModalProvider');
  }

  return context;
};
