'use client';

import { useGetLocations } from '@muraadso/api/hooks';
import { Select } from '@muraadso/ui';
import { useQueryState } from 'nuqs';

export const InventoryFilters = () => {
  const [locationId, setLocationId] = useQueryState('locationId');
  const { locations } = useGetLocations();

  return (
    <div className="flex items-center">
      <Select
        value={locationId || 'all'}
        onValueChange={(value) => setLocationId(value === 'all' ? null : value)}
      >
        <Select.Trigger id="category">
          <Select.Value placeholder="All Categories" />
        </Select.Trigger>
        <Select.Content>
          <Select.Item value="all">All Locations</Select.Item>
          {locations?.map(({ id, name }) => (
            <Select.Item key={id} value={id}>
              {name}
            </Select.Item>
          ))}
        </Select.Content>
      </Select>
    </div>
  );
};
