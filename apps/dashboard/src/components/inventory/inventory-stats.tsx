'use client';

import { useGetInventoryStats } from '@muraadso/api/hooks';
import { Skeleton } from '@muraadso/ui';

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};

export const InventoryStats = () => {
  const { stats, isPending, error } = useGetInventoryStats();

  if (isPending) {
    return (
      <div className="mb-8 grid w-full grid-cols-4 gap-8 divide-x divide-border border-border border-y border-dashed py-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="flex w-full flex-col space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-8 w-16" />
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="mb-8 grid w-full grid-cols-4 gap-8 divide-x divide-border border-border border-y border-dashed py-6">
        <div className="col-span-4 text-center text-destructive text-sm">
          Failed to load inventory stats
        </div>
      </div>
    );
  }

  const getStockStatusColor = () => {
    if (stats.outOfStockCount > 10) return 'text-destructive';
    if (stats.lowStockCount > 5) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getStockStatusText = () => {
    if (stats.outOfStockCount > 10) return 'Needs attention';
    if (stats.lowStockCount > 5) return 'Monitor closely';
    return 'Healthy stock';
  };

  return (
    <div className="mb-8 grid w-full grid-cols-4 gap-8 divide-x divide-border border-border border-y border-dashed py-6">
      <div className="flex w-full flex-col space-y-2">
        <p className="font-medium text-muted-foreground text-sm">
          Inventory Value
        </p>
        <div className="flex items-center space-x-2">
          <span className="font-medium text-2xl">{formatCurrency(stats.totalInventoryValue)}</span>
          <span className="text-green-600 text-xs">
            {stats.totalUnitsInStock.toLocaleString()} units
          </span>
        </div>
      </div>

      <div className="flex w-full flex-col space-y-2">
        <p className="font-medium text-muted-foreground text-sm">Stock Alerts</p>
        <div className="flex items-center space-x-2">
          <span className="font-medium text-2xl">{stats.lowStockCount + stats.outOfStockCount}</span>
          <span className={`text-xs ${getStockStatusColor()}`}>
            {getStockStatusText()}
          </span>
        </div>
      </div>

      <div className="flex w-full flex-col space-y-2">
        <p className="font-medium text-muted-foreground text-sm">Out of Stock</p>
        <div className="flex items-center space-x-2">
          <span className="font-medium text-2xl">{stats.outOfStockCount}</span>
          <span className="text-muted-foreground text-xs">
            {stats.outOfStockCount > 0 ? 'items need restock' : 'all stocked'}
          </span>
        </div>
      </div>

      <div className="flex w-full flex-col space-y-2">
        <p className="font-medium text-muted-foreground text-sm">Locations</p>
        <div className="flex items-center space-x-2">
          <span className="font-medium text-2xl">{stats.activeLocationsCount}</span>
          <span className="text-muted-foreground text-xs">
            {formatCurrency(stats.avgInventoryPerLocation)} avg value
          </span>
        </div>
      </div>
    </div>
  );
};
