import { useGetInventory } from '@muraadso/api/hooks';
import { parseAsInteger, useQueryState } from 'nuqs';

export const useInventory = () => {
  const [page] = useQueryState('page', parseAsInteger.withDefault(1));
  const [pageSize] = useQueryState('pageSize', parseAsInteger.withDefault(10));
  const [q] = useQueryState('q');
  const [variantId] = useQueryState('variantId');
  const [locationId] = useQueryState('locationId');

  // const [isPublished] = useQueryState('isPublished');

  // Convert isPublished string to boolean if it exists
  // const publishedValue =
  //   isPublished === 'true' ? true : isPublished === 'false' ? false : undefined;

  const { isPending, data, error } = useGetInventory({
    page,
    pageSize,
    q: q || undefined,
    variantId: variantId || undefined,
    locationId: locationId || undefined,
    // isPublished: publishedValue,
  });

  return {
    isPending,
    data,
    error,
    inventory: data?.inventory || [],
    pagination: data?.pagination,
  };
};
