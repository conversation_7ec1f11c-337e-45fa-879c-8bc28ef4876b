'use client';

import { InventoryTable, useInventory } from '@/components/inventory';
import { InventoryFilters } from '@/components/inventory/inventory-filters';
import { InventorySearch } from '@/components/inventory/inventory-search';
import { InventoryStats } from '@/components/inventory/inventory-stats';
import { Pagination } from '@/components/paginations';

export const InventoryPage = () => {
  const { isPending, inventory, pagination } = useInventory();

  return (
    <div className="px4 flex w-full flex-col px-12 py-2">
      <InventoryStats />

      <div className="mb-6 flex flex-wrap gap-4">
        <InventorySearch isLoading={isPending} />
        <InventoryFilters />
      </div>

      {isPending && (
        <div className="flex items-center justify-center py-8">
          <div className="text-muted-foreground">Loading orders...</div>
        </div>
      )}

      {inventory.length > 0 && (
        <>
          <InventoryTable inventory={inventory} />

          {pagination && (
            <Pagination
              totalPages={pagination.totalPages}
              hasNextPage={pagination.hasNextPage}
              hasPreviousPage={pagination.hasPreviousPage}
            />
          )}
        </>
      )}
    </div>
  );
};
