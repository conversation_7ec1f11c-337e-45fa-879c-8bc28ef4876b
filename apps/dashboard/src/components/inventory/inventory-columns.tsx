'use client';

import { useCreateInventory, useUpdateInventory } from '@muraadso/api/hooks';
import { LocationIcon } from '@muraadso/icons';
import type { InventoryMatrixItem } from '@muraadso/models/inventory';
import { Button, Input, Label, Popover } from '@muraadso/ui';
import { cn } from '@muraadso/ui/utils';
import type { ColumnDef } from '@tanstack/react-table';
import { formatDate } from 'date-fns';
import { useState } from 'react';

export const useInventoryColumns = (): ColumnDef<InventoryMatrixItem>[] => {
  return [
    {
      accessorKey: 'variant.product.name',
      header: 'Item',
      cell: ({ row }) => {
        return (
          <div className="font-medium">
            {row.original?.productName} / {row.original.variantName}
          </div>
        );
      },
    },
    {
      accessorKey: 'variant.sku',
      header: 'SKU',
      cell: ({ row }) => {
        const sku = row.original.variantSKU || 'N/A';
        return <div className="text-muted-foreground">{sku}</div>;
      },
    },
    {
      accessorKey: 'location.name',
      header: 'Showroom',
      cell: ({ row }) => {
        const locationName = row.original.locationName || 'N/A';
        return (
          <div className="inline-flex items-center space-x-2">
            <LocationIcon className="size-4 text-muted-foreground " />
            <span>{locationName}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'quantity',
      header: () => <div className="w-full text-center">Stock</div>,
      cell: ({ row }) => <RowQuantityItem item={row.original} />,
    },
    {
      accessorKey: 'updatedAt',
      header: () => <div className="w-full text-end">Last Updated</div>,
      cell: ({ row }) => {
        return (
          <div className="text-end">
            {row.original.updatedAt
              ? formatDate(row.original.updatedAt, 'dd MMM, yyyy')
              : 'N/A'}
          </div>
        );
      },
    },
  ];
};

const RowQuantityItem = ({ item }: { item: InventoryMatrixItem }) => {
  const { mutateAsync: createInventory, isPending: isCreating } =
    useCreateInventory();
  const { mutateAsync: updateInventory, isPending: isUpdating } =
    useUpdateInventory();

  const isLoading = isCreating || isUpdating;

  const [quantity, setQuantity] = useState(item.quantity);
  const id = item.inventoryId;
  const variantId = item.variantId;
  const locationId = item.locationId;

  return (
    <Popover onOpenChange={() => setQuantity(item.quantity)}>
      <Popover.Trigger asChild>
        <Button
          size="sm"
          variant="ghost"
          className={cn('w-full text-sm', {
            'font-medium text-destructive': quantity === 0,
          })}
        >
          {quantity === 0 ? 'Out of Stock' : `${quantity} in Stock`}
        </Button>
      </Popover.Trigger>
      <Popover.Content className="w-56">
        <div className="flex w-full flex-col">
          <Label htmlFor="quantity">Stock Qty</Label>
          <Input
            id="quantity"
            placeholder="12"
            className="mt-2.5"
            value={quantity}
            max="999"
            min="0"
            step="1"
            type="number"
            disabled={isLoading}
            onChange={(e) => {
              const val = Math.min(Number(e.target.value), 999);
              setQuantity(Number(val));
            }}
          />
          <Button
            variant="secondary"
            className="mt-4"
            loading={isLoading}
            onClick={async () => {
              if (!id)
                await createInventory({
                  variantId,
                  locationId,
                  quantity,
                });
              else
                await updateInventory({
                  id,
                  variantId,
                  locationId,
                  quantity,
                });
            }}
          >
            Update
          </Button>
        </div>
      </Popover.Content>
    </Popover>
  );
};
