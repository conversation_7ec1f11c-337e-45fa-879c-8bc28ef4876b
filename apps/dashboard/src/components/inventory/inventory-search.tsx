'use client';

import { SearchIcon, SpinnerIcon } from '@muraadso/icons';
import { Input } from '@muraadso/ui';
import { useQueryState } from 'nuqs';
import { useEffect, useState } from 'react';

export const InventorySearch = ({ isLoading }: { isLoading: boolean }) => {
  const [searchParam, setSearchParam] = useQueryState('q');
  const [searchQuery, setSearchQuery] = useState(searchParam || '');

  useEffect(() => {
    const timer = setTimeout(() => {
      setSearchParam(searchQuery || null);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery, setSearchParam]);

  return (
    <div className="min-w-[200px] flex-1">
      <div className="relative">
        <Input
          placeholder="Search..."
          value={searchQuery}
          leading={
            isLoading ? (
              <SpinnerIcon className="size-4 text-muted-foreground" />
            ) : (
              <SearchIcon className="size-4 text-muted-foreground" />
            )
          }
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>
    </div>
  );
};
