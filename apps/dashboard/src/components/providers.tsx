'use client';

import { ManagedModal } from '@/components/managed-modal';
import { ModalProvider } from '@/components/modal';
import { env } from '@/config/env';
import { ApiClientProvider } from '@muraadso/api';
import { Toaster, TooltipProvider, toast } from '@muraadso/ui';
import { ThemeProvider } from 'next-themes';
import { NuqsAdapter } from 'nuqs/adapters/next/app';
import React, { type PropsWithChildren } from 'react';

const apiClientConfig = {
  baseURL: env.NEXT_PUBLIC_API_URL + '/v1',
  timeout: 30000,
  onError: (error: { message: string; status?: number }) => {
    toast.error(`[${error.status}] ${error.message}]`);
    if (error.status === 401) {
      window.location.href = '/login';
    }
  },
};

const Providers = ({ children }: PropsWithChildren) => {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="light"
      enableSystem
      disableTransitionOnChange
    >
      <NuqsAdapter>
        <ApiClientProvider
          config={apiClientConfig}
          enableDevtools={process.env.NODE_ENV === 'development'}
        >
          <TooltipProvider delayDuration={100}>
            {children}
            <Toaster position="top-center" richColors />
          </TooltipProvider>
        </ApiClientProvider>
      </NuqsAdapter>
    </ThemeProvider>
  );
};

export default Providers;
