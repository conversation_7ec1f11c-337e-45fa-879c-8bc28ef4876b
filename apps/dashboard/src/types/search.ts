import type { Customer } from '@muraadso/models/customers';
import type { Order } from '@muraadso/models/orders';
import type { Product } from '@muraadso/models/products';
import type { ComponentType } from 'react';

export type SearchResultType = 'navigation' | 'product' | 'order' | 'customer';

export interface BaseSearchResult {
  id: string;
  type: SearchResultType;
  title: string;
  description?: string;
  url: string;
}

export interface NavigationSearchResult extends BaseSearchResult {
  type: 'navigation';
  icon?: ComponentType;
}

export interface ProductSearchResult extends BaseSearchResult {
  type: 'product';
  data: Product;
  category?: string;
  brand?: string;
}

export interface OrderSearchResult extends BaseSearchResult {
  type: 'order';
  data: Order;
  customerName?: string;
  status?: string;
  amount?: number;
}

export interface CustomerSearchResult extends BaseSearchResult {
  type: 'customer';
  data: Customer;
  email?: string;
  phone?: string;
}

export type SearchResult =
  | NavigationSearchResult
  | ProductSearchResult
  | OrderSearchResult
  | CustomerSearchResult;

export interface SearchResults {
  navigation: NavigationSearchResult[];
  products: ProductSearchResult[];
  orders: OrderSearchResult[];
  customers: CustomerSearchResult[];
}

export interface QuickSearchState {
  isOpen: boolean;
  query: string;
  results: SearchResults;
  isLoading: boolean;
  recentSearches: string[];
}

export interface UseQuickSearchReturn {
  isOpen: boolean;
  query: string;
  results: SearchResults;
  isLoading: boolean;
  recentSearches: string[];
  openSearch: () => void;
  closeSearch: () => void;
  setQuery: (query: string) => void;
  selectResult: (result: SearchResult) => void;
  clearRecentSearches: () => void;
}
