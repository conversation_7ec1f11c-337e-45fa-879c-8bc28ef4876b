'use client';

import { DataTable } from '@/components/data-table';
import { useModal } from '@/components/modal';
import { getImageUrl } from '@/lib/utils';
import { useDeleteBrand, useGetBrands } from '@muraadso/api/hooks';
import { MoreHorizontalIcon, PlusIcon } from '@muraadso/icons';
import type { Brand } from '@muraadso/models/brands';
import { Avatar, Button, Checkbox, DropdownMenu } from '@muraadso/ui';
import type { ColumnDef } from '@tanstack/react-table';
import { formatDate } from 'date-fns';
import React from 'react';

const Page = () => {
  const { brands, isPending } = useGetBrands();
  const { mutateAsync: deleteBrand } = useDeleteBrand();

  const modal = useModal();

  const columns: ColumnDef<Brand>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
    },
    {
      accessorKey: 'image',
      header: 'Image',
      cell: ({ row }) => (
        <Avatar className="size-8 rounded-sm">
          <Avatar.Image src={getImageUrl(row.getValue('image'))} alt="" />
          <Avatar.Fallback className="rounded-sm">
            {row.original?.name?.substring(0, 2).toUpperCase() ?? '-'}
          </Avatar.Fallback>
        </Avatar>
      ),
    },
    {
      accessorKey: 'name',
      header: 'Brand',
      cell: ({ row }) => (
        <div className="capitalize">{row.original?.name ?? '-'}</div>
      ),
    },
    {
      accessorKey: 'slug',
      header: 'Slug',
      cell: ({ row }) => (
        <div className="whitespace-nowrap">/{row.getValue('slug') ?? '-'}</div>
      ),
    },
    {
      accessorKey: 'description',
      header: 'Description',
      cell: ({ row }) => (
        <div className="line-clamp-1 max-w-sm">
          {row.getValue('description')}
        </div>
      ),
    },
    {
      accessorKey: 'rank',
      header: 'Rank',
      cell: ({ row }) => <div>{row.original?.rank ?? '-'}</div>,
    },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      cell: ({ row }) => (
        <div className="whitespace-nowrap">
          {formatDate(row.original?.createdAt, 'dd MMM, yyyy') ?? '-'}
        </div>
      ),
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      cell: ({ row }) => (
        <div className="whitespace-nowrap">
          {formatDate(row.original?.updatedAt, 'dd MMM, yyyy') ?? '-'}
        </div>
      ),
    },
    {
      id: 'actions',
      enableHiding: false,
      cell: ({ row }) => {
        const brand = row.original;
        return (
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontalIcon className="size-5 text-muted-foreground opacity-0 group-hover:opacity-100" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content align="end">
              <DropdownMenu.Label>Actions</DropdownMenu.Label>
              <DropdownMenu.Item onClick={() => modal.open('brands', brand)}>
                Edit
              </DropdownMenu.Item>
              <DropdownMenu.Item
                onClick={async () => {
                  try {
                    await deleteBrand(brand.id);
                  } catch (_) {}
                }}
              >
                Delete
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="flex w-full flex-col px-12 py-8">
      <div className="mb-6 flex w-full items-center justify-between">
        <div className="font-medium text-xl">Brands</div>
        <Button
          onClick={() => {
            modal.open('brands');
          }}
          className="gap-x-2"
        >
          <PlusIcon className="size-4.5" />
          New Brand
        </Button>
      </div>

      {isPending && <div>Loading...</div>}
      {brands && <DataTable columns={columns} data={brands} />}
    </div>
  );
};

export default Page;
