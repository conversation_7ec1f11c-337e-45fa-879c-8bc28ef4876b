import { AppSidebar, AppSidebarInset } from '@/components/layouts/app-sidebar';
import { Header } from '@/components/layouts/header';
import { ManagedModal } from '@/components/managed-modal';
import { ModalProvider } from '@/components/modal';
import { QuickSearch } from '@/components/quick-search';
import { QuickSearchProvider } from '@/components/quick-search/quick-search-context';
import { OrderPageProvider } from '@/contexts/order-page-context';
import { SidebarProvider } from '@muraadso/ui/blocks';
import type React from 'react';

const Layout = ({ children }: React.PropsWithChildren) => {
  return (
    <OrderPageProvider>
      <ModalProvider>
        <QuickSearchProvider>
          <SidebarProvider>
            <AppSidebar />
            <AppSidebarInset>
              <Header />
              {children}
              <ManagedModal />
              <QuickSearch />
            </AppSidebarInset>
          </SidebarProvider>
        </QuickSearchProvider>
      </ModalProvider>
    </OrderPageProvider>
  );
};

export default Layout;
