'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import {} from '@muraadso/api/hooks';
import { useAuth } from '@muraadso/api/hooks';
import { CheckIcon, CloseIcon, LockIcon } from '@muraadso/icons';
import {
  type ChangePassword,
  changePasswordSchema,
} from '@muraadso/models/auth';
import { Button, Form, Input, Separator } from '@muraadso/ui';
import React, { useMemo } from 'react';
import { useForm } from 'react-hook-form';

const Page = () => {
  const {
    changePassword: { mutateAsync: changePassword, isPending },
  } = useAuth();

  const form = useForm<ChangePassword>({
    mode: 'onTouched',
    resolver: zodResolver(changePasswordSchema),
    defaultValues: {
      password: '',
      confirm: '',
    },
  });

  const checkStrength = (pass: string) => {
    const requirements = [
      { regex: /.{8,}/, text: 'At least 8 characters' },
      { regex: /[0-9]/, text: 'At least 1 number' },
      { regex: /[a-z]/, text: 'At least 1 lowercase letter' },
      { regex: /[A-Z]/, text: 'At least 1 uppercase letter' },
    ];

    return requirements.map((req) => ({
      met: req.regex.test(pass),
      text: req.text,
    }));
  };

  const strength = checkStrength(form.watch('password'));

  const strengthScore = useMemo(() => {
    return strength.filter((req) => req.met).length;
  }, [strength]);

  const getStrengthColor = (score: number) => {
    if (score === 0) return 'bg-border';
    if (score <= 1) return 'bg-red-500';
    if (score <= 2) return 'bg-orange-500';
    if (score === 3) return 'bg-amber-500';
    return 'bg-emerald-500';
  };

  const getStrengthText = (score: number) => {
    if (score === 0) return 'Choose password';
    if (score <= 2) return 'Weak password';
    if (score === 3) return 'Medium password';
    return 'Strong password';
  };

  const onSubmit = async (data: ChangePassword) => {
    try {
      await changePassword(data);
      form.reset();
    } catch (_) {}
  };

  return (
    <div className="flex w-full flex-col px-12 py-2">
      <Separator />
      <div className="mx-auto flex w-full max-w-sm flex-col py-16">
        <div className="mb-4 flex w-full flex-col space-y-1">
          <h1 className="font-medium text-lg">Change password</h1>
          <p className="text-muted-foreground text-sm">
            Update password for enhanced account security.
          </p>
        </div>

        <Separator />

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="mt-6 grid w-full"
          >
            <Form.Field
              name="password"
              control={form.control}
              render={({ field }) => (
                <Form.Item>
                  <Form.Label htmlFor="passwrod" className="mb-2.5 block">
                    New Password <span className="text-destructive">*</span>
                  </Form.Label>
                  <Form.Control>
                    <Input
                      id="passwrod"
                      placeholder="••••••••••"
                      className="w-full"
                      type="password"
                      leading={<LockIcon size={16} />}
                      disabled={isPending}
                      {...field}
                    />
                  </Form.Control>
                  <Form.Message className="font-normal" />
                </Form.Item>
              )}
            />

            <Form.Field
              name="confirm"
              control={form.control}
              render={({ field }) => (
                <Form.Item className="mt-4">
                  <Form.Label htmlFor="confirm" className="mb-2.5 block">
                    Confirm Password <span className="text-destructive">*</span>
                  </Form.Label>
                  <Form.Control>
                    <Input
                      id="confirm"
                      placeholder="••••••••••"
                      className="w-full"
                      type="password"
                      leading={<LockIcon size={16} />}
                      disabled={isPending}
                      {...field}
                    />
                  </Form.Control>
                  <Form.Message className="font-normal" />
                </Form.Item>
              )}
            />

            <div>
              <div
                className="my-4 h-1 w-full overflow-hidden rounded-full bg-border"
                aria-valuenow={strengthScore}
                aria-valuemin={0}
                aria-valuemax={4}
                aria-label="Password strength"
              >
                <div
                  className={`h-full ${getStrengthColor(strengthScore)} transition-all duration-500 ease-out`}
                  style={{ width: `${(strengthScore / 4) * 100}%` }}
                />
              </div>

              <p className="mb-2 font-medium text-foreground text-sm">
                {getStrengthText(strengthScore)}. Must contain:
              </p>

              <ul className="space-y-1.5" aria-label="Password requirements">
                {strength.map((req, index) => (
                  <li
                    key={`key-${index * 2}`}
                    className="flex items-center gap-2"
                  >
                    {req.met ? (
                      <CheckIcon
                        size={16}
                        className="text-emerald-500"
                        aria-hidden="true"
                      />
                    ) : (
                      <CloseIcon
                        size={16}
                        className="text-muted-foreground/80"
                        aria-hidden="true"
                      />
                    )}
                    <span
                      className={`text-xs ${req.met ? 'text-emerald-600' : 'text-muted-foreground'}`}
                    >
                      {req.text}
                      <span className="sr-only">
                        {req.met
                          ? ' - Requirement met'
                          : ' - Requirement not met'}
                      </span>
                    </span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="flex w-full justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                className="mt-8 px-5"
                onClick={() => form.reset()}
                disabled={isPending || !form.formState.isDirty}
              >
                Discard
              </Button>
              <Button type="submit" className="mt-8 px-5" loading={isPending}>
                Apply changes
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default Page;
