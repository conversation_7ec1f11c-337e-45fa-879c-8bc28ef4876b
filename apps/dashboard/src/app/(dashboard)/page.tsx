'use client';

import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Legend,
  Pie,
  <PERSON><PERSON>hart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

import {
  useGetCustomerGrowth,
  useGetDashboardStats,
  useGetOrderStatus,
  useGetProductCategories,
  useGetSalesData,
  useGetTopProducts,
} from '@muraadso/api/hooks';
import { Card, Chart, type Config, Skeleton } from '@muraadso/ui';

const Page = () => {
  const {
    stats,
    isPending: isStatsLoading,
    error: statsError,
  } = useGetDashboardStats();

  const {
    sales,
    isPending: isSalesLoading,
    error: salesError,
  } = useGetSalesData();

  const {
    customerGrowth,
    isPending: isCustomerGrowthLoading,
    error: customerGrowthError,
  } = useGetCustomerGrowth();

  const {
    productCategories,
    isPending: isCategoriesLoading,
    error: categoriesError,
  } = useGetProductCategories();

  const {
    orderStatus,
    isPending: isOrderStatusLoading,
    error: orderStatusError,
  } = useGetOrderStatus();

  const {
    topProducts,
    isPending: isTopProductsLoading,
    error: topProductsError,
  } = useGetTopProducts();

  const renderError = (error: unknown) => {
    if (!error) return null;
    return (
      <div className="flex h-full w-full items-center justify-center p-4 text-center text-red-500 text-sm">
        <p>Failed to load data. Please try again later.</p>
      </div>
    );
  };

  const salesChartConfig = {
    sales: {
      label: 'Sales',
      color: '#2563eb',
    },
  } satisfies Config;

  const customerGrowthConfig = {
    new: {
      label: 'New Customers',
      color: '#2563eb',
    },
    returning: {
      label: 'Returning Customers',
      color: '#60a5fa',
    },
  } satisfies Config;

  // Colors for pie charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(value);
  };

  return (
    <div className="space-y-6 px-12 py-4">
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Sales"
          value={formatCurrency(stats.totalSales)}
          description="Last 6 months"
          isLoading={isStatsLoading}
          error={statsError}
        />
        <StatCard
          title="Total Orders"
          value={stats.totalOrders.toString()}
          description="Last 6 months"
          isLoading={isStatsLoading}
          error={statsError}
        />
        <StatCard
          title="Total Products"
          value={stats.totalProducts.toString()}
          description="Active products"
          isLoading={isStatsLoading}
          error={statsError}
        />
        <StatCard
          title="Total Customers"
          value={stats.totalCustomers.toString()}
          description="Registered customers"
          isLoading={isStatsLoading}
          error={statsError}
        />
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <Card>
          <Card.Header>
            <Card.Title>Sales Over Time</Card.Title>
            <Card.Description>
              Monthly sales for the last 6 months
            </Card.Description>
          </Card.Header>
          <Card.Content>
            {isSalesLoading ? (
              <Skeleton className="h-[300px] w-full" />
            ) : salesError ? (
              renderError(salesError)
            ) : (
              <Chart config={salesChartConfig} className="h-[300px] w-full">
                <AreaChart data={sales}>
                  <defs>
                    <linearGradient id="colorSales" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#2563eb" stopOpacity={0.8} />
                      <stop
                        offset="95%"
                        stopColor="#2563eb"
                        stopOpacity={0.1}
                      />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
                  <Tooltip formatter={(value) => [`$${value}`, 'Sales']} />
                  <Area
                    type="monotone"
                    dataKey="sales"
                    stroke="var(--color-sales)"
                    fillOpacity={1}
                    fill="url(#colorSales)"
                  />
                </AreaChart>
              </Chart>
            )}
          </Card.Content>
        </Card>

        <Card>
          <Card.Header>
            <Card.Title>Customer Growth</Card.Title>
            <Card.Description>New vs returning customers</Card.Description>
          </Card.Header>
          <Card.Content>
            {isCustomerGrowthLoading ? (
              <Skeleton className="h-[300px] w-full" />
            ) : customerGrowthError ? (
              renderError(customerGrowthError)
            ) : (
              <Chart config={customerGrowthConfig} className="h-[300px] w-full">
                <BarChart data={customerGrowth}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar
                    dataKey="new"
                    fill="var(--color-new)"
                    radius={[4, 4, 0, 0]}
                  />
                  <Bar
                    dataKey="returning"
                    fill="var(--color-returning)"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </Chart>
            )}
          </Card.Content>
        </Card>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <Card>
          <Card.Header>
            <Card.Title>Product Categories</Card.Title>
            <Card.Description>Distribution by category</Card.Description>
          </Card.Header>
          <Card.Content>
            {isCategoriesLoading ? (
              <Skeleton className="h-[300px] w-full" />
            ) : categoriesError ? (
              renderError(categoriesError)
            ) : (
              <div className="h-[300px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={productCategories}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) =>
                        `${name}: ${((percent || 0) * 100).toFixed(0)}%`
                      }
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {productCategories?.map((_, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={COLORS[index % COLORS.length]}
                        />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value) => [`${value}%`, 'Percentage']}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            )}
          </Card.Content>
        </Card>

        <Card>
          <Card.Header>
            <Card.Title>Order Status</Card.Title>
            <Card.Description>
              Current order status distribution
            </Card.Description>
          </Card.Header>
          <Card.Content>
            {isOrderStatusLoading ? (
              <Skeleton className="h-[300px] w-full" />
            ) : orderStatusError ? (
              renderError(orderStatusError)
            ) : (
              <div className="h-[300px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={orderStatus}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) =>
                        `${name}: ${((percent || 0) * 100).toFixed(0)}%`
                      }
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {orderStatus?.map((_, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={COLORS[index % COLORS.length]}
                        />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value) => [`${value}%`, 'Percentage']}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            )}
          </Card.Content>
        </Card>
      </div>

      <Card>
        <Card.Header>
          <Card.Title>Top Selling Products</Card.Title>
          <Card.Description>Best performing products by sales</Card.Description>
        </Card.Header>
        <Card.Content>
          {isTopProductsLoading ? (
            <Skeleton className="h-[300px] w-full" />
          ) : topProductsError ? (
            renderError(topProductsError)
          ) : (
            <div className="h-[300px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  layout="vertical"
                  data={topProducts}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 100,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="name" type="category" />
                  <Tooltip />
                  <Bar dataKey="sales" fill="#2563eb" radius={[0, 4, 4, 0]}>
                    {topProducts?.map((_, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>
          )}
        </Card.Content>
      </Card>
    </div>
  );
};

const StatCard = ({
  title,
  value,
  description,
  isLoading,
  error,
}: {
  title: string;
  value: string;
  description: string;
  isLoading: boolean;
  error?: unknown;
}) => {
  return (
    <Card className="!gap-2">
      <Card.Header className="!mb-0 flex flex-row items-center justify-between">
        <Card.Title className="font-medium text-sm">{title}</Card.Title>
      </Card.Header>
      <Card.Content>
        {isLoading ? (
          <Skeleton className="h-8 w-full" />
        ) : error ? (
          <p className="text-destructive text-xs">Failed to load data</p>
        ) : (
          <div className="flex flex-col space-y-1">
            <h4 className="font-bold text-2xl">{value}</h4>
            <p className="text-muted-foreground text-xs">{description}</p>
          </div>
        )}
      </Card.Content>
    </Card>
  );
};

export default Page;
