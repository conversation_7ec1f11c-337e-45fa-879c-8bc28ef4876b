'use client';

import { CityTable } from '@/components/cities';
import { CountryTable } from '@/components/countries';
import { DistrictTable } from '@/components/districts';
import { LocationTable } from '@/components/locations';
import { useModal } from '@/components/modal';
import {
  useGetCities,
  useGetCountries,
  useGetDistricts,
  useGetLocations,
} from '@muraadso/api/hooks';
import { PlusIcon } from '@muraadso/icons';
import { Button, Separator, Tabs } from '@muraadso/ui';
import { useQueryState } from 'nuqs';
import React, { Suspense } from 'react';

const Page = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PageContent />
    </Suspense>
  );
};

const PageContent = () => {
  const [activeTab, setActiveTab] = useQueryState('tab', {
    defaultValue: 'countries',
  });
  const modal = useModal();

  const getTabTitle = () => {
    switch (activeTab) {
      case 'countries':
        return 'Countries';
      case 'cities':
        return 'Cities';
      case 'districts':
        return 'Districts';
      case 'showrooms':
        return 'Showrooms';
      default:
        return 'Countries';
    }
  };

  const getNewButtonText = () => {
    switch (activeTab) {
      case 'countries':
        return 'New Country';
      case 'cities':
        return 'New City';
      case 'districts':
        return 'New District';
      case 'showrooms':
        return 'New Showroom';
      default:
        return 'New Country';
    }
  };

  const handleNewClick = () => {
    switch (activeTab) {
      case 'countries':
        modal.open('countries');
        break;
      case 'cities':
        modal.open('cities');
        break;
      case 'districts':
        modal.open('districts');
        break;
      case 'showrooms':
        modal.open('locations');
        break;
    }
  };

  return (
    <div className="flex w-full flex-col px-12 py-2">
      <Separator />

      <div className="my-4 flex w-full items-center justify-between">
        <div className="font-medium text-xl">{getTabTitle()}</div>
        <Button onClick={handleNewClick} className="gap-x-2">
          <PlusIcon className="size-4.5" />
          {getNewButtonText()}
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <Tabs.List className="mb-6">
          <Tabs.Trigger value="countries">Countries</Tabs.Trigger>
          <Tabs.Trigger value="cities">Cities</Tabs.Trigger>
          <Tabs.Trigger value="districts">Districts</Tabs.Trigger>
          <Tabs.Trigger value="showrooms">Showrooms</Tabs.Trigger>
        </Tabs.List>

        <Tabs.Content value="countries">
          <Suspense fallback={<div>Loading...</div>}>
            <CountriesData />
          </Suspense>
        </Tabs.Content>

        <Tabs.Content value="cities">
          <Suspense fallback={<div>Loading...</div>}>
            <CitiesData />
          </Suspense>
        </Tabs.Content>

        <Tabs.Content value="districts">
          <Suspense fallback={<div>Loading...</div>}>
            <DistrictsData />
          </Suspense>
        </Tabs.Content>

        <Tabs.Content value="showrooms">
          <Suspense fallback={<div>Loading...</div>}>
            <ShowroomsData />
          </Suspense>
        </Tabs.Content>
      </Tabs>
    </div>
  );
};

const CountriesData = () => {
  const { isPending, countries } = useGetCountries();

  return (
    <>
      {isPending && <div>Loading...</div>}
      {countries && <CountryTable countries={countries} />}

      {!isPending && countries?.length === 0 && (
        <div className="py-10 text-center">
          <p className="text-muted-foreground">No countries found</p>
        </div>
      )}
    </>
  );
};

const CitiesData = () => {
  const { isPending, cities } = useGetCities();

  return (
    <>
      {isPending && <div>Loading...</div>}
      {cities && <CityTable cities={cities} />}

      {!isPending && cities?.length === 0 && (
        <div className="py-10 text-center">
          <p className="text-muted-foreground">No cities found</p>
        </div>
      )}
    </>
  );
};

const DistrictsData = () => {
  const { isPending, districts } = useGetDistricts();

  return (
    <>
      {isPending && <div>Loading...</div>}
      {districts && <DistrictTable districts={districts} />}

      {!isPending && districts?.length === 0 && (
        <div className="py-10 text-center">
          <p className="text-muted-foreground">No districts found</p>
        </div>
      )}
    </>
  );
};

const ShowroomsData = () => {
  const { isPending, locations } = useGetLocations();

  return (
    <>
      {isPending && <div>Loading...</div>}
      {locations && <LocationTable locations={locations} />}

      {!isPending && locations?.length === 0 && (
        <div className="py-10 text-center">
          <p className="text-muted-foreground">No showrooms found</p>
        </div>
      )}
    </>
  );
};

export default Page;
