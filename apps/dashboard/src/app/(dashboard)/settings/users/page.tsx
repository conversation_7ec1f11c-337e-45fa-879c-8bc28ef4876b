'use client';

import { DataTable } from '@/components/data-table';
import { useModal } from '@/components/modal';
import { useDeleteSysop, useGetSysops } from '@muraadso/api/hooks';
import { MoreHorizontalIcon, PlusIcon } from '@muraadso/icons';
import type { Sysop } from '@muraadso/models/sysops';
import { Avatar, Button, Checkbox, DropdownMenu } from '@muraadso/ui';
import { cn } from '@muraadso/ui/utils';
import type { ColumnDef } from '@tanstack/react-table';
import type React from 'react';

const Page = () => {
  const { sysops, isPending } = useGetSysops();
  const { mutateAsync: deleteSysop } = useDeleteSysop();

  const { open, close } = useModal();

  const columns: ColumnDef<Sysop>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
    },
    {
      id: 'name',
      header: 'Display Name',
      cell: ({ row }) => (
        <div className="flex items-center gap-4">
          <Avatar>
            <Avatar.Image src={row.original.image || undefined} alt="" />
            <Avatar.Fallback>
              {row.original?.name?.substring(0, 2).toUpperCase() || '-'}
            </Avatar.Fallback>
          </Avatar>
          <span className="capitalize">{row.original?.name || '-'}</span>
        </div>
      ),
    },
    {
      id: 'email',
      header: 'Email',
      cell: ({ row }) => (
        <div className="lowercase">{row.original?.user?.email || '-'}</div>
      ),
    },
    {
      id: 'phone',
      header: 'Phone',
      cell: ({ row }) => (
        <div className="lowercase">{row.original?.user?.phone || '-'}</div>
      ),
    },
    {
      id: 'role',
      header: 'Roles',
      cell: ({ row }) => (
        <div className="">
          {/*{row.original.roles.map((it) => it.name).join(', ')}*/}
        </div>
      ),
    },
    {
      id: 'location',
      header: 'Location',
      cell: ({ row }) => (
        <div className="">{row.original?.location?.name || 'All'}</div>
      ),
    },
    {
      id: 'status',
      header: 'Status',
      cell: ({ row }) => (
        <div
          data-state={
            row.original.user.isBanned
              ? 'banned'
              : !row.original.user.emailVerifiedAt
                ? 'pending'
                : 'active'
          }
          className={cn(
            'inline-flex h-6 items-center justify-center gap-2 whitespace-nowrap rounded-md bg-background px-2 font-medium text-muted-foreground text-xs ring-1 ring-border ring-inset has-[>.dot]:gap-1.5',
            'data-[state=active]:text-green-500 data-[state=banned]:text-red-500 data-[state=pending]:text-yellow-500',
          )}
        >
          {row.original.user.isBanned
            ? 'Banned'
            : !row.original.user.emailVerifiedAt
              ? 'Pending'
              : 'Active'}
        </div>
      ),
    },
    {
      id: 'actions',
      enableHiding: false,
      cell: ({ row }) => {
        const sysop = row.original;
        return (
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="ghost" className="h-8 w-8">
                <span className="sr-only">Open menu</span>
                <MoreHorizontalIcon />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content align="end">
              <DropdownMenu.Label>Actions</DropdownMenu.Label>
              <DropdownMenu.Item>Ban User</DropdownMenu.Item>
              <DropdownMenu.Item onClick={() => deleteSysop(sysop.id)}>
                Delete
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="flex w-full flex-col px-12 py-4">
      <div className="mb-6 flex w-full justify-start">
        <Button className="gap-x-2" onClick={() => open('sysops')}>
          <PlusIcon className="size-4.5" />
          Invite User
        </Button>
      </div>

      {isPending && <div>Loading...</div>}
      {sysops && <DataTable columns={columns} data={sysops} />}
    </div>
  );
};

export default Page;
