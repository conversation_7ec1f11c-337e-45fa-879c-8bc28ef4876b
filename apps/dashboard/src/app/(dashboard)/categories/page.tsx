'use client';

import { DataTable } from '@/components/data-table';
import { useModal } from '@/components/modal';
import { getImageUrl } from '@/lib/utils';
import { useDeleteCategory, useGetCategories } from '@muraadso/api/hooks';
import { MoreHorizontalIcon, PlusIcon } from '@muraadso/icons';
import type { Category } from '@muraadso/models/categories';
import { Avatar, Button, Checkbox, DropdownMenu } from '@muraadso/ui';
import type { ColumnDef } from '@tanstack/react-table';
import { formatDate } from 'date-fns';
import type React from 'react';

const Page = () => {
  const { categories, isPending } = useGetCategories();
  const { mutateAsync: deleteCategory } = useDeleteCategory();

  const { open } = useModal();

  const columns: ColumnDef<Category>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
    },
    {
      accessorKey: 'image',
      header: 'Image',
      cell: ({ row }) => (
        <Avatar className="size-8 rounded-sm">
          <Avatar.Image
            className="object-cover"
            src={getImageUrl(row.getValue('image'))}
            alt=""
          />
          <Avatar.Fallback className="rounded-sm">
            {row.original?.name?.substring(0, 2).toUpperCase() ?? '-'}
          </Avatar.Fallback>
        </Avatar>
      ),
    },
    {
      accessorKey: 'name',
      header: 'Category',
      cell: ({ row }) => (
        <div className="whitespace-nowrap">{row.original?.name ?? '-'}</div>
      ),
    },
    {
      accessorKey: 'slug',
      header: 'Slug',
      cell: ({ row }) => (
        <div className="whitespace-nowrap">/{row.getValue('slug') ?? '-'}</div>
      ),
    },
    {
      accessorKey: 'description',
      header: 'Description',
      cell: ({ row }) => (
        <div className="line-clamp-1 max-w-sm">
          {row.getValue('description')}
        </div>
      ),
    },
    {
      accessorKey: 'rank',
      header: 'Rank',
      cell: ({ row }) => <div>{row.original?.rank ?? '-'}</div>,
    },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      cell: ({ row }) => (
        <div className="whitespace-nowrap">
          {formatDate(row.original?.createdAt, 'dd MMM, yyyy') ?? '-'}
        </div>
      ),
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      cell: ({ row }) => (
        <div className="whitespace-nowrap">
          {formatDate(row.original?.updatedAt, 'dd MMM, yyyy') ?? '-'}
        </div>
      ),
    },
    {
      id: 'actions',
      enableHiding: false,
      cell: ({ row }) => {
        const category = row.original;
        return (
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontalIcon className="size-5 text-muted-foreground opacity-0 group-hover:opacity-100" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content align="end">
              <DropdownMenu.Label>Actions</DropdownMenu.Label>
              <DropdownMenu.Item onClick={() => open('categories', category)}>
                Edit
              </DropdownMenu.Item>
              <DropdownMenu.Item
                onClick={async () => {
                  try {
                    await deleteCategory(category.id);
                  } catch (_) {}
                }}
              >
                Delete
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="flex w-full flex-col px-12 py-8">
      <div className="mb-6 flex w-full items-center justify-between">
        <div className="font-medium text-xl">Categories</div>
        <Button
          onClick={() => {
            open('categories');
          }}
          className="gap-x-2"
        >
          <PlusIcon className="size-4.5" />
          New Category
        </Button>
      </div>

      {isPending && <div>Loading...</div>}
      {categories && <DataTable columns={columns} data={categories} />}
    </div>
  );
};

export default Page;
