'use client';

import { useAuth } from '@muraadso/api/hooks';
import { notFound, useRouter, useSearchParams } from 'next/navigation';
import type React from 'react';
import { Suspense } from 'react';
import { useEffect } from 'react';

const Page = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PageContent />
    </Suspense>
  );
};

const PageContent = () => {
  const { replace } = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  if (!token) notFound();

  const {
    verify: { mutateAsync: verifyToken, error },
  } = useAuth();

  useEffect(() => {
    const handleVerification = () => {
      try {
        verifyToken({
          type: 'invite',
          token,
        }).then(() => {
          setTimeout(() => replace('/settings/change-password'), 2000);
        });
      } catch (e) {}
    };

    handleVerification();
  }, [token, verifyToken, replace]);

  if (error) return <InvalidToken message={error.message} />;

  return 'Loading...';
};

const InvalidToken: React.FC<{ message: string }> = ({ message }) => {
  return (
    <div className="mx-auto flex h-screen w-full max-w-xs flex-col justify-center py-12">
      <img src="/images/logo.png" alt="sf" className="h-auto w-40" />
      <h1 className="mt-8 text-lg">Error!</h1>
      <p className="text-muted-foreground">{message}</p>
    </div>
  );
};

export default Page;
