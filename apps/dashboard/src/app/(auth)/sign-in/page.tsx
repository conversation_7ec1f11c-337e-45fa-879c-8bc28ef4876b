import { SignInForm } from '@/components/auth/sign-in';
import { Separator } from '@muraadso/ui';
import type { Metadata } from 'next';
import type React from 'react';

export const metadata: Metadata = {
  title: 'Sign In',
  description: 'Sign in to your account to continue.',
};

const Page = async () => {
  return (
    <div className="flex h-screen flex-col justify-center">
      <div className="mx-auto flex w-full max-w-sm flex-col gap-6 rounded-lg bg-background p-8">
        <div className="mb-4 w-40">
          <img src="/images/logo.png" alt="" className="h-auto w-full" />
        </div>
        <div className="flex flex-col gap-2">
          <div className="font-bold text-xl lg:text-2xl">Sign In</div>
          <div className="text-muted-foreground text-sm lg:text-base">
            Enter your credentials to continue
          </div>
        </div>
        <Separator />
        <SignInForm />
      </div>
    </div>
  );
};

export default Page;
