import { env } from '@/config/env';
import { type NextRequest, NextResponse } from 'next/server';

export const middleware = async (req: NextRequest) => {
  const isAuthenticated = await checkAuthenticated(req);
  if (!isAuthenticated) {
    return NextResponse.redirect(new URL('/sign-in', req.nextUrl));
  }

  return NextResponse.next();
};

const checkAuthenticated = async (req: NextRequest) => {
  const cookie = req.cookies.get('session');
  if (!cookie || !cookie.value) return false;

  const { token }: { token: string } = JSON.parse(atob(cookie.value));
  if (!token) return false;

  const meURL = env.NEXT_PUBLIC_API_URL + '/v1/sysops/me';
  const res = await fetch(meURL, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: 'Bearer ' + cookie.value,
    },
  });

  return res.status === 200;
};

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt|images/*|sign-in|invite).*)',
  ],
};
