'use client';

import { usePathname } from 'next/navigation';
import React, { createContext, useContext, type PropsWithChildren } from 'react';

export type OrderPageType = 'orders' | 'platform' | 'quality-check';

interface OrderPageContextValue {
  pageType: OrderPageType;
  canUpdateOrderStatus: boolean;
  canUpdateQchStatus: boolean;
  showQchStatus: boolean;
  showQchProgress: boolean;
}

const OrderPageContext = createContext<OrderPageContextValue | undefined>(
  undefined,
);

export const OrderPageProvider = ({ children }: PropsWithChildren) => {
  const pathname = usePathname();

  const getPageType = (): OrderPageType => {
    if (pathname.includes('/quality-checking')) return 'quality-check';
    if (pathname.includes('/platform')) return 'platform';
    return 'orders';
  };

  const pageType = getPageType();

  const getPagePermissions = (type: OrderPageType): OrderPageContextValue => {
    switch (type) {
      case 'orders':
        return {
          pageType: type,
          canUpdateOrderStatus: true,
          canUpdateQchStatus: false,
          showQchStatus: false,
          showQchProgress: false,
        };
      case 'platform':
        return {
          pageType: type,
          canUpdateOrderStatus: true,
          canUpdateQchStatus: false,
          showQchStatus: true,
          showQchProgress: true,
        };
      case 'quality-check':
        return {
          pageType: type,
          canUpdateOrderStatus: false,
          canUpdateQchStatus: true,
          showQchStatus: true,
          showQchProgress: false,
        };
      default:
        return {
          pageType: 'orders',
          canUpdateOrderStatus: true,
          canUpdateQchStatus: false,
          showQchStatus: false,
          showQchProgress: false,
        };
    }
  };

  const value = getPagePermissions(pageType);

  return (
    <OrderPageContext.Provider value={value}>
      {children}
    </OrderPageContext.Provider>
  );
};

export const useOrderPageContext = () => {
  const context = useContext(OrderPageContext);
  if (context === undefined) {
    throw new Error(
      'useOrderPageContext must be used within an OrderPageProvider',
    );
  }
  return context;
};
