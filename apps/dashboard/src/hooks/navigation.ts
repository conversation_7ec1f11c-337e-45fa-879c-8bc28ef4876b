import { navigationConfig } from '@/config/navigation';
import { NavigationManager } from '@/lib/navigation';
import { usePathname } from 'next/navigation';
import { useMemo } from 'react';

export const useNavigation = () => {
  const pathname = usePathname();

  const navigationManager = useMemo(
    () => new NavigationManager(navigationConfig),
    [],
  );

  return {
    mainMenu: navigationManager.getMainMenu(),
    activePathSubNavItems: navigationManager.getSubNavItems(pathname),
    activePathInfo: navigationManager.getActivePathInfo(pathname),
    getSubNavItems: (path: string) => navigationManager.getSubNavItems(path),
    isActive: (path: string, onlyCurrent = false) => {
      return navigationManager.isPathActive(path, pathname, onlyCurrent);
    },
  };
};
