import type { City } from '@muraadso/models/cities';
import type { Country } from '@muraadso/models/countries';
import type { District } from '@muraadso/models/districts';

export interface StructuredAddress {
  countryId?: string;
  cityId?: string;
  districtId?: string;
  street?: string;
  additionalInfo?: string;
}

export interface AddressFormatOptions {
  countries: Country[];
  cities: City[];
  districts?: District[];
}

export const formatStructuredAddress = (
  address: StructuredAddress,
  options: AddressFormatOptions,
): string => {
  if (!address) return '';

  const parts: string[] = [];

  // Add street
  if (address.street) {
    parts.push(address.street);
  }

  // Add district
  if (address.districtId && options.districts) {
    const district = options.districts.find((d) => d.id === address.districtId);
    if (district) {
      parts.push(district.name);
    }
  }

  // Add city
  if (address.cityId) {
    const city = options.cities.find((c) => c.id === address.cityId);
    if (city) {
      parts.push(city.name);
    }
  }

  // Add country
  if (address.countryId) {
    const country = options.countries.find((c) => c.id === address.countryId);
    if (country) {
      parts.push(country.name);
    }
  }

  // Add additional info
  if (address.additionalInfo) {
    parts.push(`(${address.additionalInfo})`);
  }

  return parts.join(', ');
};

/**
 * Checks if a structured address has the minimum required fields
 */
export const isValidStructuredAddress = (
  address: StructuredAddress,
): boolean => {
  return !!(address.countryId && address.cityId && address.street);
};

/**
 * Creates an empty structured address object
 */
export const createEmptyStructuredAddress = (): StructuredAddress => ({
  countryId: '',
  cityId: '',
  districtId: '',
  street: '',
  additionalInfo: '',
});
