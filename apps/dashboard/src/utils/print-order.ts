import { formatPrice } from '@/lib/utils';
import type { Order } from '@muraadso/models/orders';
import type { Product } from '@muraadso/models/products';

export const printOrder = (order: Order) => {
  // Create a new window for printing
  const printWindow = window.open('', '_blank');

  if (!printWindow) {
    alert('Please allow popups to generate PDF');
    return;
  }

  const html = generateOrderHTML(order);

  printWindow.document.write(html);
  printWindow.document.close();

  // Wait for content to load then print
  printWindow.onload = () => {
    printWindow.print();
    printWindow.close();
  };
};

const generateOrderHTML = (order: Order): string => {
  const totalAmount = order.items.reduce(
    (sum, item) => sum + item.unitPrice * item.quantity,
    0,
  );

  const serviceTypeLabels = {
    retail: 'Retail',
    consignment: 'U iibin',
    buyback: 'Ka iibsad',
    'trade-in': 'Isku bedel',
  };

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Order #${order.trackingId}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 20px;
          color: #333;
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
          border-bottom: 2px solid #eee;
          padding-bottom: 20px;
        }
        .title {
          font-size: 24px;
          font-weight: bold;
          color: #ff0000;
          margin-bottom: 5px;
        }
        .order-title {
          font-size: 18px;
          color: #666;
        }
        .section {
          margin-bottom: 25px;
        }
        .section-title {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 10px;
          color: #374151;
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 5px;
        }
        .info-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 15px;
          margin-bottom: 15px;
        }
        .info-item {
          margin-bottom: 8px;
        }
        .info-label {
          font-weight: bold;
          color: #374151;
        }
        .info-value {
          color: #6b7280;
          margin-top: 2px;
          text-transform: capitalize;
        }
        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-top: 10px;
        }
        .items-table th,
        .items-table td {
          border: 1px solid #e5e7eb;
          padding: 8px;
          text-align: left;
        }
        .items-table th {
          background-color: #f9fafb;
          font-weight: bold;
        }
        .total-row {
          font-weight: bold;
          background-color: #f3f4f6;
        }
        .text-right {
          text-align: right;
        }
        .text-center {
          text-align: center;
        }
        @media print {
          body {
            margin: 0;
            padding: 15px;
          }
          .no-print {
            display: none;
          }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="title">Muraadso</div>
        <div class="order-title">Order #${order.trackingId}</div>
        <div style="color: #6b7280; margin-top: 5px;">
          ${new Date(order.createdAt).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
          })}
        </div>
      </div>

      <div class="section">
        <div class="section-title">Order Information</div>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">Service Type</div>
            <div class="info-value">${serviceTypeLabels[order.serviceType as keyof typeof serviceTypeLabels] || order.serviceType}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Status</div>
            <div class="info-value">${order.status}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Total Amount</div>
            <div class="info-value">${formatPrice(totalAmount)}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Payment Status</div>
            <div class="info-value">${order.isPaid ? 'Paid' : 'Pending'}</div>
          </div>
        </div>
      </div>

      <div class="section">
        <div class="section-title">Customer Information</div>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">Name</div>
            <div class="info-value">${order.customer.name}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Phone</div>
            <div class="info-value">${order.customer.phone || 'Not provided'}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Email</div>
            <div class="info-value">${order.customer.email || 'Not provided'}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Address</div>
            <div class="info-value">${order.address}</div>
          </div>
        </div>
      </div>

      ${
        order.guarantor
          ? `
        <div class="section">
          <div class="section-title">Guarantor Information</div>
          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">Name</div>
              <div class="info-value">${order.guarantor.name || 'Not provided'}</div>
            </div>
            <div class="info-item">
              <div class="info-label">Phone</div>
              <div class="info-value">${order.guarantor.phone || 'Not provided'}</div>
            </div>
            <div class="info-item">
              <div class="info-label">Email</div>
              <div class="info-value">${order.guarantor.email || 'Not provided'}</div>
            </div>
            <div class="info-item">
              <div class="info-label">National ID</div>
              <div class="info-value">${order.guarantor.nationalId || 'Not provided'}</div>
            </div>
          </div>
        </div>
      `
          : ''
      }

      <div class="section">
        <div class="section-title">Order Items</div>
        <table class="items-table">
          <thead>
            <tr>
              <th>Item</th>
              ${order.serviceType !== 'retail' ? '<th>IMEI/Serial</th>' : ''}
              <th class="text-center">Qty</th>
              <th class="text-right">Unit Price</th>
              <th class="text-right">Total</th>
            </tr>
          </thead>
          <tbody>
            ${order.items
              .map((item) => {
                const product = item.productSnapshot as Product;
                const isLaptop = product?.category?.slug === 'laptops';
                const identifier = isLaptop
                  ? item.metadata?.serialNumber
                  : item.metadata?.imei;

                return `
                <tr>
                  <td>${product?.brand?.name || ''} ${product?.name || ''} / ${item.variantSnapshot?.name || ''}</td>
                  ${order.serviceType !== 'retail' ? `<td>${identifier || '-'}</td>` : ''}
                  <td class="text-center">x${item.quantity}</td>
                  <td class="text-right">${formatPrice(item.unitPrice)}</td>
                  <td class="text-right">${formatPrice(item.unitPrice * item.quantity)}</td>
                </tr>
              `;
              })
              .join('')}
            <tr class="total-row">
              <td colspan="${order.serviceType !== 'retail' ? '4' : '3'}" class="text-right">Total Amount:</td>
              <td class="text-right">${formatPrice(totalAmount)}</td>
            </tr>
          </tbody>
        </table>
      </div>

      ${
        order.note
          ? `
        <div class="section">
          <div class="section-title">Notes</div>
          <div class="info-value">${order.note}</div>
        </div>
      `
          : ''
      }

      <div style="margin-top: 40px; text-align: center; color: #6b7280; font-size: 12px;">
        Generated on ${new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
        })}
      </div>
    </body>
    </html>
  `;
};
