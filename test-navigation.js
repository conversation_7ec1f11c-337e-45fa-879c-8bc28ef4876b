// Simple test to verify navigation logic
const navigationConfig = {
  mainMenu: [
    {
      title: 'Overview',
      items: [
        {
          title: 'Dashboard',
          description: 'Overview of your business metrics and insights',
          path: '/',
        },
      ],
    },
    {
      title: 'Management',
      items: [
        {
          title: 'Orders',
          description: 'Manage and track your orders',
          path: '/orders',
        },
        {
          title: 'Platform',
          description: 'Manage consignment, buyback, and trade-in orders',
          path: '/platform',
        },
        {
          title: 'Quality Check',
          description: 'Assess and approve/reject platform orders',
          path: '/quality-checking',
        },
        {
          title: 'Customers',
          description: 'Manage customers and their information',
          path: '/customers',
        },
        {
          title: 'Users',
          description: 'Manage team members and their roles',
          path: '/users',
        },
      ],
    },
    {
      title: 'Products',
      items: [
        {
          title: 'Items',
          description: 'Manage products and their details',
          path: '/products',
        },
        {
          title: 'Categories',
          description: 'Organize products into categories',
          path: '/categories',
        },
        {
          title: 'Brands',
          description: 'Manage product brands and manufacturers',
          path: '/brands',
        },
        {
          title: 'Inventory',
          description: 'Track stock levels and locations',
          path: '/inventory',
        },
      ],
    },
  ],
};

function getActivePathInfo(currentPath) {
  // Collect all navigation items in a flat array
  const allItems = [];
  for (const menu of navigationConfig.mainMenu) {
    allItems.push(...menu.items);
  }
  
  // First try exact match
  const exactMatch = allItems.find(item => item.path === currentPath);
  if (exactMatch) {
    return exactMatch;
  }
  
  // Then try prefix match for non-root paths, prioritizing longer matches
  if (currentPath !== '/') {
    const prefixMatches = allItems
      .filter(item => item.path !== '/' && currentPath.startsWith(item.path))
      .sort((a, b) => b.path.length - a.path.length); // Sort by path length descending
    
    if (prefixMatches.length > 0) {
      return prefixMatches[0];
    }
  }
  
  // Default to dashboard
  return navigationConfig.mainMenu[0]?.items[0] ?? {
    title: 'Dashboard',
    description: 'Overview of your business metrics and insights',
    path: '/',
  };
}

// Test cases
const testPaths = [
  '/',
  '/orders',
  '/platform',
  '/quality-checking',
  '/customers',
  '/users',
  '/products',
  '/categories',
  '/brands',
  '/inventory',
  '/settings',
  '/unknown-path'
];

console.log('Testing navigation path matching:');
testPaths.forEach(path => {
  const result = getActivePathInfo(path);
  console.log(`${path} -> ${result.title} (${result.description})`);
});
